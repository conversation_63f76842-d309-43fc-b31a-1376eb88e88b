package cn.mlamp.insightflow.cms.controller.dam;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.model.query.DamClassifyAssetBatchRequest;
import cn.mlamp.insightflow.cms.model.query.DamClassifyAssetRequest;
import cn.mlamp.insightflow.cms.model.query.DamClassifyQuery;
import cn.mlamp.insightflow.cms.model.query.DamClassifyRequest;
import cn.mlamp.insightflow.cms.model.vo.DamAssetClassifyVO;
import cn.mlamp.insightflow.cms.model.vo.DamClassifyVO;
import cn.mlamp.insightflow.cms.service.dam.IDamClassifyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * DAM分类控制器
 */
@RestController
@RequestMapping("/dam/classify")
@RequiredArgsConstructor
@Tag(name = "素材库分类管理接口")
public class DamClassifyController {


    private final IDamClassifyService damClassifyService;

    /**
     * 获取租户分类列表接口
     */
    @GetMapping("/list")
    @Operation(summary = "获取租户分类列表接口")
    public RespBody<List<DamClassifyVO>> getClassifyList(@Valid DamClassifyQuery query) {
        return RespBody.ok(damClassifyService.getClassifyList(query));
    }

    /**
     * 添加租户分类接口
     */
    @PostMapping("/add")
    @Operation(summary = "添加租户分类接口")
    public RespBody<Integer> addClassify(@RequestBody @Valid DamClassifyRequest damClassifyRequest) {
        return RespBody.ok(damClassifyService.addClassify(damClassifyRequest));
    }

    /**
     * 删除租户分类接口
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除租户分类接口")
    public RespBody<Boolean> deleteClassify(@PathVariable("id") Integer id) {
        return RespBody.ok(damClassifyService.deleteClassify(id));
    }

    /**
     * 修改租户分类接口
     */
    @PutMapping("/update")
    @Operation(summary = "修改租户分类接口")
    public RespBody<Boolean> updateClassify(@RequestBody @Valid DamClassifyRequest damClassifyRequest) {
        return RespBody.ok(damClassifyService.updateClassify(damClassifyRequest));
    }


    /**
     * 素材批量添加分类接口
     */
    @PostMapping("asset/add")
    @Operation(summary = "单个素材替换分类接口")
    public RespBody<Boolean> addClassify(@RequestBody DamClassifyAssetRequest damClassifyAssetRequest) {
        return RespBody.ok(damClassifyService.addClassify(damClassifyAssetRequest));
    }


    /**
     * 素材批量添加分类接口
     */
    @PostMapping("asset/batchAdd")
    @Operation(summary = "素材批量追加分类接口")
    public RespBody<Boolean> addBatchClassify(@RequestBody DamClassifyAssetBatchRequest damClassifyAssetBatchRequest) {
        return RespBody.ok(damClassifyService.addBatchClassify(damClassifyAssetBatchRequest));
    }


    /**
     * 获取素材包含分类列表接口
     */
    @GetMapping("asset/list")
    @Operation(summary = "获取素材包含分类列表接口")
    public RespBody<List<DamAssetClassifyVO>> getAssetClassifyList(DamClassifyAssetBatchRequest request) {
        return RespBody.ok(damClassifyService.getAssetClassifyList(request));
    }


}