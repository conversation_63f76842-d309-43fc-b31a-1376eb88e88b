package cn.mlamp.insightflow.cms.graph.events;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * Agent Graph消息事件 用于传递节点执行过程中的消息信息
 */
@Getter
public class AgentGraphStartedEvent extends AbstractAgentGraphEvent<Map<String, Object>> {

    private final EventType event = EventType.WORKFLOW_STARTED;

    private final Map<String, Object> metadata;

    public AgentGraphStartedEvent(
            String conversationId,
            String messageId
    ) {
        this(conversationId, messageId, new HashMap<>());
    }

    public AgentGraphStartedEvent(
            String conversationId,
            String messageId,
            Map<String, Object> metadata
    ) {
        super(conversationId, messageId);
        this.metadata = new HashMap<>(metadata);
    }

    @Override
    protected Map<String, Object> getEventData() {
        return Map.of();
    }

}
