{{- if and (.Capabilities.APIVersions.Has "policy/v1beta1/PodSecurityPolicy") .Values.testFramework.enabled .Values.rbac.pspEnabled }}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "grafana.fullname" . }}-test
  namespace: {{ include "grafana.namespace" . }}
  annotations:
    "helm.sh/hook": test-success
    "helm.sh/hook-delete-policy": "before-hook-creation,hook-succeeded"
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
rules:
  - apiGroups:      ['policy']
    resources:      ['podsecuritypolicies']
    verbs:          ['use']
    resourceNames:  [{{ include "grafana.fullname" . }}-test]
{{- end }}
