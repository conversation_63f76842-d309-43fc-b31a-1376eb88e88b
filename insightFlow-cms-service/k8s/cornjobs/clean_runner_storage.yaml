apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: clean-runner-storage
  labels:
    app: clean-runner-storage
spec:
  jobTemplate:
    metadata:
      name: clean-runner-storage
      labels:
        app: clean-runner-storage
    spec:
      template:
        metadata:
          name: clean-runner-storage
          labels:
            app: clean-runner-storage
        spec:
          restartPolicy: Never
          containers:
            - name: cleaner
              image: hub.intra.mlamp.cn/public/docker-jdk-mvn-jdk17-amd64
              command:
                - bash
                - -c
                - "rm -r /storage/ai-pc || echo 'ok'"
              volumeMounts:
                - mountPath: /storage
                  name: storage
          volumes:
            - name: storage
              persistentVolumeClaim:
                claimName: insight-flow-cms-git-runner-cache
  schedule: "0 0 * * *"  # 每天午夜执行