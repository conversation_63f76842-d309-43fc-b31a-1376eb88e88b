package cn.mlamp.insightflow.cms.entity.chat;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 会话表
 */
@TableName(value = "conversation", autoResultMap = true)
@Data
public class Conversation {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "conversation_id")
    private String conversationId;

    @TableField(value = "tenant_id")
    private Integer tenantId;

    @TableField(value = "user_id")
    private Integer userId;

    @TableField(value = "name")
    private String name;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "update_time")
    private Date updateTime;

}
