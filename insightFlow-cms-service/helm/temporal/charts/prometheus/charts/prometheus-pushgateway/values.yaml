# Default values for prometheus-pushgateway.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# Provide a name in place of prometheus-pushgateway for `app:` labels
nameOverride: ""

# Provide a name to substitute for the full names of resources
fullnameOverride: ""

# Provide a namespace to substitude for the namespace on resources
namespaceOverride: ""

image:
  repository: quay.io/prometheus/pushgateway
  # if not set appVersion field from Chart.yaml is used
  tag: ""
  pullPolicy: IfNotPresent

# Optional pod imagePullSecrets
imagePullSecrets: []

service:
  type: ClusterIP
  port: 9091
  targetPort: 9091
  # nodePort: 32100
  portName: http

  # Optional - Can be used for headless if value is "None"
  clusterIP: ""

  ipDualStack:
    enabled: false
    ipFamilies: ["IPv6", "IPv4"]
    ipFamilyPolicy: "PreferDualStack"

  loadBalancerIP: ""
  loadBalancerSourceRanges: []

# Whether to automatically mount a service account token into the pod
automountServiceAccountToken: true

# Optional pod annotations
podAnnotations: {}

# Optional pod labels
podLabels: {}

# Optional service annotations
serviceAnnotations: {}

# Optional service labels
serviceLabels: {}

# Optional serviceAccount labels
serviceAccountLabels: {}

# Optional persistentVolume labels
persistentVolumeLabels: {}

# Optional additional environment variables
extraVars: []

## Additional pushgateway container arguments
##
## example:
## extraArgs:
##  - --persistence.file=/data/pushgateway.data
##  - --persistence.interval=5m
extraArgs: []

## Additional InitContainers to initialize the pod
##
extraInitContainers: []

# Optional additional containers (sidecar)
extraContainers: []
  # - name: oAuth2-proxy
  #   args:
  #     - -https-address=:9092
  #     - -upstream=http://localhost:9091
  #     - -skip-auth-regex=^/metrics
  #     - -openshift-delegate-urls={"/":{"group":"monitoring.coreos.com","resource":"prometheuses","verb":"get"}}
  #   image: openshift/oauth-proxy:v1.1.0
  #   ports:
  #       - containerPort: 9092
  #         name: proxy
  #   resources:
  #       limits:
  #         memory: 16Mi
  #       requests:
  #         memory: 4Mi
  #         cpu: 20m
  #   volumeMounts:
  #     - mountPath: /etc/prometheus/secrets/pushgateway-tls
  #       name: secret-pushgateway-tls

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 200m
  #   memory: 50Mi
  # requests:
  #   cpu: 100m
  #   memory: 30Mi

# -- Sets web configuration
# To enable basic authentication, provide basicAuthUsers as a map
webConfiguration: {}
  # basicAuthUsers:
  #   username: password

liveness:
  enabled: true
  probe:
    httpGet:
      path: /-/healthy
      port: 9091
    initialDelaySeconds: 10
    timeoutSeconds: 10

readiness:
  enabled: true
  probe:
    httpGet:
      path: /-/ready
      port: 9091
    initialDelaySeconds: 10
    timeoutSeconds: 10

serviceAccount:
  # Specifies whether a ServiceAccount should be created
  create: true
  # The name of the ServiceAccount to use.
  # If not set and create is true, a name is generated using the fullname template
  name:

## Configure ingress resource that allow you to access the
## pushgateway installation. Set up the URL
## ref: http://kubernetes.io/docs/user-guide/ingress/
##
ingress:
  ## Enable Ingress.
  ##
  enabled: false
  # AWS ALB requires path of /*
  className: ""
  path: /
  pathType: ImplementationSpecific

  ## Extra paths to prepend to every host configuration. This is useful when working with annotation based services.
  extraPaths: []
  # - path: /*
  #   backend:
  #     serviceName: ssl-redirect
  #     servicePort: use-annotation

  ## Annotations.
  ##
  # annotations:
  #   kubernetes.io/ingress.class: nginx
  #   kubernetes.io/tls-acme: 'true'

  ## Hostnames.
  ## Must be provided if Ingress is enabled.
  ##
  # hosts:
  #   - pushgateway.domain.com

  ## TLS configuration.
  ## Secrets must be manually created in the namespace.
  ##
  # tls:
  #   - secretName: pushgateway-tls
  #     hosts:
  #       - pushgateway.domain.com

tolerations: []
  # - effect: NoSchedule
  #   operator: Exists

## Node labels for pushgateway pod assignment
## Ref: https://kubernetes.io/docs/user-guide/node-selection/
##
nodeSelector: {}

replicaCount: 1

hostAliases: []
  # - ip: "127.0.0.1"
  #   hostnames:
  #   - "foo.local"
  #   - "bar.local"
  # - ip: "********"
  #   hostnames:
  #   - "foo.remote"
  #   - "bar.remote"

## When running more than one replica alongside with persistence, different volumes are needed
## per replica, since sharing a `persistence.file` across replicas does not keep metrics synced.
## For this purpose, you can enable the `runAsStatefulSet` to deploy the pushgateway as a
## StatefulSet instead of as a Deployment.
runAsStatefulSet: false

## Security context to be added to push-gateway pods
##
securityContext:
  fsGroup: 65534
  runAsUser: 65534
  runAsNonRoot: true

## Security context to be added to push-gateway containers
## Having a separate variable as securityContext differs for pods and containers.
containerSecurityContext: {}
#  allowPrivilegeEscalation: false
#  readOnlyRootFilesystem: true
#  runAsUser: 65534
#  runAsNonRoot: true

## Affinity for pod assignment
## Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
affinity: {}

## Pod anti-affinity can prevent the scheduler from placing pushgateway replicas on the same node.
## The value "soft" means that the scheduler should *prefer* to not schedule two replica pods onto the same node but no guarantee is provided.
## The value "hard" means that the scheduler is *required* to not schedule two replica pods onto the same node.
## The default value "" will disable pod anti-affinity so that no anti-affinity rules will be configured (unless set in `affinity`).
##
podAntiAffinity: ""

## If anti-affinity is enabled sets the topologyKey to use for anti-affinity.
## This can be changed to, for example, failure-domain.beta.kubernetes.io/zone
##
podAntiAffinityTopologyKey: kubernetes.io/hostname

## Topology spread constraints for pods
## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/
topologySpreadConstraints: []

# Enable this if you're using https://github.com/coreos/prometheus-operator
serviceMonitor:
  enabled: false
  namespace: monitoring

  # telemetryPath: HTTP resource path from which to fetch metrics.
  # Telemetry path, default /metrics, has to be prefixed accordingly if pushgateway sets a route prefix at start-up.
  #
  telemetryPath: "/metrics"

  # Fallback to the prometheus default unless specified
  # interval: 10s

  ## scheme: HTTP scheme to use for scraping. Can be used with `tlsConfig` for example if using istio mTLS.
  # scheme: ""

  ## tlsConfig: TLS configuration to use when scraping the endpoint. For example if using istio mTLS.
  ## Of type: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#tlsconfig
  # tlsConfig: {}

  # bearerTokenFile:
  # Fallback to the prometheus default unless specified
  # scrapeTimeout: 30s

  ## Used to pass Labels that are used by the Prometheus installed in your cluster to select Service Monitors to work with
  ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#prometheusspec
  additionalLabels: {}

  # Retain the job and instance labels of the metrics pushed to the Pushgateway
  # [Scraping Pushgateway](https://github.com/prometheus/pushgateway#configure-the-pushgateway-as-a-target-to-scrape)
  honorLabels: true

  ## Metric relabel configs to apply to samples before ingestion.
  ## [Metric Relabeling](https://prometheus.io/docs/prometheus/latest/configuration/configuration/#metric_relabel_configs)
  metricRelabelings: []
  # - action: keep
  #   regex: 'kube_(daemonset|deployment|pod|namespace|node|statefulset).+'
  #   sourceLabels: [__name__]

  ## Relabel configs to apply to samples before ingestion.
  ## [Relabeling](https://prometheus.io/docs/prometheus/latest/configuration/configuration/#relabel_config)
  relabelings: []
  # - sourceLabels: [__meta_kubernetes_pod_node_name]
  #   separator: ;
  #   regex: ^(.*)$
  #   targetLabel: nodename
  #   replacement: $1
  #   action: replace

# The values to set in the PodDisruptionBudget spec (minAvailable/maxUnavailable)
# If not set then a PodDisruptionBudget will not be created
podDisruptionBudget: {}

priorityClassName:

# Deployment Strategy type
strategy:
  type: Recreate

persistentVolume:
  ## If true, pushgateway will create/use a Persistent Volume Claim
  ## If false, use emptyDir
  ##
  enabled: false

  ## pushgateway data Persistent Volume access modes
  ## Must match those of existing PV or dynamic provisioner
  ## Ref: http://kubernetes.io/docs/user-guide/persistent-volumes/
  ##
  accessModes:
    - ReadWriteOnce

  ## pushgateway data Persistent Volume Claim annotations
  ##
  annotations: {}

  ## pushgateway data Persistent Volume existing claim name
  ## Requires pushgateway.persistentVolume.enabled: true
  ## If defined, PVC must be created manually before volume will be bound
  existingClaim: ""

  ## pushgateway data Persistent Volume mount root path
  ##
  mountPath: /data

  ## pushgateway data Persistent Volume size
  ##
  size: 2Gi

  ## pushgateway data Persistent Volume Storage Class
  ## If defined, storageClassName: <storageClass>
  ## If set to "-", storageClassName: "", which disables dynamic provisioning
  ## If undefined (the default) or set to null, no storageClassName spec is
  ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
  ##   GKE, AWS & OpenStack)
  ##
  # storageClass: "-"

  ## Subdirectory of pushgateway data Persistent Volume to mount
  ## Useful if the volume's root directory is not empty
  ##
  subPath: ""

extraVolumes: []
  # - name: extra
  #   emptyDir: {}
extraVolumeMounts: []
  # - name: extra
  #   mountPath: /usr/share/extras
  #   readOnly: true

# Configuration for clusters with restrictive network policies in place:
# - allowAll allows access to the PushGateway from any namespace
# - customSelector is a list of pod/namespaceSelectors to allow access from
# These options are mutually exclusive and the latter will take precedence.
networkPolicy: {}
  # allowAll: true
  # customSelectors:
  #   - namespaceSelector:
  #       matchLabels:
  #         type: admin
  #   - podSelector:
  #       matchLabels:
  #         app: myapp

# Array of extra K8s objects to deploy (evaluated as a template)
# The value can hold an array of strings as well as objects
extraManifests: []
