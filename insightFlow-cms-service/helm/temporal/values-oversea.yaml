imagePullSecrets:
  - name: tencentcloudcr

server:
  image:
    repository: tsg-miaoacms-tcr1.tencentcloudcr.com/cms/temporalio-server
  frontend:
    service:
      type: NodePort
  config:
    persistence:
      default:
        driver: "sql"
        sql:
          host: **********
          port: 3306
          user: temporal
          password: S5JCh8D1juo2bmvb
          database: temporal
      visibility:
        driver: "sql"
        sql:
          host: **********
          port: 3306
          user: temporal
          password: S5JCh8D1juo2bmvb
          database: temporal_visibility

admintools:
  image:
    repository: tsg-miaoacms-tcr1.tencentcloudcr.com/cms/temporalio-admin-tools

web:
  image:
    repository: tsg-miaoacms-tcr1.tencentcloudcr.com/cms/temporalio-ui
  service:
    type: NodePort

elasticsearch:
  enabled: false

cassandra:
  enabled: false

mysql:
  enabled: true