apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "dify.fullname" . }}-external-models-config
data:
  claude-opus-4.yaml: |-
    model: claude-opus-4
    label:
      en_US: Claude Opus 4
    model_type: llm
    features:
      - agent-thought
      - vision
    model_properties:
      mode: chat
      context_size: 200000
    parameter_rules:
      - name: temperature
        use_template: temperature
      - name: top_p
        use_template: top_p
      - name: top_k
        label:
          zh_Hans: 取样数量
          en_US: Top k
        type: int
        help:
          zh_Hans: 仅从每个后续标记的前 K 个选项中采样。
          en_US: Only sample from the top K options for each subsequent token.
        required: false
      - name: max_tokens
        use_template: max_tokens
        required: true
        default: 8000
        min: 1
        max: 32000
      - name: response_format
        use_template: response_format
    pricing:
      input: '15.00'
      output: '75.00'
      unit: '0.000001'
      currency: USD
  claude-sonnet-4.yaml: |-
    model: claude-sonnet-4
    label:
      en_US: <PERSON> Sonnet 4
    model_type: llm
    features:
      - agent-thought
      - vision
    model_properties:
      mode: chat
      context_size: 200000
    parameter_rules:
      - name: temperature
        use_template: temperature
      - name: top_p
        use_template: top_p
      - name: top_k
        label:
          zh_Hans: 取样数量
          en_US: Top k
        type: int
        help:
          zh_Hans: 仅从每个后续标记的前 K 个选项中采样。
          en_US: Only sample from the top K options for each subsequent token.
        required: false
      - name: max_tokens
        use_template: max_tokens
        required: true
        default: 8000
        min: 1
        max: 64000
      - name: response_format
        use_template: response_format
    pricing:
      input: '3.00'
      output: '15.00'
      unit: '0.000001'
      currency: USD
  claude-3-7-sonnet.yaml: |-
    model: claude-3-7-sonnet
    label:
      en_US: claude-3.7-sonnet
    model_type: llm
    features:
      - agent-thought
      - vision
    model_properties:
      mode: chat
      context_size: 200000
    parameter_rules:
      - name: temperature
        use_template: temperature
      - name: top_p
        use_template: top_p
      - name: top_k
        label:
          zh_Hans: 取样数量
          en_US: Top k
        type: int
        help:
          zh_Hans: 仅从每个后续标记的前 K 个选项中采样。
          en_US: Only sample from the top K options for each subsequent token.
        required: false
      - name: max_tokens
        use_template: max_tokens
        required: true
        default: 8192
        min: 1
        max: 128000
      - name: response_format
        use_template: response_format
    pricing:
      input: '3.00'
      output: '15.00'
      unit: '0.000001'
      currency: USD
  claude-3-5-sonnet.yaml: |-
    model: anthropic/claude-3.5-sonnet
    label:
      en_US: claude-3.5-sonnet
    model_type: llm
    features:
      - agent-thought
      - vision
    model_properties:
      mode: chat
      context_size: 200000
    parameter_rules:
      - name: temperature
        use_template: temperature
      - name: top_p
        use_template: top_p
      - name: top_k
        label:
          zh_Hans: 取样数量
          en_US: Top k
        type: int
        help:
          zh_Hans: 仅从每个后续标记的前 K 个选项中采样。
          en_US: Only sample from the top K options for each subsequent token.
        required: false
      - name: max_tokens
        use_template: max_tokens
        required: true
        default: 8192
        min: 1
        max: 8192
      - name: response_format
        use_template: response_format
    pricing:
      input: '3.00'
      output: '15.00'
      unit: '0.000001'
      currency: USD
  gemini-2.0-flash-001.yaml: |-
    model: gemini-2.0-flash-001
    label:
      en_US: Gemini 2.0 Flash 001
    model_type: llm
    features:
      - agent-thought
      - vision
      - tool-call
      - stream-tool-call
    model_properties:
      mode: chat
      context_size: 1000000
    parameter_rules:
      - name: temperature
        use_template: temperature
        min: 0
        max: 2
        default: 1
      - name: top_p
        use_template: top_p
      - name: top_k
        label:
          zh_Hans: 取样数量
          en_US: Top k
        type: int
        help:
          zh_Hans: 仅从每个后续标记的前 K 个选项中采样。
          en_US: Only sample from the top K options for each subsequent token.
        required: false
      - name: max_tokens
        use_template: max_tokens
        required: true
        default: 8000
        min: 1
        max: 8000
      - name: response_format
        use_template: response_format
    pricing:
      input: '0.10'
      output: '0.40'
      unit: '0.000001'
      currency: USD
  gemini-2.5-flash-preview-04-17.yaml: |-
    model: gemini-2.5-flash-preview-04-17
    label:
      en_US: Gemini 2.5 Flash Preview 0417
    model_type: llm
    features:
      - agent-thought
      - vision
      - tool-call
      - stream-tool-call
    model_properties:
      mode: chat
      context_size: 1000000
    parameter_rules:
      - name: temperature
        use_template: temperature
        min: 0
        max: 2
        default: 1
      - name: top_p
        use_template: top_p
      - name: top_k
        label:
          zh_Hans: 取样数量
          en_US: Top k
        type: int
        help:
          zh_Hans: 仅从每个后续标记的前 K 个选项中采样。
          en_US: Only sample from the top K options for each subsequent token.
        required: false
      - name: max_tokens
        use_template: max_tokens
        required: true
        default: 10000
        min: 1
        max: 66000
      - name: response_format
        use_template: response_format
    pricing:
      input: '0.15'
      output: '0.60'
      unit: '0.000001'
      currency: USD
  gemini-2.5-pro-preview-05-06.yaml: |-
    model: gemini-2.5-pro-preview-05-06
    label:
      en_US: Gemini 2.5 Pro Preview 0506
    model_type: llm
    features:
      - agent-thought
      - vision
      - tool-call
      - stream-tool-call
    model_properties:
      mode: chat
      context_size: 1000000
    parameter_rules:
      - name: temperature
        use_template: temperature
        min: 0
        max: 2
        default: 1
      - name: top_p
        use_template: top_p
      - name: top_k
        label:
          zh_Hans: 取样数量
          en_US: Top k
        type: int
        help:
          zh_Hans: 仅从每个后续标记的前 K 个选项中采样。
          en_US: Only sample from the top K options for each subsequent token.
        required: false
      - name: max_tokens
        use_template: max_tokens
        required: true
        default: 10000
        min: 1
        max: 66000
      - name: response_format
        use_template: response_format
    pricing:
      input: '1.25'
      output: '10.00'
      unit: '0.000001'
      currency: USD
  o3.yaml: |-
    model: o3
    label:
      zh_Hans: o3
      en_US: o3
    model_type: llm
    features:
      - agent-thought
    model_properties:
      mode: chat
      context_size: 200000
    parameter_rules:
      - name: max_tokens
        use_template: max_tokens
        default: 10000
        min: 1
        max: 100000
      - name: response_format
        label:
          zh_Hans: 回复格式
          en_US: response_format
        type: string
        help:
          zh_Hans: 指定模型必须输出的格式
          en_US: specifying the format that the model must output
        required: false
        options:
          - text
          - json_object
    pricing:
      input: '10.00'
      output: '40.00'
      unit: '0.000001'
      currency: USD