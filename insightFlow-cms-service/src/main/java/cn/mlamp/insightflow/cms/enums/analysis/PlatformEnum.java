package cn.mlamp.insightflow.cms.enums.analysis;

import cn.mlamp.insightflow.cms.entity.CmsPullTaskDedupedData;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 视频来源平台，对应 {@link CmsPullTaskDedupedData#getKwSource()} 信息
 *
 * <AUTHOR>
 */
public enum PlatformEnum implements IEnum<String> {

    LOCAL("local"),
    DOUYIN("douyin"),
    KUAISHOU("kuaishou"),
    XIAOHONGSHU("xiaohongshu"),
    BILIBILI("bilibili"),
    WEIBO("weibo"),
    TIKTOK("tiktok"),
    ;
    private final String value;

    PlatformEnum(String value) {
        this.value = value;
    }

    @Override
    public String getValue() {
        return value;
    }

    /**
     * 兼容历史
     *
     * @param value value
     * @return PlatformEnum
     */
    @JsonCreator
    public static PlatformEnum from(String value) {
        // TODO: 兼容历史数据，等待清洗数据
        if (StringUtils.isEmpty(value)) {
            return null;
        } else if ("douyin.com".equals(value)) {
            return DOUYIN;
        } else if ("tiktok.com".equals(value)) {
            return TIKTOK;
        }
        value = value.toLowerCase();
        for (PlatformEnum type : PlatformEnum.values()) {
            if (Objects.equals(type.getValue(), value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown PlatformEnum value: " + value);
    }
}
