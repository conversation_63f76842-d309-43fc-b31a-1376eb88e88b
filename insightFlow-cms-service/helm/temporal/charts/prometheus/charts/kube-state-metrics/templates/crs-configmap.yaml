{{- if .Values.customResourceState.enabled}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "kube-state-metrics.fullname" . }}-customresourcestate-config
  namespace: {{ template "kube-state-metrics.namespace" . }}
  labels:
    {{- include "kube-state-metrics.labels" . | indent 4 }}
  {{- if .Values.annotations }}
  annotations:
    {{ toYaml .Values.annotations | nindent 4 }}
  {{- end }}
data:
  config.yaml: |
    {{- toYaml .Values.customResourceState.config | nindent 4 }}
{{- end }}
