package cn.mlamp.insightflow.cms.auth.tcc.model;

import lombok.Data;

import java.util.Date;

/**
 * 租户BO
 */
@Data
public class Tenant {

    /**
     * 租户ID
     */
    private Integer id;

    /**
     * 租户名称
     */
    private String name;

    /**
     * 租户过期时间
     */
    private Date expireTime;

    /**
     * 最后一次登录时间
     */
    private Date lastLoginTimestamp;

    /**
     * 是否是当前切换租户
     */
    private boolean isCurrent;

    /**
     * 租户角色列表
     */
    private String role;

}
