package cn.mlamp.insightflow.cms.annotation;

import cn.mlamp.insightflow.cms.enums.MemberRightEnum;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 检查是否有会员权益
 */
@Target({ ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface HasMemberRightCheck {
    MemberRightEnum[] rights();
}
