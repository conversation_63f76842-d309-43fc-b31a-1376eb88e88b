apiVersion: apps/v1
kind: Deployment
metadata:
  name: cms-oversea-ng
  namespace: cms-oversea
  labels:
    app: cms-oversea-ng
spec:
  selector:
    matchLabels:
      app: cms-oversea-ng
  template:
    metadata:
      name: cms-oversea-ng
      labels:
        app: cms-oversea-ng
    spec:
      containers:
        - name: nginx
          image: nginx:latest
          ports:
            - containerPort: 80
          volumeMounts:
            - name: config
              mountPath: /etc/nginx/conf.d/cms-oversea.conf
              subPath: cms-oversea.conf
            - name: config
              mountPath: /etc/nginx/conf.d/cms-temporal.conf
              subPath: cms-temporal.conf
            - name: config
              mountPath: /etc/nginx/conf.d/cms-algo.conf
              subPath: cms-algo.conf
      volumes:
        - name: config
          configMap:
            name: cms-oversea-nginx-config
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: cms-oversea-nginx-config
  namespace: cms-oversea
  labels:
    app: cms-oversea-ng
data:
  cms-oversea.conf: |
    server {
        listen 80;
        server_name meowoo.ai;
    
        # 连接到上游服务器的超时时间
        proxy_connect_timeout 60s;
        
        # 从上游服务器读取响应的超时时间
        proxy_read_timeout 600s;
        
        # 向上游服务器发送请求的超时时间
        proxy_send_timeout 600s;
    
        location /api/ {
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
    
            proxy_pass http://insight-flow-cms-api-oversea-srv/;
        }
    
        location /home {
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
    
            proxy_pass http://miaoa-website-ui-overseas.cms-ui:80/;
        }
  
        location /Meow_files/ {
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
    
            proxy_pass http://miaoa-website-ui-overseas.cms-ui:80/Meow_files/;
        }
    
        location / {
          proxy_set_header Host $host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header X-Forwarded-Proto $scheme;
    
          proxy_pass http://cms-ui.cms-ui:80;
        }
    
    }
  cms-temporal.conf: |
    server {
        listen 80;
        server_name temporal-meowoo.mlamp.cn;
    
        location / {
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
    
            proxy_pass http://temporal-web.temporal:8080;
        }
    }
  cms-algo.conf: |
    server {
        listen 80;
        server_name meowoo-celery.mlamp.cn;
    
        location / {
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
    
            proxy_pass http://cms-prod-abroad-llm-customer-svc.algo.svc.cluster.local:5002;
        }
    }
---
apiVersion: v1
kind: Service
metadata:
  name: cms-oversea
  namespace: cms-oversea
  labels:
    app: cms-oversea
spec:
  type: NodePort
  selector:
    app: cms-oversea-ng
  ports:
    - port: 80
      targetPort: 80
      protocol: TCP
---
apiVersion: v1
kind: Service
metadata:
  name: cms-ui
  namespace: cms-ui
  labels:
    app: cms-ui
spec:
  type: ClusterIP
  selector:
    app: insight-flow-cms-ui-overseas
  ports:
    - port: 80
      targetPort: 80
      protocol: TCP
---
apiVersion: v1
kind: Service
metadata:
  name: miaoa-website-ui-overseas
  namespace: cms-ui
  labels:
    app: miaoa-website-ui-overseas
spec:
  type: ClusterIP
  selector:
    app: miaoa-website-ui-overseas
  ports:
    - port: 80
      targetPort: 80
      protocol: TCP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cms-ingress
  namespace: cms-oversea
  annotations:
    kubernetes.io/ingress.internetChargeType: TRAFFIC_POSTPAID_BY_HOUR
    kubernetes.io/ingress.internetMaxBandwidthOut: "1024"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  ingressClassName: qcloud
  tls:
    - hosts:
        - meowoo.ai
      secretName: meowoo-ai-peugvji1
    - hosts:
        - temporal-meowoo.mlamp.cn
      secretName: mlamp-cn-oknob5t4
  rules:
    - host: meowoo.ai
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: cms-oversea
                port:
                  number: 80
    - host: temporal-meowoo.mlamp.cn
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: cms-oversea
                port:
                  number: 80
