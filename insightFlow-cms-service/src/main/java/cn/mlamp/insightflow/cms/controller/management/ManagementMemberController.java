package cn.mlamp.insightflow.cms.controller.management;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.model.query.MemberCreationRequest;
import cn.mlamp.insightflow.cms.model.query.MemberUpdateRequest;
import cn.mlamp.insightflow.cms.model.vo.ManagementMemberDetailVO;
import cn.mlamp.insightflow.cms.model.vo.ManagementMemberListVO;
import cn.mlamp.insightflow.cms.model.vo.ManagementMemberPageVO;
import cn.mlamp.insightflow.cms.service.management.ManagementMemberService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 管理中心-商品（会员）相关接口
 */
@RestController
@RequestMapping("/management/members")
@RequiredArgsConstructor
public class ManagementMemberController {
    private final ManagementMemberService managementMemberService;

    @GetMapping("/page")
    public RespBody<IPage<ManagementMemberPageVO>> pageMembers(@RequestParam(name = "name", required = false) String name,
                                                               @RequestParam("current") int current,
                                                               @RequestParam("pageSize") int pageSize) {
        IPage<ManagementMemberPageVO> vo = managementMemberService.pageMembers(name, current, pageSize);
        return RespBody.ok(vo);
    }

    /**
     * 获取会员商品详情
     *
     * @param id
     * @return
     */
    @GetMapping
    public RespBody<ManagementMemberDetailVO> getMemberDetail(@RequestParam int id) {
        ManagementMemberDetailVO vo = managementMemberService.getMemberDetail(id);
        return RespBody.ok(vo);
    }

    @PostMapping
    public RespBody<ManagementMemberDetailVO> createMember(@RequestBody MemberCreationRequest request) {
        ManagementMemberDetailVO vo = managementMemberService.createMember(request);
        return RespBody.ok(vo);
    }

    @PutMapping
    public RespBody<Void> updateMember(@RequestBody MemberUpdateRequest request) {
        managementMemberService.updateMember(request);
        return RespBody.ok();
    }

    @DeleteMapping
    public RespBody<Void> deleteMember(@RequestParam int id) {
        managementMemberService.deleteMember(id);
        return RespBody.ok();
    }

    /**
     * 没有分页，返回会员商品列表
     *
     * @return
     */
    @GetMapping("/list")
    public RespBody<List<ManagementMemberListVO>> listMembers() {
        List<ManagementMemberListVO> vos = managementMemberService.listMembers();
        return RespBody.ok(vos);
    }
}
