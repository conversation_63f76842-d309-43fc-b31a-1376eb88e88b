package cn.mlamp.insightflow.cms.entity;

import cn.mlamp.insightflow.cms.enums.MemberSourceTypeEnum;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("cms_member")
public class CmsMember {
    @TableId(type = IdType.AUTO)
    private Integer id ;
    private String name;
    /**
     * {@link MemberSourceTypeEnum#getSourceType()}
     */
    private String sourceType;
    private Integer userId;
    private Integer level;
    private String levelName;
    @TableField(value = "is_shown")
    private Integer shown;

    private Integer duration;
    private BigDecimal price;
    private BigDecimal originalPrice;

    // 爆款营销素材分镜拆解浏览，1-无限次，0-无次数
    private Integer hotStoryboard;
    // 黄金3s套路总结浏览，1-无限次，0-无次数
    private Integer gold3sSummary;
    // 视频解析，次数
    private Integer videoAnalysis;
    // 脚本生成，次数
    private Integer script;
    // 素材库专有空间，byte
    private Long damCapacity;
    // 素材ai打标，次数
    private Integer damAiMark;
    // 视频生成时长，秒
    private Long videoDuration;
    // 营销视频合成，1-无限次，0-无次数
    private Integer videoSynthesis;
    // 背景音乐合成，次数
    private Integer videoBgm;
    // 台词文本转语音，次数
    private Integer tts;

    // 数据逻辑删除标记 0:未删除 1:已删除
    private Integer isDeleted;
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime updateTime;
}