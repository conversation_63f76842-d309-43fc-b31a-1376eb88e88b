apiVersion: v1
kind: Service
metadata:
  name: {{ include "insight-flow-cms.service.name" . }}
  labels:
    {{ include "insight-flow-cms.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http

  selector:
    {{ include "insight-flow-cms.selectorLabels" . | nindent 4 }}
    component: api
