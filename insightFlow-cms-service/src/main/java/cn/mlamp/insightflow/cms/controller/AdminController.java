package cn.mlamp.insightflow.cms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.mlamp.insightflow.cms.auth.cms.dto.TtcTenantBaseInfoDTO;
import cn.mlamp.insightflow.cms.auth.cms.service.TtcAdaptor;
import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.common.resp.RespCode;
import cn.mlamp.insightflow.cms.config.AdminConfig;
import cn.mlamp.insightflow.cms.config.UserContext;

import cn.mlamp.insightflow.cms.config.WebhookConfig;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.model.dto.SystemMetricsDTO;
import cn.mlamp.insightflow.cms.model.query.TokenRechargeRequest;
import cn.mlamp.insightflow.cms.service.DataStatisticsService;
import cn.mlamp.insightflow.cms.service.ISystemMetricsService;
import cn.mlamp.insightflow.cms.service.TenantTokenService;
import cn.mlamp.insightflow.cms.service.WechatWebhookService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 管理员控制器
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
@Slf4j
@RequestMapping("/admin")
@RestController
@RequiredArgsConstructor
@Tag(name = "管理员相关接口")
public class AdminController {

    @Autowired
    private final TtcAdaptor ttcAdaptor;

    @Autowired
    private final AdminConfig adminConfig;

    @Autowired
    private final TenantTokenService tenantTokenService;

    @Autowired
    private final WechatWebhookService wechatWebhookService;

    @Autowired
    private final DataStatisticsService dataStatisticsService;

    @Autowired
    private final ISystemMetricsService systemMetricsService;

    @Autowired
    private final WebhookConfig webhookConfig;


    /**
     * 获取所有租户信息，支持按名称筛选
     *
     * @param name 租户名称（可选，用于筛选）
     * @return 租户信息列表
     */
    @GetMapping("/tenant/list")
    @Operation(summary = "获取所有租户信息")
    public RespBody<List<TtcTenantBaseInfoDTO>> queryTenantList(
            @RequestParam(value = "name", required = false) String name) {
        log.info("查询租户列表，筛选条件name: {}", name);

        // 检查当前用户是否为管理员租户
        Integer currentTenantId = UserContext.getTenantId();
        if (!Objects.equals(currentTenantId, adminConfig.getTenantId())) {
            log.warn("非管理员租户({}≠{})尝试访问管理员接口", currentTenantId, adminConfig.getTenantId());
            throw new BusinessException(RespCode.FORBIDDEN.getCode(), "您没有权限访问此接口");
        }

        // 调用TtcAdaptor获取所有租户信息
        List<TtcTenantBaseInfoDTO> tenantList = ttcAdaptor.queryTenantList();

        // 如果提供了name参数，进行筛选
        if (StrUtil.isNotBlank(name) && CollUtil.isNotEmpty(tenantList)) {
            tenantList = tenantList.stream()
                    .filter(tenant -> tenant.getTenantName() != null && tenant.getTenantName().contains(name))
                    .collect(Collectors.toList());
        }

        return RespBody.ok(tenantList);
    }

    /**
     * 租户Token充值
     *
     * @param request 充值请求参数
     * @return 充值结果
     */
    @PostMapping("/tenant/recharge")
    @Operation(summary = "租户Token充值")
    public RespBody<?> rechargeToken(@Valid @RequestBody TokenRechargeRequest request) {
        log.info("租户Token充值，参数: {}", request);

        // 检查当前用户是否为管理员租户
        Integer currentTenantId = UserContext.getTenantId();
        if (!Objects.equals(currentTenantId, adminConfig.getTenantId())) {
            log.warn("非管理员租户({}≠{})尝试访问管理员接口", currentTenantId, adminConfig.getTenantId());
            throw new BusinessException(RespCode.FORBIDDEN.getCode(), "您没有权限访问此接口");
        }

        // 调用服务层方法进行充值
        tenantTokenService.rechargeToken(request.getTenantId(), request.getTenantName(), request.getRechargeTokens(),
                UserContext.getUserId());

        return RespBody.ok();
    }

    /**
     * 推送千川数据统计到微信webhook
     *
     * @return 推送结果
     */
    @GetMapping("/qianchuan/stats/push")
    @Operation(summary = "推送千川数据统计到微信webhook")
    public RespBody<?> pushQianchuanStats() {
        log.info("开始推送千川数据统计到微信webhook");

        // 检查当前用户是否为管理员租户
        Integer currentTenantId = UserContext.getTenantId();
        if (!Objects.equals(currentTenantId, adminConfig.getTenantId())) {
            log.warn("非管理员租户({}≠{})尝试访问管理员接口", currentTenantId, adminConfig.getTenantId());
//            throw new BusinessException(RespCode.FORBIDDEN.getCode(), "您没有权限访问此接口");
        }

        try {
            // 获取千川数据统计
            Map<String, Object> statistics = dataStatisticsService.getQianchuanPushStatistics();

            SystemMetricsDTO systemMetricsDTO = systemMetricsService.getSystemMetrics(DateUtil.offsetDay(new Date(), -1));

            // 格式化为Markdown消息
            String markdownContent = dataStatisticsService.formatStatisticsToMarkdown(statistics, systemMetricsDTO);

            // 发送到微信webhook
            boolean success = wechatWebhookService.sendMarkdownMessage(markdownContent);

            if (!success) {
                log.error("推送千川数据统计到微信webhook失败");
                return RespBody.fail("推送失败");
            }

            String businessContent = dataStatisticsService.getMarkdownHeader(new StringBuilder(), systemMetricsDTO).toString();
            wechatWebhookService.sendMarkdownMessage(businessContent, webhookConfig.getBusinessWebhookUrl());

            systemMetricsService.sendDayReport(new Date());

            log.info("推送千川数据统计到微信webhook成功");
            return RespBody.ok();
        } catch (Exception e) {
            log.error("推送千川数据统计到微信webhook异常", e);
            return RespBody.fail("推送异常: " + e.getMessage());
        }
    }

}
