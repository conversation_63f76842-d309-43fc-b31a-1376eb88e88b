{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if or .Release.IsUpgrade (ne .Values.sentinel.service.type "NodePort")  .Values.sentinel.service.nodePorts.redis -}}
{{- if and (eq .Values.architecture "replication") .Values.sentinel.enabled }}
apiVersion: {{ include "common.capabilities.statefulset.apiVersion" . }}
kind: StatefulSet
metadata:
  name: {{ printf "%s-node" (include "common.names.fullname" .) }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: node
  {{- if or .Values.commonAnnotations .Values.sentinel.annotations }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.sentinel.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.replica.replicaCount }}
  revisionHistoryLimit: {{ .Values.replica.revisionHistoryLimit }}
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.replica.podLabels .Values.commonLabels ) "context" . ) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: node
  serviceName: {{ printf "%s-headless" (include "common.names.fullname" .) }}
  {{- if .Values.replica.updateStrategy }}
  updateStrategy: {{- toYaml .Values.replica.updateStrategy | nindent 4 }}
  {{- end }}
  {{- if .Values.replica.minReadySeconds }}
  minReadySeconds: {{ .Values.replica.minReadySeconds }}
  {{- end }}
  {{- if .Values.replica.podManagementPolicy }}
  podManagementPolicy: {{ .Values.replica.podManagementPolicy | quote }}
  {{- end }}
  template:
    metadata:
      labels: {{- include "common.labels.standard" ( dict "customLabels" $podLabels "context" $ ) | nindent 8 }}
        app.kubernetes.io/component: node
        {{- if .Values.sentinel.masterService.enabled }}
        app.kubernetes.io/role: slave
        {{- end }}
        {{- if and .Values.metrics.enabled .Values.metrics.podLabels }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.metrics.podLabels "context" $ ) | nindent 8 }}
        {{- end }}
      annotations:
      {{- if .Values.configmapChecksumAnnotations }}
        {{- if (include "redis.createConfigmap" .) }}
        checksum/configmap: {{ pick ( include (print $.Template.BasePath "/configmap.yaml") . | fromYaml ) "data" | toYaml | sha256sum }}
        {{- end }}
        checksum/health: {{ pick ( include (print $.Template.BasePath "/health-configmap.yaml") . | fromYaml ) "data" | toYaml | sha256sum }}
        checksum/scripts: {{ pick ( include (print $.Template.BasePath "/scripts-configmap.yaml") . | fromYaml ) "data" | toYaml | sha256sum }}
      {{- end }}
      {{- if .Values.secretChecksumAnnotations }}
        checksum/secret: {{ pick ( include (print $.Template.BasePath "/secret.yaml") . | fromYaml ) "data" | toYaml | sha256sum }}
      {{- end }}
      {{- if .Values.replica.podAnnotations }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.replica.podAnnotations "context" $ ) | nindent 8 }}
      {{- end }}
      {{- if and .Values.metrics.enabled .Values.metrics.podAnnotations }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.metrics.podAnnotations "context" $ ) | nindent 8 }}
      {{- end }}
    spec:
      {{- if .Values.sentinel.extraPodSpec }}
      {{- include "common.tplvalues.render" (dict "value" .Values.sentinel.extraPodSpec "context" $) | nindent 6 }}
      {{- end }}
      {{- include "redis.imagePullSecrets" . | nindent 6 }}
      automountServiceAccountToken: {{ .Values.replica.automountServiceAccountToken }}
      {{- if .Values.replica.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.replica.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.replica.priorityClassName }}
      priorityClassName: {{ .Values.replica.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.replica.affinity }}
      affinity: {{- include "common.tplvalues.render" (dict "value" .Values.replica.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.replica.podAffinityPreset "component" "node" "customLabels" $podLabels "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.replica.podAntiAffinityPreset "component" "node" "customLabels" $podLabels "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.replica.nodeAffinityPreset.type "key" .Values.replica.nodeAffinityPreset.key "values" .Values.replica.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.replica.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" (dict "value" .Values.replica.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.replica.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.replica.tolerations "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.replica.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.replica.topologySpreadConstraints "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.replica.shareProcessNamespace }}
      shareProcessNamespace: {{ .Values.replica.shareProcessNamespace }}
      {{- end }}
      {{- if .Values.replica.schedulerName }}
      schedulerName: {{ .Values.replica.schedulerName | quote }}
      {{- end }}
      {{- if .Values.replica.dnsPolicy }}
      dnsPolicy: {{ .Values.replica.dnsPolicy }}
      {{- end }}
      {{- if .Values.replica.dnsConfig }}
      dnsConfig: {{- include "common.tplvalues.render" (dict "value" .Values.replica.dnsConfig "context" $) | nindent 8 }}
      {{- end }}
      enableServiceLinks: {{ .Values.sentinel.enableServiceLinks }}
      terminationGracePeriodSeconds: {{ .Values.sentinel.terminationGracePeriodSeconds }}
      containers:
        - name: redis
          image: {{ template "redis.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy | quote }}
          {{- if not .Values.diagnosticMode.enabled }}
          {{- if .Values.replica.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.replica.lifecycleHooks "context" $) | nindent 12 }}
          {{- else }}
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/bash
                  - -ec
                  - /opt/bitnami/scripts/start-scripts/prestop-redis.sh
          {{- end }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.command "context" $) | nindent 12 }}
          {{- else if .Values.replica.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.replica.command "context" $) | nindent 12 }}
          {{- else }}
          command: ['/bin/bash', '-ec']
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.args "context" $) | nindent 12 }}
          {{- else if .Values.replica.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.replica.args "context" $) | nindent 12 }}
          {{- else if .Values.sentinel.externalAccess.enabled }}
          args:
            - |
              pod_index=($(echo "$POD_NAME" | tr "-" "\n"))
              pod_index="${pod_index[-1]}"
              ips=($(echo "{{ .Values.sentinel.externalAccess.service.loadBalancerIP }}" | cut -d [ -f2 | cut -d ] -f 1))
              export REDIS_CLUSTER_ANNOUNCE_IP="${ips[$pod_index]}"
              export REDIS_NODES="${ips[@]}"
              /opt/bitnami/scripts/start-scripts/start-node.sh
          {{- else }}
          args:
            - /opt/bitnami/scripts/start-scripts/start-node.sh
          {{- end }}
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: BITNAMI_DEBUG
              value: {{ ternary "true" "false" (or .Values.image.debug .Values.diagnosticMode.enabled) | quote }}
            - name: REDIS_MASTER_PORT_NUMBER
              value: {{ .Values.replica.containerPorts.redis | quote }}
            - name: ALLOW_EMPTY_PASSWORD
              value: {{ ternary "no" "yes" .Values.auth.enabled | quote }}
            {{- if .Values.auth.enabled }}
            {{- if .Values.auth.usePasswordFiles }}
            - name: REDIS_PASSWORD_FILE
              value: "/opt/bitnami/redis/secrets/redis-password"
            - name: REDIS_MASTER_PASSWORD_FILE
              value: "/opt/bitnami/redis/secrets/redis-password"
            {{- else }}
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ template "redis.secretName" . }}
                  key: {{ template "redis.secretPasswordKey" . }}
            - name: REDIS_MASTER_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ template "redis.secretName" . }}
                  key: {{ template "redis.secretPasswordKey" . }}
            {{- end }}
            {{- end }}
            - name: REDIS_TLS_ENABLED
              value: {{ ternary "yes" "no" .Values.tls.enabled | quote }}
            {{- if .Values.tls.enabled }}
            - name: REDIS_TLS_PORT
              value: {{ .Values.replica.containerPorts.redis | quote }}
            - name:  REDIS_TLS_AUTH_CLIENTS
              value: {{ ternary "yes" "no" .Values.tls.authClients | quote }}
            - name:  REDIS_TLS_CERT_FILE
              value: {{ template "redis.tlsCert" . }}
            - name:  REDIS_TLS_KEY_FILE
              value: {{ template "redis.tlsCertKey" . }}
            {{- if not (empty (include "redis.tlsCACert" .)) }}
            - name:  REDIS_TLS_CA_FILE
              value: {{ template "redis.tlsCACert" . }}
            {{- end }}
            {{- if .Values.tls.dhParamsFilename }}
            - name:  REDIS_TLS_DH_PARAMS_FILE
              value: {{ template "redis.tlsDHParams" . }}
            {{- end }}
            {{- else }}
            - name: REDIS_PORT
              value: {{ .Values.replica.containerPorts.redis | quote }}
            {{- end }}
            - name: REDIS_SENTINEL_TLS_ENABLED
              value: {{ ternary "yes" "no" .Values.tls.enabled | quote }}
            {{- if .Values.tls.enabled }}
            - name: REDIS_SENTINEL_TLS_PORT_NUMBER
              value: {{ .Values.sentinel.containerPorts.sentinel | quote }}
            - name:  REDIS_SENTINEL_TLS_AUTH_CLIENTS
              value: {{ ternary "yes" "no" .Values.tls.authClients | quote }}
            - name:  REDIS_SENTINEL_TLS_CERT_FILE
              value: {{ template "redis.tlsCert" . }}
            - name:  REDIS_SENTINEL_TLS_KEY_FILE
              value: {{ template "redis.tlsCertKey" . }}
            {{- if not (empty (include "redis.tlsCACert" .)) }}
            - name:  REDIS_SENTINEL_TLS_CA_FILE
              value: {{ template "redis.tlsCACert" . }}
            {{- end }}
            {{- if .Values.tls.dhParamsFilename }}
            - name:  REDIS_SENTINEL_TLS_DH_PARAMS_FILE
              value: {{ template "redis.tlsDHParams" . }}
            {{- end }}
            {{- else }}
            - name: REDIS_SENTINEL_PORT
              value: {{ .Values.sentinel.containerPorts.sentinel | quote }}
            {{- end }}
            - name: REDIS_DATA_DIR
              value: {{ .Values.replica.persistence.path }}
            {{- if .Values.replica.externalMaster.enabled }}
            - name:  REDIS_EXTERNAL_MASTER_HOST
              value: {{ .Values.replica.externalMaster.host | quote }}
            - name:  REDIS_EXTERNAL_MASTER_PORT
              value: {{ .Values.replica.externalMaster.port | quote }}
            {{- end }}
            {{- if .Values.replica.extraEnvVars }}
            {{- include "common.tplvalues.render" ( dict "value" .Values.replica.extraEnvVars "context" $ ) | nindent 12 }}
            {{- end }}
          {{- if or .Values.replica.extraEnvVarsCM .Values.replica.extraEnvVarsSecret }}
          envFrom:
            {{- if .Values.replica.extraEnvVarsCM }}
            - configMapRef:
              name: {{ .Values.replica.extraEnvVarsCM }}
            {{- end }}
            {{- if .Values.replica.extraEnvVarsSecret }}
            - secretRef:
                name: {{ .Values.replica.extraEnvVarsSecret }}
            {{- end }}
          {{- end }}
          ports:
            - name: redis
              containerPort: {{ .Values.replica.containerPorts.redis }}
          {{- if not .Values.diagnosticMode.enabled }}
          {{- end }}
          {{- if .Values.replica.resources }}
          resources: {{- toYaml .Values.replica.resources | nindent 12 }}
          {{- else if ne .Values.replica.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.replica.resourcesPreset) | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: start-scripts
              mountPath: /opt/bitnami/scripts/start-scripts
            - name: health
              mountPath: /health
            - name: sentinel-data
              mountPath: /opt/bitnami/redis-sentinel/etc
            {{- if and .Values.auth.enabled .Values.auth.usePasswordFiles }}
            - name: redis-password
              mountPath: /opt/bitnami/redis/secrets/
            {{- end }}
            - name: redis-data
              mountPath: {{ .Values.replica.persistence.path }}
              {{- if .Values.replica.persistence.subPath }}
              subPath: {{ .Values.replica.persistence.subPath }}
              {{- else if .Values.replica.persistence.subPathExpr }}
              subPathExpr: {{ .Values.replica.persistence.subPathExpr }}
              {{- end }}
            - name: config
              mountPath: /opt/bitnami/redis/mounted-etc
            {{- if and .Values.replica.containerSecurityContext.enabled .Values.replica.containerSecurityContext.readOnlyRootFilesystem }}
            - name: empty-dir
              mountPath: /opt/bitnami/redis/etc
              subPath: app-conf-dir
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
            {{- end }}
            {{- if .Values.tls.enabled }}
            - name: redis-certificates
              mountPath: /opt/bitnami/redis/certs
              readOnly: true
            {{- end }}
            {{- if .Values.replica.extraVolumeMounts }}
            {{- include "common.tplvalues.render" ( dict "value" .Values.replica.extraVolumeMounts "context" $ ) | nindent 12 }}
            {{- end }}
        - name: sentinel
          image: {{ template "redis.sentinel.image" . }}
          imagePullPolicy: {{ .Values.sentinel.image.pullPolicy | quote }}
          {{- if not .Values.diagnosticMode.enabled }}
          {{- if .Values.sentinel.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.sentinel.lifecycleHooks "context" $) | nindent 12 }}
          {{- else }}
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/bash
                  - -ec
                  - /opt/bitnami/scripts/start-scripts/prestop-sentinel.sh
          {{- end }}
          {{- end }}
          {{- if .Values.sentinel.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.sentinel.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.command "context" $) | nindent 12 }}
          {{- else if .Values.sentinel.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.sentinel.command "context" $) | nindent 12 }}
          {{- else }}
          command: ['/bin/bash', '-ec']
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.args "context" $) | nindent 12 }}
          {{- else if .Values.sentinel.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.sentinel.args "context" $) | nindent 12 }}
          {{- else if .Values.sentinel.externalAccess.enabled }}
          args:
            - |
              pod_index=($(echo "$POD_NAME" | tr "-" "\n"))
              pod_index="${pod_index[-1]}"
              ips=($(echo "{{ .Values.sentinel.externalAccess.service.loadBalancerIP }}" | cut -d [ -f2 | cut -d ] -f 1))
              export REDIS_CLUSTER_ANNOUNCE_IP="${ips[$pod_index]}"
              export REDIS_NODES="${ips[@]}"
              /opt/bitnami/scripts/start-scripts/start-sentinel.sh
          {{- else }}
          args:
            - /opt/bitnami/scripts/start-scripts/start-sentinel.sh
          {{- end }}
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: BITNAMI_DEBUG
              value: {{ ternary "true" "false" (or .Values.sentinel.image.debug .Values.diagnosticMode.enabled) | quote }}
            {{- if .Values.auth.enabled }}
            {{- if .Values.auth.usePasswordFiles }}
            - name: REDIS_PASSWORD_FILE
              value: "/opt/bitnami/redis/secrets/redis-password"
            {{- else }}
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ template "redis.secretName" . }}
                  key: {{ template "redis.secretPasswordKey" . }}
            {{- end }}
            {{- else }}
            - name: ALLOW_EMPTY_PASSWORD
              value: "yes"
            {{- end }}
            - name: REDIS_SENTINEL_TLS_ENABLED
              value: {{ ternary "yes" "no" .Values.tls.enabled | quote }}
            {{- if .Values.tls.enabled }}
            - name: REDIS_SENTINEL_TLS_PORT_NUMBER
              value: {{ .Values.sentinel.containerPorts.sentinel | quote }}
            - name:  REDIS_SENTINEL_TLS_AUTH_CLIENTS
              value: {{ ternary "yes" "no" .Values.tls.authClients | quote }}
            - name:  REDIS_SENTINEL_TLS_CERT_FILE
              value: {{ template "redis.tlsCert" . }}
            - name:  REDIS_SENTINEL_TLS_KEY_FILE
              value: {{ template "redis.tlsCertKey" . }}
            {{- if not (empty (include "redis.tlsCACert" .)) }}
            - name:  REDIS_SENTINEL_TLS_CA_FILE
              value: {{ template "redis.tlsCACert" . }}
            {{- end }}
            {{- if .Values.tls.dhParamsFilename }}
            - name:  REDIS_SENTINEL_TLS_DH_PARAMS_FILE
              value: {{ template "redis.tlsDHParams" . }}
            {{- end }}
            {{- else }}
            - name: REDIS_SENTINEL_PORT
              value: {{ .Values.sentinel.containerPorts.sentinel | quote }}
            {{- end }}
            {{- if .Values.sentinel.externalMaster.enabled }}
            - name:  REDIS_EXTERNAL_MASTER_HOST
              value: {{ .Values.sentinel.externalMaster.host | quote }}
            - name:  REDIS_EXTERNAL_MASTER_PORT
              value: {{ .Values.sentinel.externalMaster.port | quote }}
            {{- end }}
            {{- if .Values.sentinel.extraEnvVars }}
            {{- include "common.tplvalues.render" ( dict "value" .Values.sentinel.extraEnvVars "context" $ ) | nindent 12 }}
            {{- end }}
          {{- if or .Values.sentinel.extraEnvVarsCM .Values.sentinel.extraEnvVarsSecret }}
          envFrom:
            {{- if .Values.sentinel.extraEnvVarsCM }}
            - configMapRef:
                name: {{ .Values.sentinel.extraEnvVarsCM }}
            {{- end }}
            {{- if .Values.sentinel.extraEnvVarsSecret }}
            - secretRef:
                name: {{ .Values.sentinel.extraEnvVarsSecret }}
            {{- end }}
          {{- end }}
          ports:
            - name: redis-sentinel
              containerPort: {{ .Values.sentinel.containerPorts.sentinel }}
          {{- if not .Values.diagnosticMode.enabled }}
          {{- end }}
          {{- if not .Values.diagnosticMode.enabled }}
          {{- end }}
          {{- if .Values.sentinel.resources }}
          resources: {{- toYaml .Values.sentinel.resources | nindent 12 }}
          {{- else if ne .Values.sentinel.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.sentinel.resourcesPreset) | nindent 12 }}
          {{- end }}
          volumeMounts:
            {{- if and .Values.sentinel.containerSecurityContext.enabled .Values.sentinel.containerSecurityContext.readOnlyRootFilesystem }}
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
            {{- end }}
            - name: start-scripts
              mountPath: /opt/bitnami/scripts/start-scripts
            - name: health
              mountPath: /health
            {{- if or .Values.sentinel.masterService.enabled .Values.sentinel.service.createMaster}}
            - name: kubectl-shared
              mountPath: /etc/shared
            {{- end }}
            - name: sentinel-data
              mountPath: /opt/bitnami/redis-sentinel/etc
            {{- if and .Values.auth.enabled .Values.auth.usePasswordFiles }}
            - name: redis-password
              mountPath: /opt/bitnami/redis/secrets/
            {{- end }}
            - name: redis-data
              mountPath: {{ .Values.replica.persistence.path }}
              {{- if .Values.replica.persistence.subPath }}
              subPath: {{ .Values.replica.persistence.subPath }}
              {{- else if .Values.replica.persistence.subPathExpr }}
              subPathExpr: {{ .Values.replica.persistence.subPathExpr }}
              {{- end }}
            - name: config
              mountPath: /opt/bitnami/redis-sentinel/mounted-etc
            {{- if .Values.tls.enabled }}
            - name: redis-certificates
              mountPath: /opt/bitnami/redis/certs
              readOnly: true
            {{- end }}
            {{- if .Values.sentinel.extraVolumeMounts }}
            {{- include "common.tplvalues.render" ( dict "value" .Values.sentinel.extraVolumeMounts "context" $ ) | nindent 12 }}
            {{- end }}
        {{- if .Values.metrics.enabled }}
        - name: metrics
          image: {{ template "redis.metrics.image" . }}
          imagePullPolicy: {{ .Values.metrics.image.pullPolicy | quote }}
          {{- if .Values.metrics.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.metrics.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.command "context" $) | nindent 12 }}
          {{- else }}
          command:
            - /bin/bash
            - -ec
            - |
              {{- if and .Values.auth.enabled .Values.auth.usePasswordFiles }}
              export REDIS_PASSWORD="$(< $REDIS_PASSWORD_FILE)"
              {{- end }}
              redis_exporter{{- range $key, $value := .Values.metrics.extraArgs }} --{{ $key }}={{ $value }}{{- end }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.args "context" $) | nindent 12 }}
          {{- end }}
          env:
            - name: REDIS_ALIAS
              value: {{ template "common.names.fullname" . }}
            - name: REDIS_EXPORTER_WEB_LISTEN_ADDRESS
              value: {{ printf ":%v" .Values.metrics.containerPorts.http }}
            {{- if .Values.auth.enabled }}
            - name: REDIS_USER
              value: default
            {{- if .Values.auth.usePasswordFiles }}
            - name: REDIS_PASSWORD_FILE
              value: "/secrets/redis-password"
            {{- else }}
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ template "redis.secretName" . }}
                  key: {{ template "redis.secretPasswordKey" . }}
            {{- end }}
            {{- end }}
            {{- if .Values.tls.enabled }}
            - name: REDIS_ADDR
              value: rediss://{{ .Values.metrics.redisTargetHost }}:{{ .Values.replica.containerPorts.redis }}
            {{- if .Values.tls.authClients }}
            - name: REDIS_EXPORTER_TLS_CLIENT_KEY_FILE
              value: {{ template "redis.tlsCertKey" . }}
            - name: REDIS_EXPORTER_TLS_CLIENT_CERT_FILE
              value: {{ template "redis.tlsCert" . }}
            {{- end }}
            {{- if not (empty (include "redis.tlsCACert" .)) }}
            - name: REDIS_EXPORTER_TLS_CA_CERT_FILE
              value: {{ template "redis.tlsCACert" . }}
            {{- end }}
            {{- end }}
            {{- if .Values.metrics.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.metrics.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          ports:
            - name: metrics
              containerPort: {{ .Values.metrics.containerPorts.http }}
          {{- if not .Values.diagnosticMode.enabled }}
          {{- if .Values.metrics.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.metrics.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.metrics.startupProbe "enabled") "context" $) | nindent 12 }}
            tcpSocket:
              port: metrics
          {{- end }}
          {{- if .Values.metrics.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.metrics.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.metrics.livenessProbe "enabled") "context" $) | nindent 12 }}
            tcpSocket:
              port: metrics
          {{- end }}
          {{- if .Values.metrics.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.metrics.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.metrics.readinessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /
              port: metrics
          {{- end }}
          {{- end }}
          {{- if .Values.metrics.resources }}
          resources: {{- toYaml .Values.metrics.resources | nindent 12 }}
          {{- else if ne .Values.metrics.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.metrics.resourcesPreset) | nindent 12 }}
          {{- end }}
          volumeMounts:
            {{- if and .Values.metrics.containerSecurityContext.enabled .Values.metrics.containerSecurityContext.readOnlyRootFilesystem }}
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
            {{- end }}
            {{- if and .Values.auth.enabled .Values.auth.usePasswordFiles }}
            - name: redis-password
              mountPath: /secrets/
            {{- end }}
            {{- if .Values.tls.enabled }}
            - name: redis-certificates
              mountPath: /opt/bitnami/redis/certs
              readOnly: true
            {{- end }}
            {{- if .Values.metrics.extraVolumeMounts }}
            {{- include "common.tplvalues.render" ( dict "value" .Values.metrics.extraVolumeMounts "context" $ ) | nindent 12 }}
            {{- end }}
        {{- end }}
        {{- if or .Values.sentinel.masterService.enabled .Values.sentinel.service.createMaster }}
        - name: kubectl-shared
          image: {{ template "redis.kubectl.image" . }}
          imagePullPolicy: {{ .Values.kubectl.image.pullPolicy | quote }}
          command: {{- toYaml .Values.kubectl.command | nindent 12 }}
          lifecycle:
            preStop:
              exec:
                command:
                  - touch
                  - /etc/shared/terminate
          {{- if .Values.kubectl.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.kubectl.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: kubectl-shared
              mountPath: /etc/shared
            - name: kubectl-scripts
              mountPath: /opt/bitnami/scripts/kubectl-scripts
          {{- if .Values.kubectl.resources }}
          resources: {{- toYaml .Values.kubectl.resources | nindent 12 }}
          {{- end }}
        {{- end }}
        {{- if .Values.replica.sidecars }}
        {{- include "common.tplvalues.render" (dict "value" .Values.replica.sidecars "context" $) | nindent 8 }}
        {{- end }}
      {{- $needsVolumePermissions := and .Values.volumePermissions.enabled .Values.replica.persistence.enabled .Values.replica.podSecurityContext.enabled .Values.replica.containerSecurityContext.enabled }}
      {{- if or .Values.replica.initContainers $needsVolumePermissions .Values.sysctl.enabled }}
      initContainers:
        {{- if .Values.replica.initContainers }}
        {{- include "common.tplvalues.render" (dict "value" .Values.replica.initContainers "context" $) | nindent 8 }}
        {{- end }}
        {{- if $needsVolumePermissions }}
        - name: volume-permissions
          image: {{ include "redis.volumePermissions.image" . }}
          imagePullPolicy: {{ .Values.volumePermissions.image.pullPolicy | quote }}
          command:
            - /bin/bash
            - -ec
            - |
              {{- if eq ( toString ( .Values.volumePermissions.containerSecurityContext.runAsUser )) "auto" }}
              chown -R `id -u`:`id -G | cut -d " " -f2` {{ .Values.replica.persistence.path }}
              {{- else }}
              chown -R {{ .Values.replica.containerSecurityContext.runAsUser }}:{{ .Values.replica.podSecurityContext.fsGroup }} {{ .Values.replica.persistence.path }}
              {{- end }}
          {{- if eq ( toString ( .Values.volumePermissions.containerSecurityContext.runAsUser )) "auto" }}
          securityContext: {{- omit .Values.volumePermissions.containerSecurityContext "runAsUser" | toYaml | nindent 12 }}
          {{- else }}
          securityContext: {{- .Values.volumePermissions.containerSecurityContext | toYaml | nindent 12 }}
          {{- end }}
          {{- if .Values.volumePermissions.extraEnvVars }}
          env:
          {{- include "common.tplvalues.render" (dict "value" .Values.volumePermissions.extraEnvVars "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.volumePermissions.resources }}
          resources: {{- toYaml .Values.volumePermissions.resources | nindent 12 }}
          {{- else if ne .Values.volumePermissions.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.volumePermissions.resourcesPreset) | nindent 12 }}
          {{- end }}
          volumeMounts:
            {{- if and .Values.volumePermissions.containerSecurityContext.enabled .Values.volumePermissions.containerSecurityContext.readOnlyRootFilesystem }}
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
            {{- end }}
            - name: redis-data
              mountPath: {{ .Values.replica.persistence.path }}
              {{- if .Values.replica.persistence.subPath }}
              subPath: {{ .Values.replica.persistence.subPath }}
              {{- else if .Values.replica.persistence.subPathExpr }}
              subPathExpr: {{ .Values.replica.persistence.subPathExpr }}
              {{- end }}
        {{- end }}
        {{- if .Values.sysctl.enabled }}
        - name: init-sysctl
          image: {{ include "redis.sysctl.image" . }}
          imagePullPolicy: {{ default "" .Values.sysctl.image.pullPolicy | quote }}
          securityContext:
            privileged: true
            runAsUser: 0
          {{- if .Values.sysctl.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.sysctl.command "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.sysctl.resources }}
          resources: {{- toYaml .Values.sysctl.resources | nindent 12 }}
          {{- else if ne .Values.sysctl.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.sysctl.resourcesPreset) | nindent 12 }}
          {{- end }}
          {{- if .Values.sysctl.mountHostSys }}
          volumeMounts:
            - name: host-sys
              mountPath: /host-sys
          {{- end }}
        {{- end }}
      {{- end }}
      volumes:
        - name: start-scripts
          configMap:
            name: {{ printf "%s-scripts" (include "common.names.fullname" .) }}
            defaultMode: 0755
        - name: health
          configMap:
            name: {{ printf "%s-health" (include "common.names.fullname" .) }}
            defaultMode: 0755
        {{- if or .Values.sentinel.masterService.enabled .Values.sentinel.service.createMaster}}
        - name: kubectl-shared
          emptyDir: {}
        - name: kubectl-scripts
          configMap:
            name: {{ printf "%s-kubectl-scripts" (include "common.names.fullname" .) }}
            defaultMode: 0755
        {{- end }}
        {{- if and .Values.auth.enabled .Values.auth.usePasswordFiles }}
        - name: redis-password
          {{ if .Values.auth.usePasswordFileFromSecret }}
          secret:
            secretName: {{ template "redis.secretName" . }}
            items:
            - key: {{ template "redis.secretPasswordKey" . }}
              path: redis-password
          {{- else }}
          emptyDir: {}
          {{- end }}
        {{- end }}
        - name: config
          configMap:
            name: {{ include "redis.configmapName" . }}
        {{- if .Values.sysctl.mountHostSys }}
        - name: host-sys
          hostPath:
            path: /sys
        {{- end }}
        {{- if not .Values.sentinel.persistence.enabled }}
        - name: sentinel-data
          {{- if or .Values.sentinel.persistence.medium .Values.sentinel.persistence.sizeLimit }}
          emptyDir:
            {{- if .Values.sentinel.persistence.medium }}
            medium: {{ .Values.sentinel.persistence.medium | quote }}
            {{- end }}
            {{- if .Values.sentinel.persistence.sizeLimit }}
            sizeLimit: {{ .Values.sentinel.persistence.sizeLimit | quote }}
            {{- end }}
          {{- else }}
          emptyDir: {}
          {{- end }}
        {{- end }}
        {{- if or (and .Values.sentinel.containerSecurityContext.enabled .Values.sentinel.containerSecurityContext.readOnlyRootFilesystem) (and .Values.metrics.enabled .Values.metrics.containerSecurityContext.enabled .Values.metrics.containerSecurityContext.readOnlyRootFilesystem) }}
        - name: empty-dir
          {{- if or .Values.sentinel.persistence.medium .Values.sentinel.persistence.sizeLimit }}
          emptyDir:
            {{- if .Values.sentinel.persistence.medium }}
            medium: {{ .Values.sentinel.persistence.medium | quote }}
            {{- end }}
            {{- if .Values.sentinel.persistence.sizeLimit }}
            sizeLimit: {{ .Values.sentinel.persistence.sizeLimit | quote }}
            {{- end }}
          {{- else }}
          emptyDir: {}
          {{- end }}
        {{- end }}
        {{- if .Values.replica.extraVolumes }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.replica.extraVolumes "context" $ ) | nindent 8 }}
        {{- end }}
        {{- if .Values.metrics.extraVolumes }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.metrics.extraVolumes "context" $ ) | nindent 8 }}
        {{- end }}
        {{- if .Values.sentinel.extraVolumes }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.sentinel.extraVolumes "context" $ ) | nindent 8 }}
        {{- end }}
        {{- if .Values.tls.enabled }}
        - name: redis-certificates
          secret:
            secretName: {{ include "redis.tlsSecretName" . }}
            defaultMode: 256
        {{- end }}
  {{- if not .Values.replica.persistence.enabled }}
        - name: redis-data
          {{- if or .Values.replica.persistence.medium .Values.replica.persistence.sizeLimit }}
          emptyDir:
            {{- if .Values.replica.persistence.medium }}
            medium: {{ .Values.replica.persistence.medium | quote }}
            {{- end }}
            {{- if .Values.replica.persistence.sizeLimit }}
            sizeLimit: {{ .Values.replica.persistence.sizeLimit | quote }}
            {{- end }}
          {{- else }}
          emptyDir: {}
          {{- end }}
  {{- else if .Values.replica.persistence.existingClaim }}
        - name: redis-data
          persistentVolumeClaim:
            claimName: {{ printf "%s" (tpl .Values.replica.persistence.existingClaim .) }}
  {{- else }}
  {{- if .Values.sentinel.persistentVolumeClaimRetentionPolicy.enabled }}
  persistentVolumeClaimRetentionPolicy:
    whenDeleted: {{ .Values.sentinel.persistentVolumeClaimRetentionPolicy.whenDeleted }}
    whenScaled: {{ .Values.sentinel.persistentVolumeClaimRetentionPolicy.whenScaled }}
  {{- end }}
  volumeClaimTemplates:
    - apiVersion: v1
      kind: PersistentVolumeClaim
      metadata:
        name: redis-data
        labels: {{- include "common.labels.matchLabels" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 10 }}
          app.kubernetes.io/component: node
        {{- if .Values.replica.persistence.annotations }}
        annotations: {{- toYaml .Values.replica.persistence.annotations | nindent 10 }}
        {{- end }}
      spec:
        accessModes:
        {{- range .Values.replica.persistence.accessModes }}
          - {{ . | quote }}
        {{- end }}
        resources:
          requests:
            storage: {{ .Values.replica.persistence.size | quote }}
        {{- if .Values.replica.persistence.selector }}
        selector: {{- include "common.tplvalues.render" ( dict "value" .Values.replica.persistence.selector "context" $) | nindent 10 }}
        {{- end }}
        {{- include "common.storage.class" (dict "persistence" .Values.replica.persistence "global" .Values.global) | nindent 8 }}
    {{- if .Values.sentinel.persistence.enabled }}
    - apiVersion: v1
      kind: PersistentVolumeClaim
      metadata:
        name: sentinel-data
        {{- $claimLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.sentinel.persistence.labels .Values.commonLabels ) "context" . ) }}
        labels: {{- include "common.labels.matchLabels" ( dict "customLabels" $claimLabels "context" $ ) | nindent 10 }}
          app.kubernetes.io/component: node
        {{- if .Values.sentinel.persistence.annotations }}
        annotations: {{- toYaml .Values.sentinel.persistence.annotations | nindent 10 }}
        {{- end }}
      spec:
        accessModes:
        {{- range .Values.sentinel.persistence.accessModes }}
          - {{ . | quote }}
        {{- end }}
        resources:
          requests:
            storage: {{ .Values.sentinel.persistence.size | quote }}
        {{- if .Values.sentinel.persistence.selector }}
        selector: {{- include "common.tplvalues.render" ( dict "value" .Values.sentinel.persistence.selector "context" $) | nindent 10 }}
        {{- end }}
        {{- if .Values.sentinel.persistence.dataSource }}
        dataSource: {{- include "common.tplvalues.render" (dict "value" .Values.sentinel.persistence.dataSource "context" $) | nindent 10 }}
        {{- end }}
        {{- include "common.storage.class" (dict "persistence" .Values.sentinel.persistence "global" .Values.global) | nindent 8 }}
    {{- end }}
  {{- end }}
{{- end }}
{{- end }}
