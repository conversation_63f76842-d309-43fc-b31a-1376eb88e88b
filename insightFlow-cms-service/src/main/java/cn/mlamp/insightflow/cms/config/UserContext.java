package cn.mlamp.insightflow.cms.config;

import java.util.Collections;
import java.util.List;

import com.mz.ttc.model.PermissionInfo;
import com.mz.ttc.model.RoleInfo;
import com.mz.ttc.model.TenantInfo;
import com.mz.ttc.util.TtcUtil;

public class UserContext {

    public static Integer getUserId() {
        return TtcUtil.getCurrentLoginInfo().getUserId();
    }

    public static String getUserName() {
        return TtcUtil.getCurrentUserName();
    }

    public static Integer getTenantId() {
        return TtcUtil.getCurrentTenantId();
    }

    public static String getEmail() {
        return TtcUtil.getCurrentLoginInfo().getEmail();
    }

    public static boolean hasPermission(String permission) {
        return getPermissions().contains(permission);
    }

    public static List<String> getPermissions() {
        final TenantInfo currentTenant = TtcUtil.getCurrentTenant();
        if (currentTenant == null) {
            return Collections.emptyList();
        }
        final RoleInfo roleInfo = currentTenant.getRole();
        if (roleInfo == null) {
            return Collections.emptyList();
        }
        return roleInfo.getPermissionInfoList().stream().map(PermissionInfo::getPermissionKey).toList();
    }

}
