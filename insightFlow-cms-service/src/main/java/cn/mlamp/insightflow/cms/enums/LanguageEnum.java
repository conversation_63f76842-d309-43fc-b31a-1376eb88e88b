package cn.mlamp.insightflow.cms.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.EnumSet;
import java.util.Objects;

/**
 * 语言 enum
 *
 * <AUTHOR>
 */
public enum LanguageEnum implements IEnum<String> {

    UNKNOWN(0, "unknown"),
    CHINESE(1, "zh"),
    // 预留繁体位置
    ENGLISH(3, "en"),
    SPANISH(4, "es"),
    FRENCH(5, "fr"),
    GERMAN(6, "de"),
    ARABIC(7, "ar"),
    RUSSIAN(8, "ru"),
    PORTUGUESE(9, "pt"),
    JAPANESE(10, "ja"),
    KOREAN(11, "ko"),
    ITALIAN(12, "it"),
    <PERSON><PERSON><PERSON>(13, "hi"),

    // 泰语
    THAI(20, "th"),
    // 越南语
    VIETNAMESE(21, "vi"),
    // 印尼
    INDONESIAN(22, "id"),
    // 马来语
    MALAY(23, "ms"),
    // 菲利宾
    FILIPINO(24, "fil"),
    TAGALOG(25, "tl"),
    // 缅甸
    BURMESE(26, "my"),
    // 高棉
    KHMER(27, "km"),
    // 老挝
    LAO(28, "lo"),
    JAVANESE(29, "jv"),
    SUNDANESE(30, "su"),
    MYANMAR(31, "my"),
    // 32
    // 33
    // 34
    CEBUANO(35, "ceb"),
    HMONG(36, "hmn"),

    // 南亚语言（与东南亚有文化联系）
    BENGALI(40, "bn"),
    TAMIL(41, "ta"),

    // 其他重要语言
    DUTCH(50, "nl"),
    TURKISH(51, "tr"),
    POLISH(52, "pl"),
    UKRAINIAN(53, "uk"),
    SWEDISH(54, "sv"),
    GREEK(55, "el"),
    HEBREW(56, "he"),
    CZECH(57, "cs"),
    PERSIAN(58, "fa"),
    ROMANIAN(59, "ro"),
    NYNORSK(60, "nn"),
    LATIN(61, "la"),
    ;

    /**
     * int code
     */
    private final int code;

    /**
     * iso 字母缩写
     */
    @Getter
    private final String isoCode;

    LanguageEnum(int code, String isoCode) {
        this.code = code;
        this.isoCode = isoCode;
    }

    public static LanguageEnum from(Integer code) {
        if (code == null) {
            return null;
        }
        for (LanguageEnum type : LanguageEnum.values()) {
            if (Objects.equals(type.code, code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown LanguageEnum code: " + code);
    }

    /**
     * TODO: 如效率有问题，需加上 Map 加速
     *
     * @param isoCode iso code
     * @return enum
     */
    @JsonCreator
    public static LanguageEnum from(String isoCode) {
        if (StringUtils.isBlank(isoCode)) {
            return null;
        }

        // 处理 iso 语言扩展模式
        if (isoCode.contains("-")) {
            isoCode = isoCode.substring(0, isoCode.indexOf("-"));
        }

        for (LanguageEnum type : LanguageEnum.values()) {
            if (Objects.equals(type.getIsoCode(), isoCode)) {
                return type;
            }
        }
        return valueOf(isoCode.toUpperCase());
    }

    @Override
    public String getValue() {
        return isoCode;
    }

    @JsonValue
    @Override
    public String toString() {
        return getIsoCode();
    }

    public String toSceneDecodeLanguage() {
        final LanguageEnum obj = switch (this) {
            case TAGALOG, FILIPINO -> FILIPINO;
            default -> this;
        };

        return switch (obj) {
            case CHINESE, ENGLISH, INDONESIAN, MALAY, THAI, FILIPINO, VIETNAMESE -> this.getIsoCode();
            default -> ENGLISH.getIsoCode();
        };
    }

}
