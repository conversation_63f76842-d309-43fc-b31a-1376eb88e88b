{{- if and (.Capabilities.APIVersions.Has "autoscaling.k8s.io/v1") (.Values.verticalPodAutoscaler.enabled) }}
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: {{ include "prometheus-node-exporter.fullname" . }}
  namespace: {{ include "prometheus-node-exporter.namespace" . }}
  labels:
    {{- include "prometheus-node-exporter.labels" . | nindent 4 }}
spec:
  {{- with .Values.verticalPodAutoscaler.recommenders }}
  recommenders:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  resourcePolicy:
    containerPolicies:
    - containerName: node-exporter
      {{- with .Values.verticalPodAutoscaler.controlledResources }}
      controlledResources:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.verticalPodAutoscaler.controlledValues }}
      controlledValues: {{ . }}
      {{- end }}
      {{- with .Values.verticalPodAutoscaler.maxAllowed }}
      maxAllowed:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.verticalPodAutoscaler.minAllowed }}
      minAllowed:
        {{- toYaml . | nindent 8 }}
      {{- end }}
  targetRef:
    apiVersion: apps/v1
    kind: DaemonSet
    name: {{ include "prometheus-node-exporter.fullname" . }}
  {{- with .Values.verticalPodAutoscaler.updatePolicy }}
  updatePolicy:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
