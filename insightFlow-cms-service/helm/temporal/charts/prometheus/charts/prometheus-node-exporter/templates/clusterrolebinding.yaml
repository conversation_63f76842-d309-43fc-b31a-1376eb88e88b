{{- if and (eq .Values.rbac.create true) (eq .Values.kubeRBACProxy.enabled true) -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    {{- include "prometheus-node-exporter.labels" . | nindent 4 }}
  name: {{ template "prometheus-node-exporter.fullname" . }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
{{- if .Values.rbac.useExistingRole }}
  name: {{ .Values.rbac.useExistingRole }}
{{- else }}
  name: {{ template "prometheus-node-exporter.fullname" . }}
{{- end }}
subjects:
- kind: ServiceAccount
  name: {{ template "prometheus-node-exporter.serviceAccountName" . }}
  namespace: {{ template "prometheus-node-exporter.namespace" . }}
{{- end -}}
