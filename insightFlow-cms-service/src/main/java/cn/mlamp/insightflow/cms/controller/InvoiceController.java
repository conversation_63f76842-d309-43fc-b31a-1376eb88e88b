package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.model.query.CreateInvoiceRequest;
import cn.mlamp.insightflow.cms.model.query.InvoiceListRequest;
import cn.mlamp.insightflow.cms.model.vo.InvoiceDetailVO;
import cn.mlamp.insightflow.cms.model.vo.InvoiceListVO;
import cn.mlamp.insightflow.cms.service.InvoiceService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 发票相关接口
 */
@RestController
@RequestMapping("/invoices")
@AllArgsConstructor
public class InvoiceController {

    private final InvoiceService invoiceService;

    /**
     * 创建开发票申请
     *
     * @param request
     */
    @PostMapping
    public void createInvoice(@RequestBody CreateInvoiceRequest request) {
        invoiceService.createInvoice(request);
    }

    /**
     * 发票管理列表
     *
     * @param request
     * @return
     */
    @PostMapping("/list")
    public RespBody<IPage<InvoiceListVO>> listInvoices(@RequestBody InvoiceListRequest request) {
        IPage<InvoiceListVO> result = invoiceService.listInvoices(request);
        return RespBody.ok(result);
    }

    @GetMapping
    public RespBody<InvoiceDetailVO> getInvoiceDetail(@RequestParam(name = "id") int id) {
        InvoiceDetailVO vo = invoiceService.getInvoiceDetail(id);
        return RespBody.ok(vo);
    }
}
