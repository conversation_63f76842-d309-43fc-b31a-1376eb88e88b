{{- if .Values.pgcli.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "dify.pgcli.fullname" . }}
  labels:
{{- include "dify.labels" . | nindent 4 }}
    component: pgcli
{{ include "dify.ud.labels" . | indent 4 }}
data:
  config: -|
    [alias_dsn]
    {{- if .Values.externalPostgres.enabled }}
    dochub = postgresql://{{ .Values.externalPostgres.username }}:{{ .Values.externalPostgres.password }}@{{ .Values.externalPostgres.address }}:{{ .Values.externalPostgres.port }}/{{ .Values.externalPostgres.dbName }}
    {{- else }}
    {{ with .Values.postgresql.global.postgresql.auth }}
    dochub = postgresql://{{ .username }}:{{ .password }}@{{ .Release.Name }}-postgresql:5432/{{ .database }}
    {{- end }}
    {{- end }}

{{- end }}