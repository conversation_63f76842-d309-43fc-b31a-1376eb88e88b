package cn.mlamp.insightflow.cms.graph.entities.frontend;

import cn.mlamp.insightflow.cms.model.dto.QianchuanVideoHotspotQueryDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class FrontendInfo {

    private String url;

    private String current;

    private ScriptGenerateDTO scriptGenerate;

    private FeaturedVideosDTO featuredVideos;

    private VideoAnalysisDTO videoAnalysis;

    private AiScriptDTO aiScript;

    private VideoGenerateDTO videoGenerate;

    private ThreeSecondsDTO threeSeconds;

    private TaskCenterDTO taskCenter;

    private MaterialLibraryDTO materialLibrary;

    @NoArgsConstructor
    @Data
    public static class ScriptGenerateDTO {

        private ScriptGenerateDTO.FormDTO form;

        @NoArgsConstructor
        @Data
        public static class FormDTO {

            private ScriptGenerateDTO.FormDTO.UploadDTO upload;

            @NoArgsConstructor
            @Data
            public static class UploadDTO {

                private String type;

                private ScriptGenerateDTO.FormDTO.UploadDTO.UrlDTO url;

                private ScriptGenerateDTO.FormDTO.UploadDTO.FileDTO file;

                @NoArgsConstructor
                @Data
                public static class UrlDTO {

                    private List<?> files;
                }

                @NoArgsConstructor
                @Data
                public static class FileDTO {

                    private List<?> files;
                }
            }
        }
    }

    @NoArgsConstructor
    @Data
    public static class FeaturedVideosDTO {

        private FeaturedVideosDTO.FormDTO form;

        @NoArgsConstructor
        @Data
        public static class FormDTO {

            private QianchuanVideoHotspotQueryDTO search;

        }
    }

    @NoArgsConstructor
    @Data
    public static class VideoAnalysisDTO {

        private Integer id;

        private VideoAnalysisDTO.FormDTO form;

        @NoArgsConstructor
        @Data
        public static class FormDTO {

            private VideoAnalysisDTO.FormDTO.AiScriptDTO aiScript;

            @NoArgsConstructor
            @Data
            public static class AiScriptDTO {

                private VideoAnalysisDTO.FormDTO.AiScriptDTO.ParaphrasedContentDTO paraphrasedContent;

                @NoArgsConstructor
                @Data
                public static class ParaphrasedContentDTO {

                    private String brand;

                    private String product;

                    private String sellingPoint;

                    private String scene;

                    private String startGold3s;

                    private String targetAudience;

                    private List<?> storyboardIds;

                }
            }
        }
    }

    @NoArgsConstructor
    @Data
    public static class VideoGenerateDTO {

        private Integer id;

        private VideoGenerateDTO.FormDTO form;

        @NoArgsConstructor
        @Data
        public static class FormDTO {

            private VideoGenerateDTO.FormDTO.VideoCompositingDTO videoCompositing;

            @NoArgsConstructor
            @Data
            public static class VideoCompositingDTO {

                private String taskInfo;

                private List<?> assetIds;

                private List<?> videoOssIds;
            }
        }
    }

    @NoArgsConstructor
    @Data
    public static class ThreeSecondsDTO {

        private ThreeSecondsDTO.FormDTO form;

        @NoArgsConstructor
        @Data
        public static class FormDTO {

            private ThreeSecondsDTO.FormDTO.SearchDTO search;

            @NoArgsConstructor
            @Data
            public static class SearchDTO {

                private String industry;

                private String tag;

                private String sortField;

                private String sortOrder;

                private Integer current;

                private Integer pageSize;

            }
        }
    }

    @NoArgsConstructor
    @Data
    public static class TaskCenterDTO {
    }

    @NoArgsConstructor
    @Data
    public static class MaterialLibraryDTO {
    }
}
