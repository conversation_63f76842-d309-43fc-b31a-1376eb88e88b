package cn.mlamp.insightflow.cms.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties("third-web")
public class ThirdWebProperties {

    private Deepana deepana;

    private DeepanaDySku deepanaDySku;

    private SuanFa suanFa;

    private EmbeddingUrl embeddingUrl;

    private TermDistance termDistance;

    private Boolean tiktokEnabled = false;

    /**
     * 深实访问接口
     *
     * @param baseUrl     深实访问 api 接口
     * @param ossBasePath 深实约定 oss 存放基础目录
     */
    public record Deepana(String baseUrl, String ossBasePath) {
    }

    public record DeepanaDySku(String baseUrl) {

    }

    public record SuanFa(String baseUrl) {

    }

    public record EmbeddingUrl(String baseUrl) {

    }

    public record TermDistance(String baseUrl) {

    }
}
