package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.model.dto.DescriptiveVideoDTO;
import cn.mlamp.insightflow.cms.model.vo.DescriptiveVideoVO;
import cn.mlamp.insightflow.cms.service.DescriptiveVideoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 妙绘视频生成控制器
 */
@Slf4j
@RestController
@RequestMapping("/descriptive-video")
@Tag(name = "妙绘视频生成", description = "妙绘视频生成相关接口")
public class DescriptiveVideoController {

    @Autowired
    private DescriptiveVideoService descriptiveVideoService;

    /**
     * 创建妙绘视频生成任务
     */
    @PostMapping("/create")
    @Operation(summary = "创建妙绘视频生成任务", description = "根据参考图片和描述提示词生成妙绘视频，支持控制是否需要先生成图片")
    public RespBody<DescriptiveVideoVO.DescriptiveVideoCreateResponse> createDescriptiveVideoTask(
            @Valid @RequestBody DescriptiveVideoDTO.DescriptiveVideoCreateRequest request) {

            log.info("创建妙绘视频生成任务，用户ID={}，租户ID={}，视频生成流程={}，请求参数={}", UserContext.getUserId(), UserContext.getTenantId(),
                            request.getVideoGenerationProcess(), request);

        DescriptiveVideoVO.DescriptiveVideoCreateResponse response = descriptiveVideoService
                .createDescriptiveVideoTask(request, UserContext.getUserId(), UserContext.getTenantId());

        // 异步执行视频生成任务
        descriptiveVideoService.generateDescriptiveVideoAsync(request, response.getTaskId(), UserContext.getUserId(),
                UserContext.getTenantId());

        return RespBody.ok(response);
    }

}
