package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("cms_video_voice")
public class CmsVideoVoice extends BaseEntity {

    /**
     * 主键 自增id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 来源类型 0：系统，1：阿里云生成
     */
    private Integer sourceType;

    /**
     * 数据类型 1：BGM；2：音色
     */
    private Integer dataType;

    /**
     * 任务Id
     */
    private Integer taskId;

    /**
     * 名称
     */
    private String name;

    /**
     * 音频文件Id（OSS ID）
     */
    private String ossId;

    /**
     * 音频时长（单位：秒）
     */
    private Integer duration;

    /**
     * 文件类型（mp3）
     */
    private String fileType;

    /**
     * 生成参数json存储
     */
    private String generate_arg;

    /**
     * 租户Id
     */
    private Integer tenantId;

    /**
     * TCC用户Id
     */
    private Integer userId;

}