package cn.mlamp.insightflow.cms.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_qianchuan_material_video_update_log")
public class QianchuanMaterialVideoUpdateLog extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Integer id;

    @TableField("video_id")
    private String videoId;

    private String ossid;

    @TableField("consume_range")
    private String consumeRange;

    private String title;
    private String brand;
    private Integer exposure;
    private Integer shares;
    private Integer comments;
    private Integer likes;
    private Integer clicks;
    private String industry;

    @TableField("our_industry")
    private String ourIndustry; // 自定义行业分类

    @TableField("ranking_type")
    private String rankingType;

    @TableField("publish_time")
    private LocalDateTime publishTime;

    @TableField("author_name")
    private String authorName;

    @TableField("author_avatar")
    private String authorAvatar;

    @TableField("cover_image")
    private String coverImage;

    private Integer duration;

    @TableField("our_duration")
    private Integer ourDuration; // 自定义视频时长（单位：秒）

    @TableField("completion_rate")
    private BigDecimal completionRate;

    private BigDecimal ctr; // 点击率，百分比

    @TableField("material_form")
    private String materialForm; // 素材形式

    private LocalDateTime updated; // 更新时间

    @TableField("source_data")
    private String sourceData; // 源信息字段，记录传递的所有参数（JSON格式）

    @TableField("operation_type")
    private Integer operationType; // 操作类型：1-插入，2-更新

    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;
}
