package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.annotation.HasMemberCheck;
import cn.mlamp.insightflow.cms.annotation.HasMemberRightCheck;
import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.model.dto.VideoReferenceInformationDTO;
import cn.mlamp.insightflow.cms.enums.MemberRightEnum;
import cn.mlamp.insightflow.cms.model.query.UserAddVideoRequest;
import cn.mlamp.insightflow.cms.model.query.StoryBoardUpdateRequest;
import cn.mlamp.insightflow.cms.model.dto.TtsCreateDTO;
import cn.mlamp.insightflow.cms.model.query.DeleteVideoRequest;
import cn.mlamp.insightflow.cms.model.query.*;
import cn.mlamp.insightflow.cms.model.vo.*;
import cn.mlamp.insightflow.cms.service.IVideoEditService;
import cn.mlamp.insightflow.cms.service.IVideoSynthesisService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: husuper
 * @CreateTime: 2025-06-09
 */
@Slf4j
@RequestMapping("/video/edit/")
@RestController
@RequiredArgsConstructor
@Tag(name = "视频编辑合成关接口")
public class VideoEditController {

    @Autowired
    private IVideoEditService videoEditService;

    @Autowired
    private IVideoSynthesisService videoSynthesisService;

    @PostMapping("tts/create")
    @Operation(summary = "TTS生成接口")
    public RespBody<TtsCreateDTO> ttsCreate(@RequestBody TtsCreateRequest ttsCreateRequest) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        if (ttsCreateRequest.getSpeedRatio() == null) {
            ttsCreateRequest.setSpeedRatio(100);
        }
        return RespBody.ok(videoEditService.getAudioBase64(ttsCreateRequest, userId, tenantId));
    }

    @PostMapping("tts-and-bgm/create")
    @Operation(summary = "BGM和TTS生成接口")
    public RespBody<VoiceCreateVO> voiceCreate(@RequestBody VoiceCreateRequest voiceCreateRequest) {
        // 设置用户ID和租户ID
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        if (voiceCreateRequest.getSpeedRatio() == null) {
            voiceCreateRequest.setSpeedRatio(100);
        }
        return RespBody.ok(videoEditService.getBgmAndAudioBase64(voiceCreateRequest, userId, tenantId));
    }

    @PostMapping("bgm/create")
    @Operation(summary = "BGM生成接口")
    public RespBody<VideoBgmCreateVO> voiceCreate(@RequestBody VideoBgmCreateRequest videoBgmCreateRequest) {
        // 设置用户ID和租户ID
        try {
            Integer userId = UserContext.getUserId();
            Integer tenantId = UserContext.getTenantId();
            return RespBody.ok(videoEditService.getVideoBgm(videoBgmCreateRequest, userId, tenantId));
        } catch (Exception e) {
            log.error("BGM生成接口异常{}", e.getMessage());
            VideoBgmCreateVO vo = new VideoBgmCreateVO();
            vo.setTaskId(videoBgmCreateRequest.getTaskId());
            VideoBgmCreateVO.BgmInfo bgmInfo = new VideoBgmCreateVO.BgmInfo();
            bgmInfo.setId(1231423);
            bgmInfo.setDuration(125);
            bgmInfo.setOssId("static/bgm/活泼搞笑/Monsters in Hotel.mp3");
            bgmInfo.setOssUrl("https://ai-pc-cms2.oss-cn-beijing.aliyuncs.com/static/bgm/活泼搞笑/Monsters in Hotel.mp3");
            bgmInfo.setName("AI生成音乐");
            bgmInfo.setFileType("mp3");
            vo.setBgmInfo(bgmInfo);
            return RespBody.ok(vo);
        }
    }

    @PostMapping("referInformation/create")
    @Operation(summary = "视频参考信息生成接口")
    public RespBody<VideoReferInformationCreateVO> videoReferenceInformationCreate(@RequestBody VideoReferInformationRequest videoReferInformationRequest) {
        // 设置用户ID和租户ID
            Integer userId = UserContext.getUserId();
            Integer tenantId = UserContext.getTenantId();
            return RespBody.ok(videoEditService.getVideoReferInformation(videoReferInformationRequest, userId, tenantId));
    }

    @PostMapping("task/create")
    @Operation(summary = "创建视频合成任务")
    @HasMemberRightCheck(rights = {MemberRightEnum.VIDEO_SYNTHESIS})
    public RespBody<VideoSynthesisVO> createTask(@RequestBody VideoSynthesisRequest request) {
        request.setUserId(UserContext.getUserId());
        request.setTenantId(UserContext.getTenantId());
        return RespBody.ok(videoSynthesisService.createSynthesisTask(request));
    }


    @PostMapping("/detail/update")
    @Operation(summary = "更新视频编辑合成任务详情数据")
    public RespBody<Boolean> updateSynthesisTaskDetail(@RequestBody VideoSynthesisDetailUpdateRequest request) {
        // 设置用户ID和租户ID
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        // 更新任务详情数据
        boolean result = videoSynthesisService.updateSynthesisTaskDetail(request, userId, tenantId);
        return RespBody.ok(result);
    }


    @PostMapping("scale")
    @Operation(summary = "视频缩放接口")
    public RespBody<VideoScaleVO> scale(@RequestBody VideoScaleRequest request) {
        return RespBody.ok(videoEditService.scale(request));
    }

    @PostMapping("scale-list")
    @Operation(summary = "视频批量缩放接口")
    public RespBody<List<VideoScaleVO>> scaleList(@RequestBody List<VideoScaleRequest> requests) {
        return RespBody.ok(videoEditService.scaleList(requests));
    }


    @PostMapping("cut")
    @Operation(summary = "视频裁剪接口")
    public RespBody<VideoCutVO> cut(@RequestBody VideoCutRequest request) {
        return RespBody.ok(videoEditService.cut(request));
    }

    @PostMapping("cut-mp3")
    @Operation(summary = "视频裁剪Mp3接口")
    public RespBody<VideoCutMp3VO> cutMp3(@RequestBody VideoCutMp3Request request) {
        return RespBody.ok(videoEditService.cutMp3(request));
    }


    @PostMapping("cut-list")
    @Operation(summary = "视频裁剪接口")
    public RespBody<List<VideoCutVO>> cutList(@RequestBody List<VideoCutRequest> requests) {
        return RespBody.ok(videoEditService.cutList(requests));
    }


    @GetMapping("/detail/{taskId}")
    @Operation(summary = "获取视频合成任务详情")
    public RespBody<VideoSynthesisVO> getSynthesisTaskDetail(@PathVariable Integer taskId) {
        VideoSynthesisVO result = videoSynthesisService.getSynthesisTaskDetail(taskId);
        return RespBody.ok(result);
    }


    @GetMapping("/list")
    @Operation(summary = "获取视频合成任务列表")
    public RespBody<Page<VideoSynthesisListItemVO>> getSynthesisTaskList(VideoSynthesisListRequest request,
                                                                         @RequestParam(value = "listType", defaultValue = "1") Integer listType) {
        return RespBody.ok(videoSynthesisService.getSynthesisTaskList(request, UserContext.getUserId(), UserContext.getTenantId(), listType));
    }


    @PostMapping("bgm/list")
    @Operation(summary = "BGM列表获取接口")
    public RespBody<List<BgmVO>> bgmList(@RequestBody VideoBgmListRequest videoBgmListRequest) {
        // 设置用户ID和租户ID
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        return RespBody.ok(videoEditService.getBgmList(videoBgmListRequest, userId, tenantId));
    }

    @PostMapping("timbre/list/{taskId}")
    @Operation(summary = "音色列表获取接口")
    public RespBody<List<TimbreVO>> timbreList(@PathVariable Integer taskId) {
        // 设置用户ID和租户ID
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        return RespBody.ok(videoEditService.timbreList(taskId, userId, tenantId));
    }

    @GetMapping("numberPerson/timbreList")
    @Operation(summary = "数字人音色列表获取接口")
    public RespBody<List<NumberPersonTimbreVO>> timbreList() {
        return RespBody.ok(videoEditService.numberPersontimbreList());
    }


    @GetMapping("/script/export/{taskId}")
    @Operation(summary = "导出视频合成脚本")
    @HasMemberCheck
    public void exportSynthesisScript(@PathVariable Integer taskId, HttpServletResponse response) throws Exception {
        videoSynthesisService.exportSynthesisScript(taskId, response);
    }


    @GetMapping("/download/segments/{taskId}")
    @Operation(summary = "下载合成分段视频列表")
    @HasMemberCheck
    public RespBody<List<String>> downloadSegmentVideos(@PathVariable Integer taskId) {
        List<String> downloadUrls = videoSynthesisService.getSegmentVideoDownloadUrls(taskId);
        return RespBody.ok(downloadUrls);
    }


    @GetMapping("/download/{taskId}")
    @Operation(summary = "下载视频合成结果")
    @HasMemberCheck
    public RespBody<String> downloadSynthesisResult(@PathVariable Integer taskId) {
        String downloadUrl = videoSynthesisService.getDownloadUrl(taskId);
        return RespBody.ok(downloadUrl);
    }

    @PostMapping("task/copy")
    @Operation(summary = "复制视频合成任务")
    public RespBody<Integer> copyTask(Integer taskId) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        return RespBody.ok(videoSynthesisService.copySynthesisTask(taskId, userId, tenantId));
    }

    @PostMapping("update/storyboard")
    @Operation(summary = "更新分镜的视频接口")
    public RespBody<Boolean> updateStoryboard(@RequestBody StoryBoardUpdateRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        return RespBody.ok(videoEditService.updateStoryboard(request, userId, tenantId));
    }

    @PostMapping("add/userAddVideo")
    @Operation(summary = "添加用户上传视频接口")
    public RespBody<Boolean> updateUserAddVideo(@RequestBody UserAddVideoRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        return RespBody.ok(videoEditService.userAddVideo(request, userId, tenantId));
    }

    @PostMapping("delete/video")
    @Operation(summary = "删除视频接口")
    public RespBody<Boolean> deleteVideo(@RequestBody DeleteVideoRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        return RespBody.ok(videoEditService.deleteVideo(request, userId, tenantId));
    }


    @GetMapping("/dub/list")
    @Operation(summary = "音效列表获取接口")
    public RespBody<List<VideoEditDubVO>> dubList(String type) {
        return RespBody.ok(videoEditService.dubList(type));
    }

    @GetMapping("/stickers/list")
    @Operation(summary = "贴图列表获取接口")
    public RespBody<List<VideoEditStickersVO>> stickersList() {
        return RespBody.ok(videoEditService.stickersList());
    }

}
