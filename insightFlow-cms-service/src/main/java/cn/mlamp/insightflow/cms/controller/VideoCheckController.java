package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.common.resp.RespCode;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.model.dto.ShumeiVideoCheckCallbackDTO;
import cn.mlamp.insightflow.cms.model.query.VideoCheckRequest;
import cn.mlamp.insightflow.cms.model.vo.VideoCheckTaskVO;
import cn.mlamp.insightflow.cms.service.IVideoCheckService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 视频检查控制器
 *
 * <AUTHOR> system
 * @date : 2025-6-23
 */
@Slf4j
@RequestMapping("/video-check")
@RestController
@RequiredArgsConstructor
@Tag(name = "视频检查相关接口")
public class VideoCheckController {

    private final IVideoCheckService videoCheckService;

    @PostMapping("/submit")
    @Operation(summary = "提交视频检查任务")
    public RespBody<VideoCheckTaskVO> submitVideoCheck(@RequestBody VideoCheckRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        request.setUserId(userId);
        request.setTenantId(tenantId);

        VideoCheckTaskVO result = videoCheckService.submitVideoCheck(request);
        return RespBody.ok(result);
    }

    @PostMapping("/callback")
    @Operation(summary = "数美视频检查回调接口")
    public RespBody<String> handleCallback(@RequestBody ShumeiVideoCheckCallbackDTO callbackDTO) {
        log.info("收到数美视频检查回调，btId: {}, requestId: {}, riskLevel: {}", callbackDTO.getBtId(), callbackDTO.getRequestId(),
                callbackDTO.getRiskLevel());

        try {
            videoCheckService.handleCallback(callbackDTO);
            return RespBody.ok("success");
        } catch (Exception e) {
            log.error("处理视频检查回调失败", e);
            return RespBody.fail(RespCode.INTERNAL_SERVER_ERROR, "处理回调失败: " + e.getMessage(), null);
        }
    }

}
