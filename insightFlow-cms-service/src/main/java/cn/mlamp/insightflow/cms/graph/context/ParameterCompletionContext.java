package cn.mlamp.insightflow.cms.graph.context;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 参数补全上下文类
 * 封装参数补全过程中的所有状态信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ParameterCompletionContext {

    /**
     * 目标执行节点名称
     */
    private String targetNode;

    /**
     * 原始任务目标描述
     */
    private String originalGoal;

    /**
     * 缺失的参数规格列表
     */
    private List<ParameterSpec> missingParams;

    /**
     * 已存在的参数集合
     */
    @Builder.Default
    private Map<String, Object> existingParams = new HashMap<>();

    /**
     * 当前补全状态
     */
    @Builder.Default
    private CompletionStatus status = CompletionStatus.AWAITING_INPUT;

    /**
     * 补全开始时间
     */
    private Date startTime;

    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;

    /**
     * 用户输入历史（用于上下文理解）
     */
    @Builder.Default
    private List<String> inputHistory = List.of();

    /**
     * 补全过程中的提示信息
     */
    private String currentPrompt;

    /**
     * 创建参数补全上下文的便捷方法
     */
    public static ParameterCompletionContext create(String targetNode, String originalGoal,
                                                    List<ParameterSpec> missingParams,
                                                    Map<String, Object> existingParams) {
        return ParameterCompletionContext.builder()
                .targetNode(targetNode)
                .originalGoal(originalGoal)
                .missingParams(missingParams)
                .existingParams(existingParams != null ? new HashMap<>(existingParams) : new HashMap<>())
                .startTime(new Date())
                .lastUpdateTime(new Date())
                .build();
    }

    /**
     * 检查是否还有缺失的必需参数
     */
    public boolean hasRequiredMissingParams() {
        if (missingParams == null || missingParams.isEmpty()) {
            return false;
        }
        return missingParams.stream().anyMatch(ParameterSpec::isRequired);
    }

    /**
     * 获取所有参数（已存在的 + 默认值）
     */
    public Map<String, Object> getAllParams() {
        Map<String, Object> allParams = new HashMap<>(existingParams);

        if (missingParams != null) {
            missingParams.stream()
                    .filter(param -> param.getDefaultValue() != null)
                    .filter(param -> !allParams.containsKey(param.getName()))
                    .forEach(param -> allParams.put(param.getName(), param.getDefaultValue()));
        }

        return allParams;
    }

    /**
     * 添加新的参数值
     */
    public void addParameter(String name, Object value) {
        if (existingParams == null) {
            existingParams = new HashMap<>();
        }
        existingParams.put(name, value);

        // 从缺失参数列表中移除已补全的参数
        if (missingParams != null) {
            missingParams.removeIf(param -> param.getName().equals(name));
        }

        lastUpdateTime = new Date();
    }

    /**
     * 批量添加参数
     */
    public void addParameters(Map<String, Object> params) {
        if (params != null && !params.isEmpty()) {
            params.forEach(this::addParameter);
        }
    }

    /**
     * 检查参数补全是否完成
     */
    public boolean isCompleted() {
        return status == CompletionStatus.COMPLETED || !hasRequiredMissingParams();
    }

    /**
     * 检查是否被取消
     */
    public boolean isCancelled() {
        return status == CompletionStatus.CANCELLED;
    }

    /**
     * 标记为完成状态
     */
    public void markCompleted() {
        this.status = CompletionStatus.COMPLETED;
        this.lastUpdateTime = new Date();
    }

    /**
     * 标记为取消状态
     */
    public void markCancelled() {
        this.status = CompletionStatus.CANCELLED;
        this.lastUpdateTime = new Date();
    }

    /**
     * 更新当前提示信息
     */
    public void updatePrompt(String prompt) {
        this.currentPrompt = prompt;
        this.lastUpdateTime = new Date();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("参数补全上下文:\n");
        sb.append("- 目标节点: ").append(targetNode).append("\n");
        sb.append("- 原始目标: ").append(originalGoal).append("\n");
        sb.append("- 补全状态: ").append(status).append("\n");

        if (existingParams != null && !existingParams.isEmpty()) {
            sb.append("- 已有参数: ").append(existingParams.size()).append("个\n");
        }

        if (missingParams != null && !missingParams.isEmpty()) {
            sb.append("- 缺失参数: ").append(missingParams.size()).append("个\n");
        }

        return sb.toString();
    }

    /**
     * 参数补全状态枚举
     */
    public enum CompletionStatus {
        /**
         * 等待用户输入
         */
        AWAITING_INPUT,

        /**
         * 正在处理用户输入
         */
        PROCESSING,

        /**
         * 参数补全完成
         */
        COMPLETED,

        /**
         * 用户取消补全
         */
        CANCELLED,

        /**
         * 补全过程出错
         */
        ERROR
    }

    /**
     * 参数类型枚举
     */
    public enum ParameterType {
        STRING,         // 文本类型
        INTEGER,        // 整数类型
        DECIMAL,        // 小数类型
        BOOLEAN,        // 布尔类型
        DATE,           // 日期类型
        DATE_RANGE,     // 日期范围
        ENUM,           // 枚举类型
        ARRAY,          // 数组类型
        OBJECT,         // 对象类型
        FILE_ID,        // 文件ID
        URL,            // URL链接
        EMAIL,          // 邮箱地址
        PHONE,          // 电话号码
        JSON            // JSON对象
    }

    /**
     * 参数规格定义
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ParameterSpec {
        /**
         * 参数名称
         */
        private String name;

        /**
         * 显示名称
         */
        private String displayName;

        /**
         * 参数描述
         */
        private String description;

        /**
         * 参数类型
         */
        private ParameterType type;

        /**
         * 是否必填
         */
        private boolean required;

        /**
         * 默认值
         */
        private Object defaultValue;

        /**
         * 验证正则表达式
         */
        private String pattern;

        /**
         * 数值范围
         */
        private Range range;

        /**
         * 枚举值列表
         */
        private List<String> enumValues;

        /**
         * 参数示例
         */
        private String example;

        /**
         * 参数提示信息
         */
        private String hint;
    }

    /**
     * 数值范围定义
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Range {
        private Double min;
        private Double max;
        private boolean minInclusive = true;
        private boolean maxInclusive = true;

        public boolean isInRange(Number value) {
            if (value == null) return false;
            double val = value.doubleValue();

            if (min != null) {
                if (minInclusive && val < min) return false;
                if (!minInclusive && val <= min) return false;
            }

            if (max != null) {
                if (maxInclusive && val > max) return false;
                if (!maxInclusive && val >= max) return false;
            }

            return true;
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            if (min != null) {
                sb.append(minInclusive ? "[" : "(").append(min);
            } else {
                sb.append("(-∞");
            }
            sb.append(", ");
            if (max != null) {
                sb.append(max).append(maxInclusive ? "]" : ")");
            } else {
                sb.append("+∞)");
            }
            return sb.toString();
        }
    }
} 