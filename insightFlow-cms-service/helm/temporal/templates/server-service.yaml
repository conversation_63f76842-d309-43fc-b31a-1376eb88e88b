{{- if $.Values.server.enabled }}
{{- range $rawService := (list "frontend" "internalFrontend") }}
{{- $service := include "serviceName" (list $rawService) }}
{{- $serviceValues := index $.Values.server $rawService }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "temporal.componentname" (list $ $service) }}
  labels:
    {{- include "temporal.resourceLabels" (list $ $service "") | nindent 4 }}
  {{- if $serviceValues.service.annotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $serviceValues.service.annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  type: {{ $serviceValues.service.type }}
  ports:
    - port: {{ $serviceValues.service.port }}
      targetPort: rpc
      protocol: TCP
      name: grpc-rpc
      {{- if hasKey $serviceValues.service "nodePort" }}
      nodePort: {{ $serviceValues.service.nodePort }}
      {{- end }}
    - port: {{ $serviceValues.service.httpPort }}
      targetPort: http
      protocol: TCP
      name: http
      # TODO: Allow customizing the node HTTP port
  selector:
    app.kubernetes.io/name: {{ include "temporal.name" $ }}
    app.kubernetes.io/instance: {{ $.Release.Name }}
    app.kubernetes.io/component: {{ $service }}

---
{{- end }}
{{- end }}

{{- if $.Values.server.enabled }}
{{- range $rawService := (list "frontend" "internalFrontend" "matching" "history" "worker") }}
{{- $service := include "serviceName" (list $rawService) }}
{{- $serviceValues := index $.Values.server $rawService }}
{{- if or (not (hasKey $serviceValues "enabled")) $serviceValues.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "temporal.componentname" (list $ (printf "%s-headless" $service)) }}
  labels:
    {{- include "temporal.resourceLabels" (list $ $service "") | nindent 4 }}
    app.kubernetes.io/headless: 'true'
  annotations:
    # Use this annotation in addition to the actual field below because the
    # annotation will stop being respected soon but the field is broken in
    # some versions of Kubernetes:
    # https://github.com/kubernetes/kubernetes/issues/58662
    service.alpha.kubernetes.io/tolerate-unready-endpoints: "true"
    {{- if (dig "metrics" "annotations" "enabled" $.Values.server.metrics.annotations.enabled $serviceValues) }}
    prometheus.io/job: {{ $.Chart.Name }}-{{ $service }}
    prometheus.io/scrape: 'true'
    prometheus.io/scheme: http
    prometheus.io/port: "9090"
    {{- end }}

spec:
  type: ClusterIP
  clusterIP: None
  publishNotReadyAddresses: true
  # For Istio service mesh - make sure all ports are defined here and in the deployment:
  # https://istio.io/latest/docs/ops/configuration/traffic-management/traffic-routing/#headless-services
  # Also for Istio - make sure to set the `appProtocol` property, see:
  # https://istio.io/latest/docs/ops/configuration/traffic-management/protocol-selection/#explicit-protocol-selection
  # Note that only the monitoring port is used for discovery (by prometheus).
  # The other ports are listed here solely to allow Istio to configure itself to intercept traffic.
  # https://istio.io/latest/docs/ops/configuration/traffic-management/traffic-routing/#headless-services
  ports:
    - port: {{ $serviceValues.service.port }}
      targetPort: rpc
      protocol: TCP
      name: grpc-rpc
    - port: {{ $serviceValues.service.membershipPort }}
      targetPort: membership
      protocol: TCP
      name: grpc-membership
    - port: 9090
      targetPort: metrics
      protocol: TCP
      name: metrics
  selector:
    app.kubernetes.io/name: {{ include "temporal.name" $ }}
    app.kubernetes.io/instance: {{ $.Release.Name }}
    app.kubernetes.io/component: {{ $service }}

---
{{- end }}
{{- end }}
{{- end }}