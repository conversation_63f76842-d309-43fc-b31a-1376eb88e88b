package cn.mlamp.insightflow.cms.entity.dam;

import cn.mlamp.insightflow.cms.util.mybatis.pg.FloatArrayTypeHandler;
import cn.mlamp.insightflow.cms.util.mybatis.pg.JsonbTypeHandler;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;
import java.util.Map;

@Data
@TableName("cms_asset_embeddings")
public class DamAssetEmbedding {
    @TableId(value = "id", type = IdType.INPUT)
    private Integer id;
    private Integer tenantId;
    private Integer userId;
    private Integer directoryId;
    private String aspectRatio;
    private String content;
    @TableField(typeHandler = FloatArrayTypeHandler.class)
    private Float[] embedding; // pgvector字段
    @TableField(typeHandler = JsonbTypeHandler.class)
    private Map<String, Object> metadata; // jsonb字段
    private Date createTime;
    private Date updateTime;
    @TableLogic
    private Integer isDeleted = 0;
    @TableField(exist = false)
    private Float distance;
    private Integer duration;

    //是否已更新（洗数据专用）0-未更新，1-已更新
    private Integer isFresh;
}