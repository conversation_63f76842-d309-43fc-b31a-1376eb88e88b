{{ if .Values.imageRenderer.enabled }}
{{- $root := . -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "grafana.fullname" . }}-image-renderer
  namespace: {{ include "grafana.namespace" . }}
  labels:
    {{- include "grafana.imageRenderer.labels" . | nindent 4 }}
    {{- with .Values.imageRenderer.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.imageRenderer.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if and (not .Values.imageRenderer.autoscaling.enabled) (.Values.imageRenderer.replicas) }}
  replicas: {{ .Values.imageRenderer.replicas }}
  {{- end }}
  revisionHistoryLimit: {{ .Values.imageRenderer.revisionHistoryLimit }}
  selector:
    matchLabels:
      {{- include "grafana.imageRenderer.selectorLabels" . | nindent 6 }}

  {{- with .Values.imageRenderer.deploymentStrategy }}
  strategy:
    {{- toYaml . | trim | nindent 4 }}
  {{- end }}
  template:
    metadata:
      labels:
        {{- include "grafana.imageRenderer.selectorLabels" . | nindent 8 }}
        {{- with .Values.imageRenderer.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        {{- with .Values.imageRenderer.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imageRenderer.schedulerName }}
      schedulerName: "{{ . }}"
      {{- end }}
      {{- with .Values.imageRenderer.serviceAccountName }}
      serviceAccountName: "{{ . }}"
      {{- end }}
      {{- with .Values.imageRenderer.hostAliases }}
      hostAliases:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.imageRenderer.priorityClassName }}
      priorityClassName: {{ . }}
      {{- end }}
      {{- with .Values.imageRenderer.image.pullSecrets }}
      imagePullSecrets:
        {{- range . }}
        - name: {{ tpl . $root }}
        {{- end}}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}-image-renderer
          {{- $registry := .Values.global.imageRegistry | default .Values.imageRenderer.image.registry -}}
          {{- if .Values.imageRenderer.image.sha }}
          image: "{{ $registry }}/{{ .Values.imageRenderer.image.repository }}:{{ .Values.imageRenderer.image.tag }}@sha256:{{ .Values.imageRenderer.image.sha }}"
          {{- else }}
          image: "{{ $registry }}/{{ .Values.imageRenderer.image.repository }}:{{ .Values.imageRenderer.image.tag }}"
          {{- end }}
          imagePullPolicy: {{ .Values.imageRenderer.image.pullPolicy }}
          {{- if .Values.imageRenderer.command }}
          command:
            {{- range .Values.imageRenderer.command }}
            - {{ . }}
            {{- end }}
          {{- end}}
          ports:
            - name: {{ .Values.imageRenderer.service.portName }}
              containerPort: {{ .Values.imageRenderer.service.targetPort }}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: {{ .Values.imageRenderer.service.portName }}
          env:
            - name: HTTP_PORT
              value: {{ .Values.imageRenderer.service.targetPort | quote }}
          {{- if .Values.imageRenderer.serviceMonitor.enabled }}
            - name: ENABLE_METRICS
              value: "true"
          {{- end }}
          {{- range $key, $value := .Values.imageRenderer.envValueFrom }}
            - name: {{ $key | quote }}
              valueFrom:
                {{- tpl (toYaml $value) $ | nindent 16 }}
          {{- end }}
          {{- range $key, $value := .Values.imageRenderer.env }}
            - name: {{ $key | quote }}
              value: {{ $value | quote }}
          {{- end }}
          volumeMounts:
            - mountPath: /tmp
              name: image-renderer-tmpfs
          {{- with .Values.imageRenderer.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
      {{- with .Values.imageRenderer.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.imageRenderer.affinity }}
      affinity:
        {{- tpl (toYaml .) $root | nindent 8 }}
      {{- end }}
      {{- with .Values.imageRenderer.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
        - name: image-renderer-tmpfs
          emptyDir: {}
{{- end }}
