package cn.mlamp.insightflow.cms.enums.analysis;

import cn.mlamp.insightflow.cms.entity.CmsPullTaskDedupedData;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 视频来源平台，对应 {@link CmsPullTaskDedupedData#getKwSource()} 信息
 *
 * <AUTHOR>
 */
public enum SearchTypeEnum implements IEnum<String> {

    BRAND("brand"),
    PRODUCT("product"),
    ;
    private final String value;

    SearchTypeEnum(String value) {
        this.value = value;
    }

    @Override
    public String getValue() {
        return value;
    }

    /**
     * 兼容历史
     *
     * @param value value
     * @return PlatformEnum
     */
    @JsonCreator
    public static SearchTypeEnum from(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        value = value.toLowerCase();
        for (SearchTypeEnum type : SearchTypeEnum.values()) {
            if (Objects.equals(type.getValue(), value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown PlatformEnum value: " + value);
    }
}
