package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 用户表;
 *
 * <AUTHOR> re<PERSON><PERSON><PERSON>
 * @date : 2025-05-12
 */
@Data
@TableName("cms_subject_info")
public class CmsSubjectInfo extends BaseEntity {
    /**
     * 主键 自增id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 主体id
     */
    private Integer subjectId;

    /**
     * 主体姓名
     */
    private String subjectName;

    /**
     * 主体标签
     */
    private String label;

    /**
     * 主体可见性: 0 个人可见 1 租户可见
     */
    private Integer visibility;

    /**
     * 主体风格
     */
    private String style;

    /**
     * 主体描述
     */
    private String description;

    /**
     * 创建人id
     */
    private Integer userId;

    /**
     * 租户Id
     */
    private Integer tenantId;


}