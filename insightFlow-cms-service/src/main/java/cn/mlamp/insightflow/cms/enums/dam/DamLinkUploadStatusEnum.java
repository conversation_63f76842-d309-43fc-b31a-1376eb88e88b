package cn.mlamp.insightflow.cms.enums.dam;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum DamLinkUploadStatusEnum implements IEnum<Integer> {
    TO_DOWNLOAD(0, "待下载"),
    DOWNLOADING(1, "下载中"),
    SUCCESS(2, "下载成功"),
    FAILED(3, "下载失败"),
    COMPLETED(4, "完成");


    @JsonValue
    private final Integer value;
    private final String desc;

    DamLinkUploadStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    @JsonCreator
    public static DamLinkUploadStatusEnum from(Integer value) {
        if (value == null) {
            return null;
        }
        for (DamLinkUploadStatusEnum status : DamLinkUploadStatusEnum.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown CmsLinkUploadStatusEnum value: " + value);
    }
}

