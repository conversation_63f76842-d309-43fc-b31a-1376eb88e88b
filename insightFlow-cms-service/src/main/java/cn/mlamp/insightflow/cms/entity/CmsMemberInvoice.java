package cn.mlamp.insightflow.cms.entity;

import cn.mlamp.insightflow.cms.enums.InvoiceStatusEnum;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("cms_member_invoice")
public class CmsMemberInvoice {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer tenantId;
    private Integer userId;
    private Integer orderId;

    /**
     * {@link InvoiceStatusEnum#getStatus()}
     */
    private String status;
    private BigDecimal price;
    /**
     * {@link cn.mlamp.insightflow.cms.enums.InvoiceTypeEnum#getType()}
     */
    private String type;
    private String title;
    private String taxpayerId;
    private String email;
    private String registeredAddress;
    private String registeredPhoneNumber;
    private String bankName;
    private String bankAccount;

    // 数据逻辑删除标记 0:未删除 1:已删除
    private Integer isDeleted;
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime updateTime;
}
