server:
  config:
    persistence:
      default:
        driver: "sql"

        sql:
          driver: "mysql8"
          host: **********
          port: 3311
          database: temporal
          user: temporal
          password: RUEApvtjOk
          maxConns: 200
          maxIdleConns: 200
          maxConnLifetime: "1h"

      visibility:
        driver: "sql"

        sql:
          driver: "mysql8"
          host: **********
          port: 3311
          database: temporal_visibility
          user: temporal
          password: RUEApvtjOk
          maxConns: 200
          maxIdleConns: 200
          maxConnLifetime: "1h"

cassandra:
  enabled: false

mysql:
  enabled: true

postgresql:
  enabled: false

schema:
  createDatabase:
    enabled: true
  setup:
    enabled: true
  update:
    enabled: true
