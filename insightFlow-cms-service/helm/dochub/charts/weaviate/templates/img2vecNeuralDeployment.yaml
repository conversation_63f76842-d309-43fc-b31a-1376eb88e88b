{{ if index .Values "modules" "img2vec-neural" "enabled" }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ index .Values "modules" "img2vec-neural" "fullnameOverride" }}
  labels:
    name: {{ index .Values "modules" "img2vec-neural" "fullnameOverride" }}
    app: {{ index .Values "modules" "img2vec-neural" "fullnameOverride" }}
    app.kubernetes.io/name: weaviate
    app.kubernetes.io/managed-by: helm
spec:
  replicas: {{ index .Values "modules" "img2vec-neural" "replicas" }}
  selector:
    matchLabels:
      app: {{ index .Values "modules" "img2vec-neural" "fullnameOverride" }}
  template:
    metadata:
      labels:
        app: {{ index .Values "modules" "img2vec-neural" "fullnameOverride" }}
    spec:
      {{- if (index .Values "modules" "img2vec-neural" "serviceAccountName") }}
      serviceAccountName: {{ index .Values "modules" "img2vec-neural" "serviceAccountName" }}
      {{- else if (index .Values "serviceAccountName") }}
      serviceAccountName: {{ index .Values "serviceAccountName" }}
      {{- end }}
      containers:
      - name: {{ index .Values "modules" "img2vec-neural" "fullnameOverride" }}
        image: {{ index .Values "modules" "img2vec-neural" "registry" }}/{{ index .Values "modules" "img2vec-neural" "repo" }}:{{ index .Values "modules" "img2vec-neural" "tag" }}
        env:
          - name: ENABLE_CUDA
            value: "{{ if index .Values "modules" "img2vec-neural" "envconfig" "enable_cuda" }}1{{ else }}0{{ end }}"
          {{ if index .Values "modules" "img2vec-neural" "envconfig" "enable_cuda" }}
          - name: NVIDIA_VISIBLE_DEVICES
            value: {{ index .Values "modules" "img2vec-neural" "envconfig" "nvidia_visible_devices"}}
          - name: NVIDIA_DRIVER_CAPABILITIES
            value: {{ index .Values "modules" "img2vec-neural" "envconfig" "nvidia_driver_capabilities"}}
          - name: LD_LIBRARY_PATH
            value: {{ index .Values "modules" "img2vec-neural" "envconfig" "ld_library_path"}}
          {{ end }}
        resources:
{{ index .Values "modules" "img2vec-neural" "resources" | toYaml | indent 10 }}
        livenessProbe:
          httpGet:
            path: /.well-known/live
            port: 8080
          initialDelaySeconds: {{ index .Values "modules" "img2vec-neural" "livenessProbe" "initialDelaySeconds" }}
          periodSeconds: {{ index .Values "modules" "img2vec-neural" "livenessProbe" "periodSeconds" }}
          timeoutSeconds: {{ index .Values "modules" "img2vec-neural" "livenessProbe" "timeoutSeconds" }}
        readinessProbe:
          httpGet:
            path: /.well-known/ready
            port: 8080
          initialDelaySeconds: {{ index .Values "modules" "img2vec-neural" "readinessProbe" "initialDelaySeconds" }}
          periodSeconds: {{ index .Values "modules" "img2vec-neural" "readinessProbe" "periodSeconds" }}
      {{- with index .Values "modules" "img2vec-neural" "nodeSelector" | default .Values.nodeSelector }}
      nodeSelector:
        {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with index .Values "modules" "img2vec-neural" "affinity" | default .Values.affinity }}
      affinity:
        {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with index .Values "modules" "img2vec-neural" "tolerations" | default .Values.tolerations }}
      tolerations:
        {{ toYaml . | nindent 8 }}
      {{- end }}
{{ end }}
