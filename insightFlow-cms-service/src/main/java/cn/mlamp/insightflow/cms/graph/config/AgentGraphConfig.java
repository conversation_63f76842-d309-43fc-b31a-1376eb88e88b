package cn.mlamp.insightflow.cms.graph.config;

import cn.mlamp.insightflow.cms.graph.CMSGraphFactory;
import cn.mlamp.insightflow.cms.graph.CMSGraphState;
import cn.mlamp.insightflow.cms.graph.helper.PromptHelper;
import com.langfuse.client.LangfuseClient;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.StreamingChatModel;
import dev.langchain4j.model.chat.listener.ChatModelErrorContext;
import dev.langchain4j.model.chat.listener.ChatModelListener;
import dev.langchain4j.model.chat.listener.ChatModelRequestContext;
import dev.langchain4j.model.chat.listener.ChatModelResponseContext;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.model.openai.OpenAiChatRequestParameters;
import dev.langchain4j.model.openai.OpenAiStreamingChatModel;
import lombok.extern.slf4j.Slf4j;
import org.bsc.langgraph4j.CompiledGraph;
import org.bsc.langgraph4j.GraphStateException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import java.util.List;
import java.util.Map;

@Slf4j
@Configuration
public class AgentGraphConfig {

    @Bean
    public ChatModel chatModel(LLMGatewayProperties llmGatewayProperties) {
        final String modelName = llmGatewayProperties.getModel();
        final Map<String, LLMGatewayProperties.ModelParameter> parameters = llmGatewayProperties.getParameters();
        final LLMGatewayProperties.ModelParameter parameter = parameters.getOrDefault(modelName, new LLMGatewayProperties.ModelParameter());
        return OpenAiChatModel.builder()
                .baseUrl(llmGatewayProperties.getBaseUrl())
                .apiKey(llmGatewayProperties.getApiKey())
                .modelName(modelName)
                .defaultRequestParameters(
                        OpenAiChatRequestParameters.builder()
                                .modelName(modelName)
                                .temperature(parameter.getTemperature())
                                .maxCompletionTokens(parameter.getMaxTokens())
                                .build()
                )
                .listeners(List.of(new ChatModelListener() {
                    @Override
                    public void onError(ChatModelErrorContext errorContext) {
                        log.error("Error: {}", errorContext.error().getMessage());
                    }

                    @Override
                    public void onResponse(ChatModelResponseContext responseContext) {
                        log.info("Response: {}", responseContext.chatResponse().toString());
                    }

                    @Override
                    public void onRequest(ChatModelRequestContext requestContext) {
                        log.info("Request: {}", requestContext.chatRequest().toString());
                    }
                }))
                .build();
    }

    @Bean
    public StreamingChatModel agentChatModel(LLMGatewayProperties llmGatewayProperties) {
        return OpenAiStreamingChatModel.builder()
                .baseUrl(llmGatewayProperties.getBaseUrl())
                .apiKey(llmGatewayProperties.getApiKey())
                .modelName(llmGatewayProperties.getModel())
                .build();
    }

    @ConditionalOnBean(LangfuseProperties.class)
    @Bean
    public LangfuseClient langfuseClient(LangfuseProperties langfuseProperties) {
        return LangfuseClient.builder()
                .url(langfuseProperties.getUrl())
                .credentials(langfuseProperties.getPublicKey(), langfuseProperties.getSecretKey())
                .xLangfusePublicKey(langfuseProperties.getPublicKey())
                .build();
    }

    @ConditionalOnBean(PromptHelper.class)
    @Bean
    public CompiledGraph<CMSGraphState> graph(CMSGraphFactory graphFactory) throws GraphStateException {
        log.info("开始初始化CMS Agent Graph...");

        // 使用工厂创建标准图
        final CompiledGraph<CMSGraphState> compiledGraph = graphFactory.createStandardGraph();

        log.info("CMS Agent Graph初始化完成");
        return compiledGraph;
    }

}
