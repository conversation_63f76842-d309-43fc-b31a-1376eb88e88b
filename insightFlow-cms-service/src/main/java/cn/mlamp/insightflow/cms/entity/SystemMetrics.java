package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 后台系统指标统计表
 */
@Data
@TableName("cms_system_metrics")
public class SystemMetrics extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Integer id; // 主键
    private Date recordDate; // 统计日期
    private Long activeUsers; // 活跃用户数
    private Long totalUsers; // 累计用户数
    private Long totalTenants; // 累计租户数
    private Long newUsers; // 新增用户数
    private Long newTenants; //新增租户数
    private Long storyboardTasks; // 分镜拆解任务总完成量
    private Long scriptTasks; // 脚本生成任务总完成量
    private Long videoOptimizeTasks; // 音视频优化任务总完成量
    private Double avgStoryboardPerTenant; // 租户均分镜拆解任务完成量
    private Double avgScriptPerTenant; // 租户均脚本生成任务完成量
    private Double avgVideoOptimizePerTenant; // 租户均音视频优化完成任务量
    private Double popularToScriptRate; // 爆款视频页面到脚本生成页面转化率(%)
    private Double popularCompleteRate; // 爆款视频页面整体转化率(%)
    private Double golden3sToScriptRate; // 黄金3秒页面到脚本生成页面转化率(%)
    private Double golden3sCompleteRate; // 黄金3秒页面整体转化率(%)
    private Integer avgCompleteTasksPerTenant; // 租户均完整任务完成量
    private Double aiScriptFailureRate; // AI生成脚本任务失败率(%)
    private Double personalUploadSuccessRate; // 个人上传视频解析成功率(%)
    private Double linkUploadSuccessRate; // 链接上传视频解析成功率(%)
    private Date createTime; // 记录创建时间
    private Date updateTime; // 记录更新时间
    private Integer isDeleted; // 数据逻辑删除标记


}