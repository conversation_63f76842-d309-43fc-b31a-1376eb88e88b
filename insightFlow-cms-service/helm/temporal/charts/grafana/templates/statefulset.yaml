{{- $sts := list "sts" "StatefulSet" "statefulset" -}}
{{- if (or (.Values.useStatefulSet) (and .Values.persistence.enabled (not .Values.persistence.existingClaim) (has .Values.persistence.type $sts)))}}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "grafana.fullname" . }}
  namespace: {{ include "grafana.namespace" . }}
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
  {{- with .Values.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.replicas }}
  selector:
    matchLabels:
      {{- include "grafana.selectorLabels" . | nindent 6 }}
  serviceName: {{ include "grafana.fullname" . }}-headless
  template:
    metadata:
      labels:
        {{- include "grafana.selectorLabels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        checksum/dashboards-json-config: {{ include (print $.Template.BasePath "/dashboards-json-configmap.yaml") . | sha256sum }}
        checksum/sc-dashboard-provider-config: {{ include (print $.Template.BasePath "/configmap-dashboard-provider.yaml") . | sha256sum }}
        {{- if and (or (and (not .Values.admin.existingSecret) (not .Values.env.GF_SECURITY_ADMIN_PASSWORD__FILE) (not .Values.env.GF_SECURITY_ADMIN_PASSWORD)) (and .Values.ldap.enabled (not .Values.ldap.existingSecret))) (not .Values.env.GF_SECURITY_DISABLE_INITIAL_ADMIN_CREATION) }}
        checksum/secret: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
        {{- end }}
        kubectl.kubernetes.io/default-container: {{ .Chart.Name }}
        {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- include "grafana.pod" . | nindent 6 }}
  {{- if .Values.persistence.enabled}}
  volumeClaimTemplates:
  - metadata:
      name: storage
    spec:
      accessModes: {{ .Values.persistence.accessModes }}
      storageClassName: {{ .Values.persistence.storageClassName }}
      resources:
        requests:
          storage: {{ .Values.persistence.size }}
      {{- with .Values.persistence.selectorLabels }}
      selector:
        matchLabels:
          {{- toYaml . | nindent 10 }}
      {{- end }}
  {{- end }}
{{- end }}
