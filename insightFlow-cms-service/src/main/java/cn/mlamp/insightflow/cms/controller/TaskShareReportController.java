package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.model.query.TaskShareRequest;
import cn.mlamp.insightflow.cms.model.vo.GetVideoValueTaskVO;
import cn.mlamp.insightflow.cms.model.vo.TaskShareDetailVO;
import cn.mlamp.insightflow.cms.model.vo.TaskShareVO;
import cn.mlamp.insightflow.cms.model.vo.VideoTaskDetailVO;
import cn.mlamp.insightflow.cms.service.ITaskShareReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 任务分享控制器
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/task/share")
@Tag(name = "任务分享接口")
public class TaskShareReportController {

    private final ITaskShareReportService taskShareReportService;

    @PostMapping("/create")
    @Operation(summary = "创建任务分享")
    public RespBody<TaskShareVO> createTaskShare(@Valid @RequestBody TaskShareRequest request) {
        // 设置用户ID和租户ID
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        // 创建任务分享
        TaskShareVO result = taskShareReportService.createTaskShare(request, userId, tenantId);
        return RespBody.ok(result);
    }

    @PostMapping("/add/{code}")
    @Operation(summary = "加入分享到我的列表")
    public RespBody<Boolean> addTaskShare(@PathVariable("code") String code) {
        // 设置用户ID和租户ID
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        // 加入分享内容到我的列表
        boolean result = taskShareReportService.addTaskShare(code, userId, tenantId);
        return RespBody.ok(result);
    }
}
