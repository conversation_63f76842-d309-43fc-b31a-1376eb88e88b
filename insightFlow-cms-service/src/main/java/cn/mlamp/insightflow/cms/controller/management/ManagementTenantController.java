package cn.mlamp.insightflow.cms.controller.management;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.model.vo.ManagementTenantListVO;
import cn.mlamp.insightflow.cms.service.management.ManagementTenantService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/management/tenants")
@RequiredArgsConstructor
public class ManagementTenantController {
    private final ManagementTenantService managementTenantService;

    @GetMapping("/list")
    public RespBody<List<ManagementTenantListVO>> listTenants() {
        List<ManagementTenantListVO> vos = managementTenantService.listTenants();
        return RespBody.ok(vos);
    }
}
