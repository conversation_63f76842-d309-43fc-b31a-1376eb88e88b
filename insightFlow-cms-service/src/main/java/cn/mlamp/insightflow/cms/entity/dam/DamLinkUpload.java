package cn.mlamp.insightflow.cms.entity.dam;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 链接上传记录表
 */
@Data
@TableName("cms_link_upload_dam")
public class DamLinkUpload {

    @TableId(type = IdType.AUTO)
    // 主键ID
    private Integer id;

    // 第三方下载资源ID
    private String resourceId;

    // 任务ID
    private Integer taskId;

    // 标题
    private String title;

    // 链接地址
    private String linkUrl;

    //存储位置
    private String ossId;

    // 状态（0：待下载,1 ：下载中,2：待裁剪,3：待入库,4：完成,5：失败）
    private Integer status;

    //错误信息
    private String errorMsg;

    // 打标类型（0：手动切分,1：AI打标）
    private Integer tagType;

    //发帖时间
    private Date postTime;

    // 租户ID
    private Integer tenantId;

    // 创建者ID
    private Integer userId;

    // 创建时间
    private Date createTime;

    // 更新时间
    private Date updateTime;

    // 逻辑删除：0-未删除，1-已删除
    @TableField("is_deleted")
    private Integer isDeleted;
}

