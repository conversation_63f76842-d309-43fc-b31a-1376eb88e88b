{{- if .Values.backup.enabled }}
{{- if .Values.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "cassandra.serviceAccountName" . }}
  labels:
    app: {{ template "cassandra.name" . }}
    chart: {{ template "cassandra.chart" . }}
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
---
{{- end }}
{{- if .Values.rbac.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ template "cassandra.fullname" . }}-backup
  labels:
    app: {{ template "cassandra.name" . }}
    chart: {{ template "cassandra.chart" . }}
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
rules:
- apiGroups: [""]
  resources: ["pods", "pods/log"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["pods/exec"]
  verbs: ["create"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ template "cassandra.fullname" . }}-backup
  labels:
    app: {{ template "cassandra.name" . }}
    chart: {{ template "cassandra.chart" . }}
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ template "cassandra.fullname" . }}-backup
subjects:
- kind: ServiceAccount
  name: {{ template "cassandra.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
{{- end }}
{{- end }}
