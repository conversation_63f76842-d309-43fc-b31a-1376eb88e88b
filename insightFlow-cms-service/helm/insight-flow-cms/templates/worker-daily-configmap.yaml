apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "insight-flow-cms.worker.daily.fullname" . }}-config
data:
  application-prod.yml: |-
    spring:
      temporal:
        namespace: cms-prod
        connection:
          target: temporal-cms-frontend.ai-pc:7233
        workers:
          - name: common-worker
            task-queue: common
            capacity:
              max-concurrent-workflow-task-pollers: 1
              max-concurrent-workflow-task-executors: 5
              max-concurrent-activity-task-pollers: 5
              max-concurrent-activity-executors: 15
          - name: daily-worker
            task-queue: daily
            capacity:
              max-concurrent-workflow-task-pollers: 5
              max-concurrent-workflow-task-executors: 15
              max-concurrent-activity-task-pollers: 5
              max-concurrent-activity-executors: 40
              max-concurrent-local-activity-executors: 40
        workflow:
          task-timeout: PT1200S
          run-timeout: PT1200S
          execution-timeout: PT1200S
        workersAutoDiscovery:
          packages:
            - cn.mlamp.insightflow.cms.worker.workflows.analysis
    cms:
      task:
        common: true
        scheduler:
          daily:
            batch-size: 5
            max-running-workflow-size: 15
          customer:
            batch-size: 5
            max-running-workflow-size: 10
  application-oversea.yml: |-
    spring:
      temporal:
        namespace: cms-oversea
        connection:
          target: temporal-frontend.temporal.svc.cluster.local:7233
        workers:
          - name: common-worker
            task-queue: common
            capacity:
              max-concurrent-workflow-task-pollers: 1
              max-concurrent-workflow-task-executors: 5
              max-concurrent-activity-task-pollers: 5
              max-concurrent-activity-executors: 15
          - name: daily-worker
            task-queue: daily
            capacity:
              max-concurrent-workflow-task-pollers: 5
              max-concurrent-workflow-task-executors: 10
              max-concurrent-activity-task-pollers: 5
              max-concurrent-activity-executors: 30
        workflow:
          task-timeout: PT1200S
          run-timeout: PT1200S
          execution-timeout: PT1200S
        workersAutoDiscovery:
          packages:
            - cn.mlamp.insightflow.cms.worker.workflows.analysis
    cms:
      task:
        common: true
        scheduler:
          daily:
            batch-size: 5
            max-running-workflow-size: 25
          customer:
            batch-size: 5
            max-running-workflow-size: 10
  application-preview.yml: |-
    spring:
      temporal:
        namespace: cms-test
        connection:
          target: temporal-cms-frontend.ai-pc:7233
        workers:
          - name: common-worker
            task-queue: common
            capacity:
              max-concurrent-workflow-task-pollers: 1
              max-concurrent-workflow-task-executors: 5
              max-concurrent-activity-task-pollers: 5
              max-concurrent-activity-executors: 15
          - name: daily-worker
            task-queue: daily
            capacity:
              max-concurrent-workflow-task-pollers: 5
              max-concurrent-workflow-task-executors: 5
              max-concurrent-activity-task-pollers: 5
              max-concurrent-activity-executors: 20
        workflow:
          task-timeout: PT1200S
          run-timeout: PT1200S
          execution-timeout: PT1200S
        workersAutoDiscovery:
          packages:
            - cn.mlamp.insightflow.cms.worker.workflows.analysis
    cms:
      task:
        common: true
        scheduler:
          daily:
            batch-size: 5
            max-running-workflow-size: 25
          customer:
            batch-size: 5
            max-running-workflow-size: 10
  application-test.yml: |-
    spring:
      temporal:
        namespace: cms-test
        connection:
          target: temporal-cms-frontend.ai-pc:7233
        workers:
          - name: common-worker
            task-queue: common
            capacity:
              max-concurrent-workflow-task-pollers: 1
              max-concurrent-workflow-task-executors: 5
              max-concurrent-activity-task-pollers: 5
              max-concurrent-activity-executors: 15
          - name: daily-worker
            task-queue: daily
            capacity:
              max-concurrent-workflow-task-pollers: 5
              max-concurrent-workflow-task-executors: 5
              max-concurrent-activity-task-pollers: 5
              max-concurrent-activity-executors: 50
        workflow:
          task-timeout: PT1200S
          run-timeout: PT1200S
          execution-timeout: PT1200S
        workersAutoDiscovery:
          packages:
            - cn.mlamp.insightflow.cms.worker.workflows.analysis
    cms:
      task:
        common: true
        scheduler:
          daily:
            batch-size: 1
            max-running-workflow-size: 1
          customer:
            batch-size: 3
            max-running-workflow-size: 10
  application-ppt.yml: |-
    spring:
      temporal:
        namespace: cms-ppt
        connection:
          target: temporal-cms-frontend.ai-pc:7233
        workers:
          - name: common-worker
            task-queue: common
            capacity:
              max-concurrent-workflow-task-pollers: 1
              max-concurrent-workflow-task-executors: 5
              max-concurrent-activity-task-pollers: 5
              max-concurrent-activity-executors: 15
          - name: daily-worker
            task-queue: daily
            capacity:
              max-concurrent-workflow-task-pollers: 5
              max-concurrent-workflow-task-executors: 5
              max-concurrent-activity-task-pollers: 5
              max-concurrent-activity-executors: 20
          - name: customer-worker
            task-queue: customer
            capacity:
              max-concurrent-workflow-task-pollers: 5
              max-concurrent-workflow-task-executors: 5
              max-concurrent-activity-task-pollers: 5
              max-concurrent-activity-executors: 20
        workflow:
          task-timeout: PT1200S
          run-timeout: PT1200S
          execution-timeout: PT1200S
        workersAutoDiscovery:
          packages:
            - cn.mlamp.insightflow.cms.worker.analysis
    cms:
      task:
        common: true
        scheduler:
          daily:
            batch-size: 1
            max-running-workflow-size: 3
          customer:
            batch-size: 1
            max-running-workflow-size: 3
