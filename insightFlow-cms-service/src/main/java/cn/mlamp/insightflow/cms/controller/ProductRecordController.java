package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.annotation.HasMemberCheck;
import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.model.query.ProductImageUploadRequest;
import cn.mlamp.insightflow.cms.model.vo.ProductRecordCardVO;
import cn.mlamp.insightflow.cms.model.vo.TaskUploadVO;
import cn.mlamp.insightflow.cms.model.vo.UploadFileVo;
import cn.mlamp.insightflow.cms.model.vo.UploadProductVO;
import cn.mlamp.insightflow.cms.service.ICmsProductRecordService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

@Slf4j
@RequestMapping("/product/record")
@RestController
@RequiredArgsConstructor
@Tag(name = "产品记录相关接口")
public class ProductRecordController {

    private final ICmsProductRecordService productRecordService;

    @PostMapping("/upload/url")
    @Operation(summary = "上传商品链接获取信息")
    @Deprecated
    public RespBody<UploadProductVO> videoTaskProductUrl(@RequestBody TaskUploadVO uploadVO) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        return RespBody.ok(productRecordService.videoTaskProductUrl(uploadVO, userId, tenantId));
    }

    @PostMapping("/upload/url/info")
    @Operation(summary = "上传商品链接获取信息")
    @HasMemberCheck
    public RespBody<UploadProductVO> getProductInfoFromUrl(@RequestBody TaskUploadVO uploadVO) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        return RespBody.ok(productRecordService.getProductInfoFromUrl(uploadVO, userId, tenantId));
    }

    @PostMapping(value = "/upload/file", consumes = {"application/x-www-form-urlencoded", "multipart/form-data"})
    @Operation(summary = "上传商品文档获取信息")
    @HasMemberCheck
    public RespBody<UploadFileVo> videoProductFileUrl(@RequestParam(value = "file") MultipartFile file) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        return RespBody.ok(productRecordService.videoTaskUploadProductFile(file, userId, tenantId));
    }

    @PostMapping("/upload/presign")
    @Operation(summary = "获取商品图片上传预签名URL")
    @HasMemberCheck
    public RespBody<TaskUploadVO> getProductImageUploadPresignedUrl(@RequestBody ProductImageUploadRequest request) {
        if (request == null || request.getFilename() == null || request.getFilename().trim().isEmpty()) {
            throw new RuntimeException("文件名不能为空");
        }
        TaskUploadVO uploadVO = productRecordService.getProductImageUploadPresignedUrl(request.getFilename());
        return RespBody.ok(uploadVO);
    }

    @PostMapping("/create")
    @Operation(summary = "创建商品卡片")
    public RespBody<Integer> createProductCard(@Valid @RequestBody ProductRecordCardVO request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        var id = productRecordService.createProductCard(request, userId, tenantId);
        return RespBody.ok(id);
    }

    @PutMapping("/update")
    @Operation(summary = "更新商品卡片")
    public RespBody<ProductRecordCardVO> updateProductCard(@Valid @RequestBody ProductRecordCardVO request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        var detail = productRecordService.updateProductCard(request, userId, tenantId);
        return RespBody.ok(detail);
    }

    @GetMapping("/list")
    @Operation(summary = "历史商品列表")
    public RespBody<Page<UploadProductVO>> videoTaskProductList(@RequestParam(value = "current", defaultValue = "1") Integer current,
                                                                @RequestParam(value = "pageSize", defaultValue = "5") Integer pageSize,
                                                                @RequestParam(value = "keyword", required = false) String keyword) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        return RespBody.ok(productRecordService.videoTaskProductList(current, pageSize, userId, tenantId, keyword));
    }

    @GetMapping("/detail/{id}")
    @Operation(summary = "获取商品详情")
    public RespBody<ProductRecordCardVO> getProductDetail(@PathVariable("id") Integer id) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        ProductRecordCardVO detail = productRecordService.getProductCardDetail(id, userId, tenantId);
        return RespBody.ok(detail);
    }

    @PostMapping("/check/exists")
    @Operation(summary = "检测是否有相同商品")
    public RespBody<ProductRecordCardVO> checkProductExists(@RequestBody ProductRecordCardVO productInfo) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        ProductRecordCardVO existingProduct = productRecordService.checkProductExists(productInfo, userId, tenantId);
        return RespBody.ok(existingProduct);
    }

    @DeleteMapping("/{recordId}")
    @Operation(summary = "删除历史商品")
    public RespBody<Void> deleteProductRecord(@PathVariable("recordId") Integer recordId) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        productRecordService.deleteProductRecord(recordId, userId, tenantId);
        return RespBody.ok();
    }

    @DeleteMapping("/batch")
    @Operation(summary = "批量删除历史商品")
    public RespBody<Void> batchDeleteProductRecords(@Valid @RequestBody ProductRecordCardVO request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        productRecordService.batchDeleteProductCards(request, userId, tenantId);
        return RespBody.ok();
    }

    @PutMapping("/selling-points/update")
    @Operation(summary = "更新商品卡片卖点")
    public RespBody<Void> updateProductCardSellingPoints(@Valid @RequestBody ProductRecordCardVO request) {
        // Validate request
        if (request.getId() == null) {
            throw new BusinessException("商品ID不能为空");
        }

        if (request.getSellingPoints() == null || request.getSellingPoints().isEmpty()) {
            throw new BusinessException("卖点列表不能为空");
        }

        // Get user and tenant ID from context
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        // Call service method to update selling points with deduplication
        productRecordService.updateProductCardSellingPoints(request, userId, tenantId);

        return RespBody.ok();
    }

}
