package cn.mlamp.insightflow.cms.enums.dam;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;

@Getter
public enum DamAssetImportTypeEnum implements IEnum<Integer> {
    DIRECT(1),
    AUTO(2);


    @JsonValue
    private final Integer value;

    DamAssetImportTypeEnum(Integer value) {
        this.value = value;
    }

    @JsonCreator
    public static DamAssetImportTypeEnum from(Integer value) {
        if (value == null) {
            return null;
        }
        for (DamAssetImportTypeEnum type : DamAssetImportTypeEnum.values()) {
            if (Objects.equals(type.getValue(), value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown DamAssetTypeEnum value: " + value);
    }

}
