package cn.mlamp.insightflow.cms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 文件下载限流配置
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "rate-limit.file-download")
public class FileDownloadRateLimitConfig {

    /**
     * 千川视频下载限流配置
     */
    private List<RateLimitRule> qianchuanVideo;

    /**
     * 千川分段视频下载限流配置
     */
    private List<RateLimitRule> qianchuanParagraph;

    /**
     * 千川分镜视频下载限流配置
     */
    private List<RateLimitRule> qianchuanScene;

    /**
     * 素材库视频下载限流配置
     */
    private List<RateLimitRule> materialLibrary;

    /**
     * 限流规则
     */
    @Data
    public static class RateLimitRule {
        /**
         * 时间窗口（秒）
         */
        private int timeWindow;

        /**
         * 限制次数
         */
        private int limit;
    }
}
