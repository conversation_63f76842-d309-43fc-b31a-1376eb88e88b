package cn.mlamp.insightflow.cms.graph.config;

import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@ConditionalOnProperty(value = "cms.agent.graph.langfuse.enabled", havingValue = "true")
@Configuration
@ConfigurationProperties(prefix = "cms.agent.graph.langfuse")
public class LangfuseProperties {

    private Boolean enabled = false;

    private String url = "https://langfuse.intra.mlamp.cn";

    private String publicKey;

    private String secretKey;

}
