package cn.mlamp.insightflow.cms.graph.context;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 参数缺失请求类
 * 当Worker节点检测到参数缺失时，通过此类提交参数补全请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ParameterMissingRequest {

    /**
     * 目标节点名称（报告参数缺失的Worker节点）
     */
    private String targetNode;

    /**
     * 原始任务目标描述
     */
    private String originalGoal;

    /**
     * 缺失的参数规格列表
     */
    private List<ParameterSpec> missingParams;

    /**
     * 已存在的参数集合
     */
    @Builder.Default
    private Map<String, Object> existingParams = Map.of();

    /**
     * 请求创建时间
     */
    @Builder.Default
    private Date createTime = new Date();

    /**
     * 错误消息（可选，用于说明参数缺失的原因）
     */
    private String errorMessage;

    /**
     * 创建参数缺失请求的便捷方法
     */
    public static ParameterMissingRequest create(String targetNode, String originalGoal,
                                                 List<ParameterSpec> missingParams) {
        return ParameterMissingRequest.builder()
                .targetNode(targetNode)
                .originalGoal(originalGoal)
                .missingParams(missingParams)
                .build();
    }

    /**
     * 创建参数缺失请求的便捷方法（包含已有参数）
     */
    public static ParameterMissingRequest create(String targetNode, String originalGoal,
                                                 List<ParameterSpec> missingParams,
                                                 Map<String, Object> existingParams) {
        return ParameterMissingRequest.builder()
                .targetNode(targetNode)
                .originalGoal(originalGoal)
                .missingParams(missingParams)
                .existingParams(existingParams)
                .build();
    }

    /**
     * 便捷方法：检查是否有必需的缺失参数
     */
    public boolean hasRequiredMissingParams() {
        return missingParams != null && missingParams.stream().anyMatch(ParameterSpec::isRequired);
    }

    /**
     * 便捷方法：获取必需的缺失参数
     */
    public List<ParameterSpec> getRequiredMissingParams() {
        if (missingParams == null) {
            return List.of();
        }
        return missingParams.stream()
                .filter(ParameterSpec::isRequired)
                .toList();
    }

    /**
     * 便捷方法：获取可选的缺失参数
     */
    public List<ParameterSpec> getOptionalMissingParams() {
        if (missingParams == null) {
            return List.of();
        }
        return missingParams.stream()
                .filter(param -> !param.isRequired())
                .toList();
    }

    /**
     * 参数类型枚举
     */
    public enum ParameterType {
        STRING,         // 文本类型
        INTEGER,        // 整数类型
        DECIMAL,        // 小数类型
        BOOLEAN,        // 布尔类型
        DATE,           // 日期类型
        DATE_RANGE,     // 日期范围
        ENUM,           // 枚举类型
        ARRAY,          // 数组类型
        OBJECT,         // 对象类型
        FILE_ID,        // 文件ID
        URL,            // URL链接
        EMAIL,          // 邮箱地址
        PHONE,          // 电话号码
        JSON            // JSON对象
    }

    /**
     * 参数规格定义
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ParameterSpec {
        /**
         * 参数名称
         */
        private String name;

        /**
         * 显示名称
         */
        private String displayName;

        /**
         * 参数描述
         */
        private String description;

        /**
         * 参数类型
         */
        private ParameterType type;

        /**
         * 是否必填
         */
        private boolean required;

        /**
         * 默认值
         */
        private Object defaultValue;

        /**
         * 验证正则表达式
         */
        private String pattern;

        /**
         * 数值范围
         */
        private Range range;

        /**
         * 枚举值列表
         */
        private List<String> enumValues;

        /**
         * 参数示例
         */
        private String example;

        /**
         * 参数提示信息
         */
        private String hint;
    }

    /**
     * 数值范围定义
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Range {
        private Double min;
        private Double max;
        private boolean minInclusive = true;
        private boolean maxInclusive = true;

        public boolean isInRange(Number value) {
            if (value == null) return false;
            double val = value.doubleValue();

            if (min != null) {
                if (minInclusive && val < min) return false;
                if (!minInclusive && val <= min) return false;
            }

            if (max != null) {
                if (maxInclusive && val > max) return false;
                if (!maxInclusive && val >= max) return false;
            }

            return true;
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            if (min != null) {
                sb.append(minInclusive ? "[" : "(").append(min);
            } else {
                sb.append("(-∞");
            }
            sb.append(", ");
            if (max != null) {
                sb.append(max).append(maxInclusive ? "]" : ")");
            } else {
                sb.append("+∞)");
            }
            return sb.toString();
        }
    }
} 