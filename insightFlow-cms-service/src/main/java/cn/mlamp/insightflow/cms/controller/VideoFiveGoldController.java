package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.annotation.HasMemberRightCheck;
import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.AdminConfig;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.enums.MemberRightEnum;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.model.query.VideoFiveGoldDetailRequest;
import cn.mlamp.insightflow.cms.model.query.VideoFiveGoldRequest;
import cn.mlamp.insightflow.cms.model.query.VideoFiveGoldUpdateRequest;
import cn.mlamp.insightflow.cms.model.vo.VideoFiveGoldDetailPageVO;
import cn.mlamp.insightflow.cms.model.vo.VideoFiveGoldVO;
import cn.mlamp.insightflow.cms.service.IVideoFiveGoldService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-19
 */
@Slf4j
@RequestMapping("/video/five/gold")
@RestController
@RequiredArgsConstructor
@Tag(name = "黄金5秒相关接口")
public class VideoFiveGoldController {

    @Autowired
    private IVideoFiveGoldService videoFiveGoldService;

    @Autowired
    private AdminConfig adminConfig;

    @GetMapping("/list")
    @Operation(summary = "获取黄金5秒列表接口")
    @HasMemberRightCheck(rights = {MemberRightEnum.GOLD3S_SUMMARY})
    public RespBody<Page<VideoFiveGoldVO>> getList(VideoFiveGoldRequest videoFiveGoldRequest) {

        return RespBody.ok(videoFiveGoldService.getList(videoFiveGoldRequest));
    }


    @GetMapping("/list/detail")
    @Operation(summary = "获取黄金5秒详情列表接口")
    public RespBody<VideoFiveGoldDetailPageVO> getListDetail(VideoFiveGoldDetailRequest videoFiveGoldDetailRequest) {
        if (videoFiveGoldDetailRequest.getFiveGoledId() == null) {
            return RespBody.fail("fiveGoledId不能为空");
        }
        return RespBody.ok(videoFiveGoldService.getListDetail(videoFiveGoldDetailRequest));
    }

    @PostMapping("/update/dialogue-routine")
    @Operation(summary = "更新台词套路接口")
    public RespBody<Boolean> updateDialogueRoutine(@RequestBody VideoFiveGoldUpdateRequest request) {
        Integer tenantId = UserContext.getTenantId();
        if (!adminConfig.getTenantId().equals(tenantId)) {
            throw new BusinessException("无权限操作");
        }
        boolean result = videoFiveGoldService.updateDialogueRoutine(request.getId(), request.getDialogueRoutine());
        return result ? RespBody.ok(true) : RespBody.fail("更新失败");
    }

    @DeleteMapping("/delete/gold/{id}")
    @Operation(summary = "删除黄金3秒总结")
    public RespBody<Boolean> deleteGold(@PathVariable("id") Integer id) {
        Integer tenantId = UserContext.getTenantId();
        if (!adminConfig.getTenantId().equals(tenantId)) {
            throw new BusinessException("无权限操作");
        }
        boolean result = videoFiveGoldService.deleteGold(id);
        return result ? RespBody.ok(true) : RespBody.fail("更新失败");
    }


}
