{{- if .Values.rbac.create -}}
{{- $fullName := include "elasticsearch.uname" . -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: "{{ template "elasticsearch.serviceAccount" . }}"
  annotations:
    {{- with .Values.rbac.serviceAccountAnnotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  labels:
    heritage: {{ .Release.Service | quote }}
    release: {{ .Release.Name | quote }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    app: {{ $fullName | quote }}
{{- end -}}
