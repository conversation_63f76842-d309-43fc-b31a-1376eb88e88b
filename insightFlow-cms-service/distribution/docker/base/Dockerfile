FROM hub.intra.mlamp.cn/ai-pc/linuxserver/ffmpeg:7.1.1

# 设置环境变量TZ
ENV TZ=Asia/Shanghai
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

RUN sed -i 's/archive.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    sed -i 's/security.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list

RUN apt update && \
    apt install -y tini less wget tzdata && \
    apt clean && \
    rm -rf /var/lib/apt/lists/* && \
    rm -rf /var/cache/apt/archives/*

RUN apt update && \
    apt install -y openjdk-17-jdk-headless openjdk-17-jre openjdk-17-jre-headless && \
    apt clean && \
    rm -rf /var/lib/apt/lists/* && \
    rm -rf /var/cache/apt/archives/*

ENV JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64

RUN apt update && \
    apt install -y s3fs && \
    apt clean && \
    rm -rf /var/lib/apt/lists/* && \
    rm -rf /var/cache/apt/archives/*

ENTRYPOINT ["/usr/bin/tini", "--", "/bin/bash"]