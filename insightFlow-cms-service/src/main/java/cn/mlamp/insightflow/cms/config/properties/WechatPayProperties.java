package cn.mlamp.insightflow.cms.config.properties;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix="pay.wechat")
@Getter
@Setter
@ToString
public class WechatPayProperties {
    private String merchantId;
    private String merchantSerialNumber;
    private String apiV3Key;
    private String appId;
    private String privateKeyPath;
}