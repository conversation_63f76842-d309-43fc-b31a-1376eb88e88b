package cn.mlamp.insightflow.cms.auth.cms.dto;

import cn.mlamp.insightflow.cms.auth.cms.bo.TenantTemplateBO;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Ttc激活用户DTO
 *
 * <AUTHOR>
 * @since 2022-09-16 14:57:42
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TtcRequestActivateUserTemplateBO extends TenantTemplateBO {
    /**
     * 用户名称
     */
    @NotBlank(message = "override_message.tenant_name_not_blank")
    @Size(max = 20, message = "override_message.tenant_name_out_of_length")
    private String tenantName;
    /**
     * 邮箱
     */
    @NotBlank(message = "override_message.password_not_blank")
    @Pattern(regexp = "^([A-Za-z0-9_\\-\\.])+\\@([A-Za-z0-9_\\-\\.])+\\.([A-Za-z]{2,10})$", message = "override_message.email_invalid")
    private String email;
}
