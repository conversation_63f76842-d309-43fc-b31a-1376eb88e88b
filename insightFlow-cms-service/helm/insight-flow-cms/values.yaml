# Default values for insight-flow-cms.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
replicaCount: 1
image:
  repository: hub.intra.mlamp.cn/ai-pc/insightflow-cms-service
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""
  cleaner:
    repository: hub.intra.mlamp.cn/public/docker-jdk-mvn-jdk17-amd64
    tag: "latest"
namespace: ai-pc
extraEnv: []
imagePullSecrets:
  - name: "ai-pc-ai-pc-robot"
nameOverride: ""
fullnameOverride: ""
podAnnotations: {}
podLabels: {}
podSecurityContext: {}
# fsGroup: 2000

securityContext: {}
# capabilities:
#   drop:
#   - ALL
# readOnlyRootFilesystem: true
# runAsNonRoot: true
# runAsUser: 1000

service:
  type: ClusterIP
  port: 80
  targetPort: 8080
ingress:
  enabled: false
resources: {}
livenessProbe:
  httpGet:
    path: /health
    port: 8080
    scheme: HTTP
  failureThreshold: 3
  initialDelaySeconds: 60
  periodSeconds: 15
  successThreshold: 1
  timeoutSeconds: 30
readinessProbe:
  failureThreshold: 3
  httpGet:
    path: /health
    port: 8080
    scheme: HTTP
  initialDelaySeconds: 60
  periodSeconds: 15
  successThreshold: 1
  timeoutSeconds: 30
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80
# Additional volumes on the output Deployment definition.
volumes: {}
# Additional volumeMounts on the output Deployment definition.
volumeMounts: {}
nodeSelector: {}
tolerations: []
affinity: {}

api:
  replicaCount: 1
  extraEnv: []
  persistence:
    enabled: false
    mountPath: "/app/storage"
    storageClassName: "gfs"
    size: "10Gi"
  volumes: []
  volumeMounts: []
  resources: {}

worker:
  persistence:
    pvc:
      enabled: false
      mountPath: "/app/storage"
      storageClassName: "gfs"
      size: "10Gi"
    s3fs:
      enabled: false
      endpoint: "https://mos-api.intra.mlamp.cn"
      bucket: "ai-pc-cms"
      accessKey: "I$In3pMTj4X)1h2N"
      secretKey: "W$t*GkF%T$*hh8FAMZ80D2ROHy863WM*"
      subPath: "/tmp/shared"
      mountPath: "/mnt/s3"
  daily:
    replicaCount: 1
    extraEnv: []
    volumes: []
    volumeMounts: []
    resources: {}
    nodeSelector: {}
    affinity: {}
    tolerations: []
  customer:
    replicaCount: 1
    extraEnv: []
    volumes: []
    volumeMounts: []
    resources: {}
    nodeSelector: {}
    affinity: {}
    tolerations: []
  network:
    replicaCount: 1
    extraEnv: []
    volumes: []
    volumeMounts: []
    resources: {}
    nodeSelector: {}
    affinity: {}
    tolerations: []