package cn.mlamp.insightflow.cms.enums;

import lombok.Getter;
import org.springframework.lang.Nullable;

/**
 * 任务收藏类型枚举
 */
@Getter
public enum CmsCollectTypeEnum {

    /**
     * AI脚本
     */
    AI_SCRIPT(1, "AI脚本"),

    /**
     * 视频收藏（智能分镜）
     */
    VIDEO_SPLIT(2, "视频收藏（智能分镜）"),

    /**
     * 视频合成
     */
    VIDEO_SYNTHESIS(3, "视频合成"),

    /**
     * 视频测一测
     */
    VIDEO_VALUE(4, "视频测一测");

    private final Integer code;
    private final String desc;

    CmsCollectTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CmsCollectTypeEnum getByCode(@Nullable Integer code) {
        if (code == null) {
            return null;
        }
        for (CmsCollectTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
