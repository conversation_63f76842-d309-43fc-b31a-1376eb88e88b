package cn.mlamp.insightflow.cms.controller.dam;

import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.entity.dam.DamAsset;
import cn.mlamp.insightflow.cms.entity.dam.DamAssetEmbedding;
import cn.mlamp.insightflow.cms.service.dam.IDamAssetEmbeddingService;
import cn.mlamp.insightflow.cms.service.dam.IDamAssetService;
import cn.mlamp.insightflow.cms.service.impl.CmsStoryboardEmbeddingServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

@RestController
@RequestMapping("/dam/embedding")
public class DamAssetEmbeddingController {

    @Autowired
    private IDamAssetEmbeddingService damAssetEmbeddingService;

    @Autowired
    private IDamAssetService damAssetService;

    @Autowired
    private CmsStoryboardEmbeddingServiceImpl cmsStoryboardEmbeddingServiceImpl;

    /**
     * 测试保存 embedding
     * POST /api/dam/embedding/save
     */
    @PostMapping("/save")
    public String saveTestEmbedding(@RequestParam(required = false) String content) {
        // 构造 DamAsset
        DamAsset asset = new DamAsset();
        // 随机数
        asset.setId(ThreadLocalRandom.current().nextInt(1000));
        asset.setTenantId(1);
        asset.setUserId(1);
        asset.setDirectoryId(1);
        asset.setCreateTime(new Date());
        asset.setUpdateTime(new Date());

        // 构造 DamAssetEmbedding
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("test", "test");

        damAssetEmbeddingService.saveByAssetAndEmbeddingContent(asset, content, metadata);
        return "success";
    }

    @PostMapping("/save/{assetId}")
    public String saveTestEmbedding(@PathVariable Integer assetId) {
        damAssetEmbeddingService.saveEmbeddingByAssetId(assetId);
        return "success";
    }

    /**
     * 分页向量相似度检索接口
     * POST /api/dam/embedding/pageEmbedding
     *
     * @param current      当前页码
     * @param pageSize     每页数量
     * @param content      检索内容
     * @param aspectRatio  宽高比
     * @param directoryIds 目录ID
     */
    @GetMapping("/pageEmbedding")
    public Page<DamAssetEmbedding> pageEmbedding(@RequestParam Integer current,
                                                 @RequestParam Integer pageSize,
                                                 @RequestParam String aspectRatio,
                                                 @RequestParam String content,
                                                 @RequestParam(required = false) List<Integer> directoryIds) {
        Integer tenantId = UserContext.getTenantId();
        Integer userId = UserContext.getUserId();
        return damAssetEmbeddingService.pageEmbedding(current, pageSize, aspectRatio, content, tenantId, userId, directoryIds);
    }

    /**
     * Top K embedding 查询接口
     */
    @GetMapping("/topKEmbedding")
    public List<DamAssetEmbedding> topKEmbedding(@RequestParam String content,
                                                 @RequestParam Integer limit,
                                                 @RequestParam Float threshold,
                                                 @RequestParam String aspectRatio,
                                                 @RequestParam(required = false) List<Integer> directoryIds) {
        Integer tenantId = UserContext.getTenantId();
        Integer userId = UserContext.getUserId();
        return damAssetEmbeddingService.topKEmbedding(content, limit, threshold, aspectRatio, tenantId, userId, directoryIds);
    }

    /**
     * 更新指定ID的embedding内容
     *
     * @param id      素材ID
     * @param content 新内容
     */
    @PostMapping("/updateEmbedding")
    public int updateEmbedding(@RequestParam Integer id, @RequestParam String content) {
        return damAssetEmbeddingService.updateEmbedding(id, content);
    }

    /**
     * 删除指定ID的embedding
     *
     * @param id 素材ID
     */
    @PostMapping("/deleteEmbedding")
    public int deleteEmbedding(@RequestParam Integer id) {
        return damAssetEmbeddingService.deleteEmbedding(id);
    }

    // 测试接口，仅供测试使用
    @PostMapping("/update/{id}")
    public String testUpdateByAsset(@PathVariable Integer id) {
        var asset = damAssetService.getById(id);
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("test", "test-update");
        damAssetEmbeddingService.updateByAsset(asset, metadata);
        return "Update called";
    }

    @PostMapping("/fix/video/aspectRatio")
    public void fixVideoAspectRatio() {
        damAssetEmbeddingService.fixVideoAspectRatio();
    }

    /**
     * 同步 cms_asset 表和 cms_asset_embeddings 表数据
     * 将 cms_asset 表中存在但 cms_asset_embeddings 表中不存在的记录进行同步
     * POST /api/dam/embedding/syncAssetsToEmbeddings
     *
     * @return 同步成功的记录数
     */
    @PostMapping("/syncAssetsToEmbeddings")
    public int syncAssetsToEmbeddings() {
        return damAssetEmbeddingService.syncAssetsToEmbeddings();
    }

    /**
     * 刷新所有资产的embedding向量，使用新的算法接口生成向量
     * POST /api/dam/embedding/refreshAssetsEmbeddings
     *
     * @return 刷新成功的记录数
     */
    @PostMapping("/refreshAssetsEmbeddings")
    public int refreshAssetsEmbeddings() {
        return damAssetEmbeddingService.refreshAssetsEmbeddings();
    }

    /**
     * 刷新StoryboardEmbedding的embedding向量，仅使用文本内容，不需要图片
     * POST /api/dam/embedding/refreshAssetsEmbeddingsWithTextOnly
     *
     * @return 刷新成功的记录数
     */
    @PostMapping("/refreshAssetsEmbeddingsWithTextOnly")
    public int refreshAssetsEmbeddingsWithTextOnly() {
        return cmsStoryboardEmbeddingServiceImpl.refreshAssetsEmbeddingsWithTextOnly();
    }

    /**
     * 将MySQL的damAsset.duration数据同步到DamAssetEmbedding表中
     * POST /api/dam/embedding/syncDurationToEmbeddings
     *
     * @return 同步成功的记录数
     */
    @PostMapping("/syncDurationToEmbeddings")
    public int syncDurationToEmbeddings() {
        return damAssetEmbeddingService.syncDurationToEmbeddings();
    }
}
