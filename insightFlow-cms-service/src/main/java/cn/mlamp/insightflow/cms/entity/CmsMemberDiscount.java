package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("cms_member_discount")
public class CmsMemberDiscount {
    @TableId(type = IdType.AUTO)
    private Integer id ;

    private Integer duration;
    private String discount;

    // 数据逻辑删除标记 0:未删除 1:已删除
    @TableField(value = "is_deleted")
    private Integer deleted;

}