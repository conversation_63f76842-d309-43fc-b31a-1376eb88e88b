package cn.mlamp.insightflow.cms.config;

import com.mz.ttc.annotation.EnableAuthCenter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

/**
 * 认证中心启用时的 RestTemplate 配置
 * 当启用认证中心时，提供一个自定义名称的 RestTemplate Bean
 * 
 * <AUTHOR>
 * @since 2025-06-26
 */
@Configuration
@EnableAuthCenter
@ConditionalOnProperty(name = "cms.enable-auth-center", havingValue = "true", matchIfMissing = true)
public class AuthCenterRestTemplateConfig {
}
