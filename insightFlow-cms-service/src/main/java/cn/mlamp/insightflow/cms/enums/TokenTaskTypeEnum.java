package cn.mlamp.insightflow.cms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@AllArgsConstructor
@Getter
public enum TokenTaskTypeEnum {


    //上传任务
    UPLOAD_TASK("upload_task", 1, "上传任务"),
    // AI仿写
    AI_IMITATE_TASK("ai_imitate_task", 2, "AI仿写"),

    // 黄金5秒
    GOLDEN_FIVE_SECOND("golden_five_second", 3, "黄金5秒"),
    // 素材上传任务
    ASSET_UPLOAD_TASK("asset_upload_task", 4, "素材上传任务"),
    // 视频价值任务
    VIDEO_VALUE_TASK("video_value_task", 5, "视频价值任务"),
    // AI视频生成
    AI_VIDEO_GENERATE_TASK("ai_video_generate_task", 6, "AI视频生成"),

    ;

    private final String taskCode;

    private final Integer taskType;

    private final String showTaskType;


    public static String getShowTaskType(Integer taskType) {
        return Arrays.stream(values()).filter(t -> t.getTaskType().equals(taskType)).findFirst()
                .map(TokenTaskTypeEnum::getShowTaskType).orElse(null);
    }


    public String getTaskCode() {
        return taskCode;
    }

    public Integer getTaskType() {
        return taskType;
    }

    public String getShowTaskType() {
        return showTaskType;
    }

}
