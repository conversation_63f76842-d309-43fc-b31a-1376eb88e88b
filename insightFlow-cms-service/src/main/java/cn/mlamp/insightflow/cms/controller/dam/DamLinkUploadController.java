package cn.mlamp.insightflow.cms.controller.dam;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.model.query.DamLinkUploadMarkRequest;
import cn.mlamp.insightflow.cms.model.query.KolListQuery;
import cn.mlamp.insightflow.cms.model.vo.DouyinKolContentVO;
import cn.mlamp.insightflow.cms.model.vo.DouyinKolVO;
import cn.mlamp.insightflow.cms.service.dam.IDamLinkUploadService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: husuper
 * @CreateTime: 2025-07-16
 */
@RestController
@RequestMapping("dam/link/upload")
@RequiredArgsConstructor
@Tag(name = "链接上传素材库")
public class DamLinkUploadController {

    @Autowired
    private IDamLinkUploadService linkUploadService;

    /**
     * 获取抖音用户列表接口
     */
    @GetMapping("/kol/list")
    @Operation(summary = "获取KOL列表接口")
    public RespBody<List<DouyinKolVO>> getKolList(KolListQuery query) {
        return RespBody.ok(linkUploadService.getKolList(query));
    }

    /**
     * 获取KOL用户帖子列表接口
     */
    @GetMapping("kol/content/list")
    @Operation(summary = "获取KOL用户帖子列表接口")
    public RespBody<List<DouyinKolContentVO>> getKolContentList(String kolId) {
        return RespBody.ok(linkUploadService.getKolContentList(kolId));
    }


    /**
     * 获取KOL用户帖子列表接口
     */
    @PostMapping("marking/type")
    @Operation(summary = "确认打标方式接口")
    public RespBody<Boolean> marking(@RequestBody DamLinkUploadMarkRequest request) {

        return RespBody.ok(linkUploadService.marking(request));
    }


}
