{{- if and .Values.worker.enabled}}
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
{{ include "dify.ud.annotations" . | indent 4 }}
    descriptions: worker
  labels:
{{- include "dify.labels" . | nindent 4 }}
    component: worker
    # app: {{ template "dify.worker.fullname" . }}
{{ include "dify.ud.labels" . | indent 4 }}
  name: {{ template "dify.worker.fullname" . }}
spec:
  replicas: {{ .Values.worker.replicas }}
  selector:
    matchLabels:
{{- include "dify.selectorLabels" . | nindent 6 }}
      component: worker
      {{/*
      # Required labels for istio
      # app: {{ template "dify.worker.fullname" . }}
      # version: {{ (print "v" .Values.serviceMesh.version) | quote }}
      */}}
  template:
    metadata:
      annotations:
{{ include "dify.ud.annotations" . | indent 8 }}
      labels:
{{- include "dify.selectorLabels" . | nindent 8 }}
        component: worker
        {{/*
        # Required labels for istio
        # app: {{ template "dify.worker.fullname" . }}
        # version: {{ (print "v" .Values.serviceMesh.version) | quote }}
        */}}
{{ include "dify.ud.labels" . | indent 8 }}
    spec:
      {{- if .Values.image.api.pullSecrets }}
      imagePullSecrets:
      {{- range .Values.image.api.pullSecrets }}
        - name: {{ . }}
      {{- end }}
      {{- end }}
{{- if .Values.worker.hostAliases }}
      hostAliases:
{{- range $.Values.worker.hostAliases }}
      - ip: {{ .ip }}
        hostnames:
        {{- range .hostnames }}
        - {{ . }}
        {{- end }}
{{- end }}
{{- end }}
      containers:
      - image: "{{ .Values.image.api.repository }}:{{ .Values.image.api.tag }}"
        imagePullPolicy: "{{ .Values.image.api.pullPolicy }}"
        name: worker
        env:
        {{- if .Values.worker.extraEnv }}
          {{- toYaml .Values.worker.extraEnv | nindent 8 }}
        {{- end }}
        envFrom:
          - configMapRef:
              name: {{ template "dify.worker.fullname" . }}
        resources:
          {{- toYaml .Values.worker.resources | nindent 12 }}
        volumeMounts:
        {{- if and (not .Values.externalS3.enabled) (not .Values.externalAzureBlob.enabled) (not .Values.externalAliyunOSS.enabled) }}
        - name: app-data
          mountPath: {{ .Values.api.persistence.mountPath | quote }}
          subPath: {{ .Values.api.persistence.persistentVolumeClaim.subPath | default "" }}
        {{- end }}
        {{- if .Values.api.externalModels }}
        {{- range $externalModel := .Values.api.externalModels }}
        - name: external-models-config
          mountPath: /app/api/core/model_runtime/model_providers/openai/llm/{{ . }}
          subPath: {{ . }}
        {{- end }}
        {{- end }}
        {{- if .Values.api.volumeMounts }}
        {{- range $apiVolumeMount := .Values.api.volumeMounts }}
        {{- if .items }}
        {{- range $apiVolumeMount.items}}
        - name: {{ $apiVolumeMount.name }}
          mountPath: {{ $apiVolumeMount.mountPath }}/{{ .path }}
          subPath: {{ .key | default "" }}
          readOnly: {{ .readOnly | default "false" }}
            {{- end }}
            {{- else }}
        - name: {{ .name }}
          mountPath: {{ .mountPath }}
          subPath: {{ .subPath | default "" }}
          readOnly: {{ .readOnly | default "false" }}
        {{- end }}
        {{- end }}
        {{- end }}
{{/*        readinessProbe:*/}}
{{/*          exec:*/}}
{{/*            command:*/}}
{{/*              - "cat"*/}}
{{/*              - "/tmp/celery_ready"*/}}
{{/*          initialDelaySeconds: 60*/}}
{{/*          periodSeconds: 180*/}}
{{/*          timeoutSeconds: 30*/}}
    {{- if and (.Values.nodeSelector) (not .Values.worker.nodeSelector) }}
      nodeSelector:
{{ toYaml .Values.nodeSelector | indent 8 }}
    {{- end }}
    {{- if .Values.worker.nodeSelector }}
      nodeSelector:
{{ toYaml .Values.worker.nodeSelector | indent 8 }}
    {{- end }}
    {{- if and (.Values.affinity) (not .Values.worker.affinity) }}
      affinity:
{{ toYaml .Values.affinity | indent 8 }}
    {{- end }}
    {{- if .Values.worker.affinity }}
      affinity:
{{ toYaml .Values.worker.affinity | indent 8 }}
    {{- end }}
    {{- if and (.Values.tolerations) (not .Values.worker.tolerations) }}
      tolerations:
{{ toYaml .Values.tolerations | indent 8 }}
    {{- end }}
    {{- if .Values.worker.tolerations }}
      tolerations:
{{ toYaml .Values.worker.tolerations | indent 8 }}
    {{- end }}
      volumes:
      {{- if and (not .Values.externalS3.enabled) (not .Values.externalAzureBlob.enabled) (not .Values.externalAliyunOSS.enabled) }}
      - name: app-data
        persistentVolumeClaim:
          claimName: {{ .Values.api.persistence.persistentVolumeClaim.existingClaim | default (printf "%s" (include "dify.fullname" . | trunc 58)) }}
      {{- end }}
      {{- if .Values.api.externalModels }}
      - name: external-models-config
        configMap:
          name: {{ template "dify.fullname" . }}-external-models-config
      {{- end }}
      {{- if .Values.worker.volumeMounts }}
      {{- range .Values.worker.volumeMounts }}
      - name: {{ .name }}
        configMap:
          name: {{ .configMap.name }}
          {{- if .items }}
          items:
          {{- range .items | default list }}
            - key: {{ .key }}
              path: {{ .path }}
          {{- end }}
          {{- end }}
      {{- end }}
      {{- end }}
{{- end }}
