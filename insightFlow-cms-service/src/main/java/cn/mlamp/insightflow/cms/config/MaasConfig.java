package cn.mlamp.insightflow.cms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * MaaS API配置
 */
@ConfigurationProperties(prefix = "maas")
@Configuration
@Data
public class MaasConfig {

    /**
     * API基础URL
     */
    private String apiUrl;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 端点
     */
    private String endPoint;

    /**
     * 图片生成API端点
     */
    private String imageGenerationEndPoint;

    /**
     * 视频生成API端点
     */
    private String videoGenerationEndPoint;
}
