{{- if or $.Values.schema.createDatabase.enabled $.Values.schema.setup.enabled $.Values.schema.update.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "temporal.componentname" (list $ (printf "schema-%d" .Release.Revision | replace "." "-")) }}
  labels:
    {{- include "temporal.resourceLabels" (list $ "database" "") | nindent 4 }}
spec:
  backoffLimit: {{ $.Values.schema.setup.backoffLimit }}
  ttlSecondsAfterFinished: 86400
  template:
    metadata:
      name: {{ include "temporal.componentname" (list $ (printf "schema-%d" .Release.Revision | replace "." "-")) }}
      labels:
        {{- include "temporal.resourceLabels" (list $ "database" "") | nindent 8 }}
        {{- with $.Values.schema.podLabels }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with $.Values.schema.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      {{ include "temporal.serviceAccount" $ }}
      restartPolicy: OnFailure
      initContainers:
        {{- with $.Values.admintools.additionalInitContainers }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- if $.Values.cassandra.enabled }}
        - name: check-cassandra
          image: "{{ $.Values.cassandra.image.repo }}:{{ $.Values.cassandra.image.tag }}"
          imagePullPolicy: {{ $.Values.cassandra.image.pullPolicy }}
          command: ['sh', '-c', 'until cqlsh {{ include "cassandra.host" $ }} {{ $.Values.cassandra.config.ports.cql }} -e "SHOW VERSION"; do echo waiting for cassandra to start; sleep 1; done;']
        {{- end }}
        {{- if or $.Values.elasticsearch.enabled }}
        - name: check-elasticsearch
          image: "{{ $.Values.admintools.image.repository }}:{{ $.Values.admintools.image.tag }}"
          imagePullPolicy: {{ $.Values.admintools.image.pullPolicy }}
          command: ['sh', '-c', 'until curl --silent --fail --user "$ES_USER:$ES_PWD" $ES_SCHEME://$ES_HOST:$ES_PORT 2>&1 > /dev/null; do echo waiting for elasticsearch to start; sleep 1; done;']
          env:
            {{- include "temporal.admintools-env" (list $ "visibility") | nindent 12 }}
        {{- end }}
        {{- if $.Values.schema.createDatabase.enabled }}
          {{- range $store := (list "default" "visibility") }}
            {{- $storeConfig := index $.Values.server.config.persistence $store }}
            {{- $driver := include "temporal.persistence.driver" (list $ $store) }}
            {{- if ne $driver "elasticsearch" }}
        - name: create-{{ $store }}-store
          image: "{{ $.Values.admintools.image.repository }}:{{ $.Values.admintools.image.tag }}"
          imagePullPolicy: {{ $.Values.admintools.image.pullPolicy }}
              {{- if eq $driver "cassandra" }}
          command: ['temporal-cassandra-tool', 'create', '-k', '{{ $storeConfig.cassandra.keyspace }}', '--replication-factor', '{{ $storeConfig.cassandra.replicationFactor }}']
              {{- else if eq $driver "sql" }}
          command: ['temporal-sql-tool', 'create-database']
              {{- end }}
          env:
              {{- include "temporal.admintools-env" (list $ $store) | nindent 12 }}
              {{- with $.Values.admintools.additionalVolumeMounts }}
          volumeMounts:
                {{- toYaml . | nindent 12 }}
              {{- end }}
              {{- with $.Values.schema.resources }}
          resources:
                {{- toYaml . | nindent 12 }}
              {{- end }}
              {{- with $.Values.schema.containerSecurityContext }}
          securityContext:
                {{- toYaml . | nindent 12 }}
              {{- end }}
            {{- end }}
          {{- end }}
        {{- end }}
        {{- if $.Values.schema.setup.enabled }}
          {{- range $store := (list "default" "visibility") }}
            {{- $storeConfig := index $.Values.server.config.persistence $store }}
            {{- $driver := include "temporal.persistence.driver" (list $ $store) }}
        - name: setup-{{ $store }}-store
          image: "{{ $.Values.admintools.image.repository }}:{{ $.Values.admintools.image.tag }}"
          imagePullPolicy: {{ $.Values.admintools.image.pullPolicy }}
            {{- if eq $driver "cassandra" }}
          command: ['temporal-cassandra-tool', 'setup-schema', '-v', '0.0']
            {{- else if eq $driver "sql" }}
          command: ['temporal-sql-tool', 'setup-schema', '-v', '0.0']
            {{- else if eq $driver "elasticsearch" }}
          command: ['sh', '-c']
          args:
            - 'curl -X PUT --fail --user "$ES_USER:$ES_PWD" $ES_SCHEME://$ES_HOST:$ES_PORT/_template/temporal_visibility_v1_template -H "Content-Type: application/json" --data-binary "@schema/elasticsearch/visibility/index_template_$ES_VERSION.json" 2>&1 &&
              curl --head --fail --user "$ES_USER:$ES_PWD" $ES_SCHEME://$ES_HOST:$ES_PORT/$ES_VISIBILITY_INDEX 2>&1 ||
              curl -X PUT --fail --user "$ES_USER:$ES_PWD" $ES_SCHEME://$ES_HOST:$ES_PORT/$ES_VISIBILITY_INDEX 2>&1'
            {{- end }}
          env:
            {{- include "temporal.admintools-env" (list $ $store) | nindent 12 }}
            {{- with $.Values.admintools.additionalVolumeMounts }}
          volumeMounts:
              {{- toYaml . | nindent 12 }}
            {{- end }}
            {{- with $.Values.schema.resources }}
          resources:
              {{- toYaml . | nindent 12 }}
            {{- end }}
            {{- with $.Values.schema.containerSecurityContext }}
          securityContext:
              {{- toYaml . | nindent 12 }}
            {{- end }}
          {{- end }}
        {{- end }}
        {{- if $.Values.schema.update.enabled }}
          {{- range $store := (list "default" "visibility") }}
            {{- $storeConfig := index $.Values.server.config.persistence $store }}
            {{- $driver := include "temporal.persistence.driver" (list $ $store) }}
            {{- $schema := include "temporal.persistence.schema" $store }}
            {{- if ne $driver "elasticsearch" }}
        - name: update-{{ $store }}-store
          image: "{{ $.Values.admintools.image.repository }}:{{ $.Values.admintools.image.tag }}"
          imagePullPolicy: {{ $.Values.admintools.image.pullPolicy }}
              {{- if eq $driver "cassandra" }}
          command: ['temporal-cassandra-tool', 'update-schema', '--schema-dir', '/etc/temporal/schema/cassandra/{{ $schema }}/versioned']
              {{- else if eq $driver "sql" }}
                {{- $plugin := include "temporal.persistence.sql.driver" (list $ $store) }}
                {{- if eq $plugin "mysql8" }}
          command: ['temporal-sql-tool', 'update-schema', '--schema-dir', '/etc/temporal/schema/mysql/v8/{{ $schema }}/versioned']
                {{- else if or (eq $plugin "postgres12") (eq $plugin "postgres12_pgx") }}
          command: ['temporal-sql-tool', 'update-schema', '--schema-dir', '/etc/temporal/schema/postgresql/v12/{{ $schema }}/versioned']
                {{- end }}
              {{- end }}
          env:
              {{- include "temporal.admintools-env" (list $ $store) | nindent 12 }}
              {{- with $.Values.admintools.additionalVolumeMounts }}
          volumeMounts:
                {{- toYaml . | nindent 12 }}
              {{- end }}
              {{- with $.Values.schema.resources }}
          resources:
                {{- toYaml . | nindent 12 }}
              {{- end }}
              {{- with $.Values.schema.containerSecurityContext }}
          securityContext:
                {{- toYaml . | nindent 12 }}
              {{- end }}
            {{- end }}
          {{- end }}
        {{- end }}
        {{- if $.Values.server.config.namespaces.create }}
          {{- range $namespace := $.Values.server.config.namespaces.namespace }}
        - name: create-{{ $namespace.name }}-namespace
          image: "{{ $.Values.admintools.image.repository }}:{{ $.Values.admintools.image.tag }}"
          imagePullPolicy: {{ $.Values.admintools.image.pullPolicy }}
          command: ['/bin/sh','-c']
          args: ['temporal operator namespace describe -n {{ $namespace.name }} || temporal operator namespace create -n {{ $namespace.name }}{{- if hasKey $namespace "retention" }} --retention {{ $namespace.retention }}{{- end }}']
          env:
            - name: TEMPORAL_ADDRESS
              {{- if and (hasKey $.Values.server "internalFrontend") $.Values.server.internalFrontend.enabled }}
              value: {{ include "temporal.fullname" $ }}-internal-frontend.{{ $.Release.Namespace }}.svc:{{ $.Values.server.internalFrontend.service.port }}
              {{- else if $.Values.server.frontend.ingress.enabled }}
              value: "{{ index $.Values.server.frontend.ingress.hosts 0 }}"
              {{- else }}
              value: "{{ include "temporal.fullname" $ }}-frontend.{{ $.Release.Namespace }}.svc:{{ $.Values.server.frontend.service.port }}"
              {{- end }}
              {{- with $.Values.admintools.additionalEnv }}
                {{- toYaml . | nindent 12 }}
              {{- end }} 
              {{- with $.Values.admintools.additionalVolumeMounts }}
          volumeMounts:
                {{- toYaml . | nindent 12 }}
              {{- end }}
              {{- with $.Values.schema.resources }}
          resources:
                {{- toYaml . | nindent 12 }}
              {{- end }}
              {{- with $.Values.schema.containerSecurityContext }}
          securityContext:
                {{- toYaml . | nindent 12 }}
              {{- end }}
          {{- end }}
        {{- end }}
      containers:
        - name: done
          image: "{{ $.Values.admintools.image.repository }}:{{ $.Values.admintools.image.tag }}"
          imagePullPolicy: {{ $.Values.admintools.image.pullPolicy }}
          command: ['sh', '-c', 'echo "Store setup completed"']
            {{- with $.Values.schema.resources }}
          resources:
              {{- toYaml . | nindent 12 }}
            {{- end }}
            {{- with $.Values.schema.containerSecurityContext }}
          securityContext:
              {{- toYaml . | nindent 12 }}
            {{- end }}
      {{- with $.Values.schema.securityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $.Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $.Values.admintools.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $.Values.admintools.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $.Values.admintools.additionalVolumes }}
      volumes:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end -}}
