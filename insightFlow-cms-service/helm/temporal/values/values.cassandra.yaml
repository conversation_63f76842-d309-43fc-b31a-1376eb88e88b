server:
  config:
    logLevel: "debug,info"

    numHistoryShards: 512

    persistence:
      default:
        driver: "cassandra"

#        faultInjection:
#          targets:
#            dataStores:
#              ExecutionStore:
#                methods:
#                  GetCurrentExecution:
#                    errors:
#                      ResourceExhausted: 0.1
#                  AppendHistoryNodes:
#                    errors:
#                      ResourceExhausted: 0.05
#                  UpdateWorkflowExecution:
#                    errors:
#                      ResourceExhausted: 0.15
#                  GetWorkflowExecution:
#                    errors:
#                      ResourceExhausted: 0.15

        cassandra:
          hosts: ["cassandra.default.svc.cluster.local"]
          port: 9042
          keyspace: temporal
          user: "user"
          password: "password"
          existingSecret: ""
          replicationFactor: 1
          consistency:
            default:
              consistency: "local_quorum"
              serialConsistency: "local_serial"

cassandra:
  enabled: false

mysql:
  enabled: false

postgresql:
  enabled: false

schema:
  createDatabase:
    enabled: true
  setup:
    enabled: false
  update:
    enabled: false
