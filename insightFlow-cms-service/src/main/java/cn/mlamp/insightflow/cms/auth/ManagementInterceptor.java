package cn.mlamp.insightflow.cms.auth;

import cn.mlamp.insightflow.cms.config.properties.ManagementProperties;
import com.mz.ttc.util.TtcUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 管理中心拦截器，判断当前用户是否可以访问管理中心接口
 */
@Slf4j
@Component
public class ManagementInterceptor implements HandlerInterceptor {
    private static final String ERROR_BODY = "{\"statusCode\":403,\"message\":\"AUTHORIZATION_FAIL\"}";

    private final ManagementProperties managementProperties;

    public ManagementInterceptor(ManagementProperties managementProperties) {
        this.managementProperties = managementProperties;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (managementProperties.getTenantIds().contains(TtcUtil.getCurrentTenantId())) {
            return true;
        }
        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        // 不是controller层返回，默认charset是ISO-8859-1
        response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
        response.getWriter().write(ERROR_BODY);
        return false;
    }
}