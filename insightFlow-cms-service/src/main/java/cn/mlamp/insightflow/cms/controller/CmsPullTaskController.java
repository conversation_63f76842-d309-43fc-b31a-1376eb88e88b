package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.service.CmsPullTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;

@RestController
@RequestMapping("/cms-pull-task")
public class CmsPullTaskController {

    @Autowired
    private CmsPullTaskService cmsPullTaskService;

    /**
     * 测试执行行业数据拉取
     *
     * @param endDate 可选，传递结束日期，默认为当前日期
     */
    @GetMapping("/executeIndustryDataPull")
    public String executeIndustryDataPull(@RequestParam(required = false) LocalDate endDate) {
        if (endDate == null) {
            endDate = LocalDate.now();
        }
        cmsPullTaskService.executeIndustryDataPull(endDate);
        return "Industry Data Pull started with end date: " + endDate;
    }

    /**
     * 测试执行 Tribe 数据拉取
     *
     * @param endDate 可选，传递结束日期，默认为当前日期
     */
    @GetMapping("/executeTribeDataPull")
    public String executeTribeDataPull(@RequestParam(required = false) LocalDate endDate) {
        if (endDate == null) {
            endDate = LocalDate.now();
        }
        cmsPullTaskService.executeTribeDataPull(endDate);
        return "Tribe Data Pull started with end date: " + endDate;
    }

    /**
     * 测试同步数据从 ES 到数据库
     *
     * @param endDate 可选，传递结束日期，默认为当前日期
     */
    @GetMapping("/syncDataFromEsToDb")
    public String syncDataFromEsToDb(@RequestParam(required = false) LocalDate endDate) {
        if (endDate == null) {
            endDate = LocalDate.now();
        }
        cmsPullTaskService.syncDataFromEsToDb(endDate);
        return "Sync Data from ES to DB started with end date: " + endDate;
    }
}
