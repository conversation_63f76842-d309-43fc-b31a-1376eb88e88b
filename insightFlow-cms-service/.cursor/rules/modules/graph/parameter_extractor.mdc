---
description:
globs: src/main/java/cn/mlamp/insightflow/cms/graph/nodes/ParameterExtractorNode.java
alwaysApply: false
---
# ParameterExtractorNode 参数补全节点详细说明

## 概述
[ParameterExtractorNode](mdc:src/main/java/cn/mlamp/insightflow/cms/graph/nodes/ParameterExtractorNode.java) 是专门负责引导用户补全缺失参数的节点。它通过友好的对话方式，逐步收集worker节点执行所需的必要参数，与 [SupervisorNode](mdc:.cursor/rules/modules/graph/supervisor.mdc) 协作处理参数缺失场景，确保用户能够顺利完成任务。

## 核心功能

### 1. 参数缺失处理
- 接收 SupervisorNode 转换的参数补全上下文
- 从 [ParameterMissingRequest](mdc:src/main/java/cn/mlamp/insightflow/cms/graph/context/ParameterMissingRequest.java) 转换为补全流程
- 分析哪些参数是必需的，识别已有参数和缺失参数
- 支持自动和手动两种参数补全触发方式

### 2. 智能参数提取
- 使用大模型从用户自然语言输入中提取参数
- 支持单个和批量参数识别
- 智能匹配参数类型和格式
- 提供参数格式示例和说明

### 3. 多层参数验证
- **类型验证**：检查参数是否符合预期类型
- **枚举验证**：验证枚举值是否在允许范围内
- **格式验证**：使用正则表达式验证参数格式
- **范围验证**：检查数值参数是否在指定范围内
- **业务验证**：执行特定的业务逻辑验证

### 4. 用户友好交互
- 生成清晰的参数补全提示
- 逐个或批量引导用户提供缺失参数
- 支持参数重新输入和修正
- 提供详细的错误提示和修正建议

### 5. 跳出机制支持
- 检测用户取消意图（关键词检测）
- 识别新任务请求（区分取消和新任务）
- 支持用户随时跳出参数补全流程
- 处理用户转向其他任务的情况

## 参数补全工作流

### 1. 触发方式

ParameterExtractorNode支持多种参数补全触发方式：

#### 自动触发方式一：Worker节点直接提交
Worker节点检测参数缺失并提交 `ParameterMissingRequest`：

```java
// Worker节点中的自动参数检测
@Override
protected Map<String, Object> run(String runId, String query, CMSGraphState state, CallbackManager callbackManager) throws Exception {
    
    // 检查完整参数
    Optional<Map<String, Object>> completeParams = getCompleteParams(state);
    if (completeParams.isPresent()) {
        return executeWithParams(completeParams.get());
    }
    
    // 检测缺失参数并自动提交请求
    List<ParameterMissingRequest.ParameterSpec> missingParams = detectMissingParams(query);
    if (!missingParams.isEmpty()) {
        return requestMissingParameters(state, "执行任务", missingParams);
    }
    
    return executeTask();
}
```

#### 自动触发方式二：Agent响应字段（推荐）
Agent节点通过在Response对象中设置 `ParameterMissingRequest` 字段来触发参数补全，详细实现请参考 [Tools规则文档](mdc:.cursor/rules/modules/graph/tools.mdc)：

```java
// Agent工具检测参数缺失
@Tool("创建任务，如果参数不足则自动触发参数补全")
public String createTask(@P("参数1") String param1, @P("参数2") String param2) {
    // 检测缺失参数
    List<ParameterMissingRequest.ParameterSpec> missingParams = detectMissingParams(param1, param2);
    
    if (!missingParams.isEmpty()) {
        // 返回特殊标记，Agent将解析并设置到Response中
        ParameterMissingRequest request = ParameterMissingRequest.builder()
            .targetNode("currentNode")
            .originalGoal("创建任务")
            .missingParams(missingParams)
            .build();
        
        return "PARAMETER_MISSING_REQUEST:" + JSONUtil.toJsonStr(request);
    }
    
    // 正常执行
    return executeTask(param1, param2);
}
```

#### 手动触发方式
Tool或Worker节点可以手动构建 `ParameterMissingRequest`：

```java
// 手动构建参数缺失请求
ParameterMissingRequest request = ParameterMissingRequest.builder()
    .targetNode("customWorker")
    .originalGoal("执行自定义任务")
    .missingParams(Arrays.asList(
        ParameterMissingRequest.ParameterSpec.builder()
            .name("customParam")
            .displayName("自定义参数")
            .description("用户自定义的参数值")
            .type(ParameterMissingRequest.ParameterType.STRING)
            .required(true)
            .example("示例值")
            .hint("请输入自定义参数")
            .build()
    ))
    .existingParams(existingParamsMap)
    .build();

// 提交到状态中
Map<String, Object> result = new HashMap<>();
result.put(CMSGraphState.PARAMETER_MISSING_REQUEST_KEY, request);
result.put(CMSGraphState.NEXT_KEY, SupervisorNode.NAME);
return result;
```

### 2. 参数收集流程

1. **接收上下文**：从 SupervisorNode 接收转换后的 `ParameterCompletionContext`
2. **检测取消**：检查用户是否想要取消参数补全
3. **智能提取**：使用大模型从用户输入中提取参数
4. **验证处理**：多层验证提取的参数
5. **状态更新**：更新参数补全上下文
6. **完成检查**：检查是否所有必需参数都已收集
7. **继续或完成**：返回继续收集或完成状态

## 技术实现详解

### 参数补全上下文

使用 [ParameterCompletionContext](mdc:src/main/java/cn/mlamp/insightflow/cms/graph/context/ParameterCompletionContext.java) 管理补全状态：

```java
public class ParameterCompletionContext {
    private String targetNode;                    // 目标执行节点
    private String originalGoal;                  // 原始任务目标
    private List<ParameterSpec> missingParams;    // 缺失的参数规格
    private Map<String, Object> existingParams;  // 已存在的参数
    private CompletionStatus status;              // 补全状态
    private LocalDateTime startTime;              // 开始时间
    private LocalDateTime lastUpdateTime;         // 最后更新时间
    private String currentPrompt;                 // 当前提示信息
}
```

### 核心执行逻辑

```java
@Override
public Map<String, Object> run(String runId, String query, CMSGraphState state, CallbackManager callbackManager) throws Exception {
    
    // 1. 获取参数补全上下文
    ParameterCompletionContext completionContext = state.getParameterCompletionContext()
        .orElseThrow(() -> new IllegalStateException("参数补全上下文不存在"));
    
    // 2. 检测用户取消意图
    if (isUserCancellation(query, completionContext)) {
        return handleUserCancellation(completionContext, state);
    }
    
    // 3. 从用户输入中提取参数
    Map<String, Object> extractedParams = extractParametersFromInput(query, completionContext);
    
    // 4. 验证和添加参数到上下文
    ValidationResult validation = validateAndAddParameters(extractedParams, completionContext);
    
    // 5. 检查是否还有缺失的必需参数
    if (completionContext.isCompleted()) {
        return completeParameterCollection(completionContext, state);
    } else {
        return continueParameterCollection(completionContext, validation, state, callbackManager);
    }
}
```

### 智能参数提取

#### 构建提取提示词
```java
private String buildParameterExtractionPrompt(String userInput, List<ParameterCompletionContext.ParameterSpec> missingParams) {
    StringBuilder prompt = new StringBuilder();
    prompt.append("你是一个参数提取助手，需要从用户输入中提取指定的参数。\n\n");
    prompt.append("当前缺失的参数：\n");

    for (ParameterCompletionContext.ParameterSpec param : missingParams) {
        prompt.append("- ").append(param.getName()).append("（").append(param.getDisplayName()).append("）：")
              .append(param.getDescription()).append("\n");
        
        if (param.getType() != null) {
            prompt.append("  类型：").append(param.getType()).append("\n");
        }
        
        if (param.getEnumValues() != null && !param.getEnumValues().isEmpty()) {
            prompt.append("  可选值：").append(String.join("、", param.getEnumValues())).append("\n");
        }
        
        if (param.getExample() != null) {
            prompt.append("  示例：").append(param.getExample()).append("\n");
        }
        prompt.append("\n");
    }

    prompt.append("请从用户输入中提取这些参数的值。如果某个参数在输入中没有明确提及，不要猜测，直接忽略该参数。\n");
    prompt.append("使用 extract_parameters 工具返回提取的参数。");

    return prompt.toString();
}
```

#### 大模型调用
```java
private Map<String, Object> extractParametersFromInput(String userInput, ParameterCompletionContext completionContext) throws Exception {
    List<ParameterCompletionContext.ParameterSpec> missingParams = completionContext.getMissingParams()
        .stream()
        .filter(param -> !completionContext.getExistingParams().containsKey(param.getName()))
        .toList();

    if (missingParams.isEmpty()) {
        return new HashMap<>();
    }

    // 构建参数提取提示词
    String extractionPrompt = buildParameterExtractionPrompt(userInput, missingParams);

    // 调用大模型进行参数提取
    ChatResponse response = chatModel.chat(
        ChatRequest.builder()
            .messages(List.of(
                SystemMessage.from(extractionPrompt),
                UserMessage.from(userInput)
            ))
            .toolSpecifications(List.of(buildParameterExtractionTool(missingParams)))
            .build()
    );

    // 解析提取结果
    return parseParameterExtractionResult(response);
}
```

### 多层参数验证

#### 验证框架
```java
private ValidationResult validateAndAddParameters(
    Map<String, Object> extractedParams, 
    ParameterCompletionContext completionContext) {
    
    ValidationResult result = new ValidationResult();
    
    for (Map.Entry<String, Object> entry : extractedParams.entrySet()) {
        String paramName = entry.getKey();
        Object value = entry.getValue();
        
        // 找到对应的参数规格
        ParameterCompletionContext.ParameterSpec spec = completionContext.getMissingParams()
            .stream()
            .filter(p -> p.getName().equals(paramName))
            .findFirst()
            .orElse(null);
            
        if (spec != null) {
            ValidationError error = validateParameter(paramName, value, spec);
            if (error == null) {
                // 验证通过，添加到上下文
                completionContext.addParameter(paramName, value);
                result.addSuccess(paramName, value);
            } else {
                result.addError(error);
            }
        }
    }
    
    return result;
}
```

#### 具体验证逻辑
```java
private ValidationError validateParameter(String paramName, Object value, ParameterCompletionContext.ParameterSpec spec) {
    
    // 1. 基本类型检查
    if (!isValidType(value, spec.getType())) {
        return new ValidationError(paramName, "参数类型不正确，期望：" + spec.getType());
    }
    
    // 2. 枚举值检查
    if (spec.getEnumValues() != null && !spec.getEnumValues().isEmpty()) {
        if (!spec.getEnumValues().contains(value.toString())) {
            return new ValidationError(paramName, 
                "参数值必须是以下之一：" + String.join("、", spec.getEnumValues()));
        }
    }
    
    // 3. 正则表达式验证
    if (spec.getPattern() != null && value instanceof String) {
        if (!Pattern.matches(spec.getPattern(), (String) value)) {
            return new ValidationError(paramName, "参数格式不正确");
        }
    }
    
    // 4. 数值范围检查
    if (spec.getRange() != null && value instanceof Number) {
        if (!spec.getRange().isInRange((Number) value)) {
            return new ValidationError(paramName, 
                String.format("参数值必须在 %s 范围内", spec.getRange()));
        }
    }
    
    return null; // 验证通过
}
```

### 类型验证实现

支持多种参数类型的验证：

```java
private boolean isValidType(Object value, ParameterCompletionContext.ParameterType expectedType) {
    if (value == null || expectedType == null) {
        return true;
    }

    switch (expectedType) {
        case STRING:
        case ENUM:
        case URL:
        case EMAIL:
        case PHONE:
        case FILE_ID:
            return value instanceof String;
        case INTEGER:
            return value instanceof Integer || (value instanceof String && isInteger((String) value));
        case DECIMAL:
            return value instanceof Number || (value instanceof String && isNumber((String) value));
        case BOOLEAN:
            return value instanceof Boolean || (value instanceof String && isBoolean((String) value));
        case DATE:
        case DATE_RANGE:
            return value instanceof String; // 简化处理，假设日期以字符串形式传入
        case ARRAY:
            return value instanceof List;
        case OBJECT:
        case JSON:
            return value instanceof Map || value instanceof String;
        default:
            return true;
    }
}
```

### 用户友好提示生成

#### 单个参数提示
```java
private String generateParameterCompletionPrompt(ParameterCompletionContext completionContext) {
    List<ParameterCompletionContext.ParameterSpec> missingParams = completionContext.getMissingParams()
        .stream()
        .filter(param -> !completionContext.getExistingParams().containsKey(param.getName()))
        .toList();

    if (missingParams.isEmpty()) {
        return "参数收集完成！";
    }

    StringBuilder prompt = new StringBuilder();

    if (missingParams.size() == 1) {
        // 单个参数补全
        ParameterCompletionContext.ParameterSpec param = missingParams.get(0);
        prompt.append("请提供").append(param.getDisplayName()).append("：\n");
        prompt.append(param.getDescription()).append("\n");

        if (param.getExample() != null) {
            prompt.append("示例：").append(param.getExample()).append("\n");
        }

        if (param.getEnumValues() != null && !param.getEnumValues().isEmpty()) {
            prompt.append("可选值：").append(String.join("、", param.getEnumValues())).append("\n");
        }

        if (param.getHint() != null) {
            prompt.append("提示：").append(param.getHint()).append("\n");
        }

    } else {
        // 多个参数补全
        prompt.append("还需要以下参数：\n");
        for (int i = 0; i < missingParams.size(); i++) {
            ParameterCompletionContext.ParameterSpec param = missingParams.get(i);
            prompt.append(i + 1).append(". ").append(param.getDisplayName())
                  .append("：").append(param.getDescription()).append("\n");

            if (param.getExample() != null) {
                prompt.append("   示例：").append(param.getExample()).append("\n");
            }
        }
        prompt.append("\n您可以一次性提供多个参数，或者逐个提供。");
    }

    return prompt.toString();
}
```

#### 错误提示生成
```java
private Map<String, Object> continueParameterCollection(
    ParameterCompletionContext completionContext,
    ValidationResult validation,
    CMSGraphState state,
    CallbackManager callbackManager) throws Exception {

    // 生成友好的参数补全提示
    String prompt = generateParameterCompletionPrompt(completionContext);

    // 如果有验证错误，添加到提示中
    if (validation.hasErrors()) {
        StringBuilder errorPrompt = new StringBuilder();
        errorPrompt.append("发现以下问题：\n");
        for (ValidationError error : validation.getErrors()) {
            errorPrompt.append("- ").append(error.getMessage()).append("\n");
        }
        errorPrompt.append("\n").append(prompt);
        prompt = errorPrompt.toString();
    }

    // 更新上下文并发送提示
    completionContext.updatePrompt(prompt);
    
    String chatRunId = UUID.fastUUID().toString();
    callbackManager.onMessage(chatRunId, prompt);
    callbackManager.onMessageEnd(chatRunId, prompt);

    Map<String, Object> result = new HashMap<>();
    result.put("status", "AWAITING_USER_INPUT");
    result.put("prompt", prompt);
    result.put(CMSGraphState.PARAMETER_COMPLETION_CONTEXT_KEY, completionContext);
    result.put(CMSGraphState.NEXT_KEY, NAME);

    return result;
}
```

### 用户取消检测

#### 多层取消检测
```java
private boolean isUserCancellation(String userInput, ParameterCompletionContext completionContext) {
    // 1. 检测取消意图的关键词
    String[] cancelKeywords = {"取消", "不用了", "算了", "重新开始", "换个话题", "退出", "停止"};
    String lowerInput = userInput.toLowerCase();

    for (String keyword : cancelKeywords) {
        if (lowerInput.contains(keyword)) {
            return true;
        }
    }

    // 2. 检测是否是全新的任务请求（与当前参数补全无关）
    return isNewIndependentTask(userInput, completionContext);
}

private boolean isNewIndependentTask(String userInput, ParameterCompletionContext completionContext) {
    // 检查是否包含明显的新任务关键词
    String[] newTaskKeywords = {"帮我", "我想要", "请", "生成", "分析", "搜索", "推荐", "创建"};
    String lowerInput = userInput.toLowerCase();

    // 如果用户输入包含新任务关键词，且不包含当前参数相关内容，则认为是新任务
    boolean hasNewTaskKeyword = false;
    for (String keyword : newTaskKeywords) {
        if (lowerInput.contains(keyword)) {
            hasNewTaskKeyword = true;
            break;
        }
    }

    return hasNewTaskKeyword && !isRelatedToCurrentParameters(userInput, completionContext);
}

private boolean isRelatedToCurrentParameters(String userInput, ParameterCompletionContext completionContext) {
    List<ParameterCompletionContext.ParameterSpec> missingParams = completionContext.getMissingParams();
    String lowerInput = userInput.toLowerCase();

    return missingParams.stream()
        .anyMatch(param -> 
            lowerInput.contains(param.getName().toLowerCase()) ||
            lowerInput.contains(param.getDisplayName().toLowerCase()) ||
            (param.getEnumValues() != null && 
             param.getEnumValues().stream().anyMatch(value -> lowerInput.contains(value.toLowerCase())))
        );
}
```

### 完成和取消处理

#### 参数补全完成
```java
private Map<String, Object> completeParameterCollection(ParameterCompletionContext completionContext, CMSGraphState state) {
    log.info("参数补全完成");

    // 标记上下文为完成状态
    completionContext.markCompleted();

    Map<String, Object> result = new HashMap<>();
    result.put("status", "PARAMETERS_COMPLETE");
    result.put("completeParams", completionContext.getAllParams());
    result.put("targetNode", completionContext.getTargetNode());
    result.put("originalGoal", completionContext.getOriginalGoal());

    // 更新状态中的上下文，返回给SupervisorNode处理
    result.put(CMSGraphState.PARAMETER_COMPLETION_CONTEXT_KEY, completionContext);
    result.put(CMSGraphState.NEXT_KEY, SupervisorNode.NAME);

    return result;
}
```

#### 用户取消处理
```java
private Map<String, Object> handleUserCancellation(ParameterCompletionContext completionContext, CMSGraphState state) {
    log.info("用户取消参数补全");

    // 标记上下文为取消状态
    completionContext.markCancelled();

    Map<String, Object> result = new HashMap<>();
    result.put("status", "USER_CANCELLED");
    result.put(CMSGraphState.NEXT_KEY, SupervisorNode.NAME);
    result.put(CMSGraphState.GOAL_KEY, "用户已取消参数补全，请继续其他操作");

    // 清除参数补全上下文
    result.put(CMSGraphState.PARAMETER_COMPLETION_CONTEXT_KEY, null);

    return result;
}
```

## 支持的参数类型

### 基础类型
- `STRING` - 文本类型，支持基本字符串验证
- `INTEGER` - 整数类型，支持数值范围验证
- `DECIMAL` - 小数类型，支持精度和范围验证
- `BOOLEAN` - 布尔类型，支持true/false识别

### 特殊类型
- `DATE` - 日期类型，支持多种日期格式
- `DATE_RANGE` - 日期范围，支持开始和结束日期
- `ENUM` - 枚举类型，限定可选值范围
- `ARRAY` - 数组类型，支持列表数据

### 业务类型
- `FILE_ID` - 文件ID，用于文件上传场景
- `URL` - URL链接，支持链接格式验证
- `EMAIL` - 邮箱地址，支持邮箱格式验证
- `PHONE` - 电话号码，支持号码格式验证
- `JSON` - JSON对象，支持复杂数据结构

## 最佳实践

### 1. 参数设计
- **清晰命名**：使用有意义的参数名称和显示名称
- **详细描述**：提供清晰的参数用途说明
- **示例值**：为每个参数提供具体的示例
- **友好提示**：添加额外的使用提示信息

### 2. 验证策略
- **分层验证**：从基础类型到业务逻辑的多层验证
- **友好错误**：提供具体、可操作的错误提示
- **格式指导**：在验证失败时给出正确的格式示例
- **重试支持**：允许用户轻松重新输入参数

### 3. 用户体验
- **智能提取**：充分利用大模型的理解能力
- **批量支持**：支持用户一次性提供多个参数
- **逐步引导**：对于复杂参数，提供逐步引导
- **取消机制**：随时允许用户跳出补全流程

### 4. 错误处理
- **优雅降级**：在大模型调用失败时提供降级方案
- **状态恢复**：支持从异常状态中恢复
- **日志记录**：详细记录参数补全过程用于调试
- **监控告警**：对关键错误进行监控和告警

## 辅助类设计

### ValidationResult 验证结果
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public static class ValidationResult {
    private List<ValidationError> errors = new ArrayList<>();
    private Map<String, Object> successParams = new HashMap<>();

    public void addError(ValidationError error) {
        errors.add(error);
    }

    public void addSuccess(String paramName, Object value) {
        successParams.put(paramName, value);
    }

    public boolean hasErrors() {
        return !errors.isEmpty();
    }
}
```

### ValidationError 验证错误
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public static class ValidationError {
    private String parameterName;
    private String message;

    public String getParameterName() {
        return parameterName;
    }

    public String getMessage() {
        return message;
    }
}
```

## 扩展指南

### 1. 添加新的参数类型
1. 在 `ParameterType` 枚举中添加新类型
2. 在 `isValidType` 方法中添加验证逻辑
3. 更新参数类型文档和示例

### 2. 自定义验证规则
1. 扩展 `ParameterSpec` 类添加新的验证字段
2. 在 `validateParameter` 方法中实现验证逻辑
3. 提供相应的错误提示信息

### 3. 增强用户体验
1. 优化大模型提示词提高参数提取准确率
2. 添加更智能的参数推荐机制
3. 支持更复杂的参数依赖关系
4. 实现参数补全的历史记录和模板功能

### 4. 性能优化
1. 缓存常用的参数验证结果
2. 优化大模型调用的频率和方式
3. 实现参数补全的异步处理
4. 添加参数补全过程的进度提示

## 故障排除

### 常见问题
1. **参数补全上下文不存在**
   - 检查 SupervisorNode 是否正确设置了上下文
   - 确认状态传递链路完整

2. **参数验证失败**
   - 检查参数规格定义是否正确
   - 确认验证逻辑是否符合预期
   - 验证正则表达式和范围设置

3. **用户取消检测不准确**
   - 调整取消关键词列表
   - 优化新任务检测逻辑
   - 检查参数相关性判断

4. **大模型参数提取效果差**
   - 优化参数提取提示词
   - 调整工具函数定义
   - 检查参数描述的清晰度

### 调试建议
1. **启用详细日志**：设置DEBUG级别日志查看详细执行过程
2. **检查上下文状态**：验证ParameterCompletionContext的状态变化
3. **验证大模型调用**：检查ChatModel的输入输出
4. **测试各种场景**：覆盖正常、异常、取消等各种用户输入场景

## 集成示例

### Worker节点集成示例
```java
@Slf4j
@Component
public class CustomWorkerNode extends BaseAgentNode {

    @Override
    protected Map<String, Object> run(String runId, String query, CMSGraphState state, CallbackManager callbackManager) throws Exception {
        
        // 1. 检查完整参数
        Optional<Map<String, Object>> completeParams = getCompleteParams(state);
        if (completeParams.isPresent()) {
            return executeWithCompleteParams(completeParams.get());
        }
        
        // 2. 自动检测缺失参数
        List<ParameterMissingRequest.ParameterSpec> missingParams = detectMissingParams(query);
        if (!missingParams.isEmpty()) {
            return requestMissingParameters(state, "执行自定义任务", missingParams);
        }
        
        // 3. 正常执行
        return executeTask(query);
    }
    
    private List<ParameterMissingRequest.ParameterSpec> detectMissingParams(String query) {
        List<ParameterMissingRequest.ParameterSpec> missing = new ArrayList<>();
        
        // 检查各种必需参数
        if (!hasRequiredParam1(query)) {
            missing.add(createParameterSpec(
                "param1", "参数1", "第一个必需参数的描述",
                ParameterMissingRequest.ParameterType.STRING, true,
                null, "示例值1", "请输入参数1的值"
            ));
        }
        
        if (!hasRequiredParam2(query)) {
            missing.add(createParameterSpec(
                "param2", "参数2", "第二个必需参数的描述",
                ParameterMissingRequest.ParameterType.ENUM, true,
                Arrays.asList("选项1", "选项2", "选项3"), "选项1", "请选择一个选项"
            ));
        }
        
        return missing;
    }
}
```

这种设计确保了参数补全节点的智能化、用户友好性和健壮性，为整个Graph系统提供了强大的参数收集能力。
