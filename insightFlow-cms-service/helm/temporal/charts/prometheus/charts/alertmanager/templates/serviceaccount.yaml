{{- if .Values.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "alertmanager.serviceAccountName" . }}
  labels:
    {{- include "alertmanager.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  namespace: {{ include "alertmanager.namespace" . }}
automountServiceAccountToken: {{ .Values.automountServiceAccountToken }}
{{- end }}
