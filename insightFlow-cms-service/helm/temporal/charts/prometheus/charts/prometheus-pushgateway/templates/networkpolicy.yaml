{{- if .Values.networkPolicy }}
apiVersion: {{ include "prometheus-pushgateway.networkPolicy.apiVersion" . }}
kind: NetworkPolicy
metadata:
  labels:
    {{- include "prometheus-pushgateway.defaultLabels" . | nindent 4 }}
  {{- if .Values.networkPolicy.customSelectors }}
  name: ingress-allow-customselector-{{ template "prometheus-pushgateway.name" . }}
  {{- else if .Values.networkPolicy.allowAll }}
  name: ingress-allow-all-{{ template "prometheus-pushgateway.name" . }}
  {{- else -}}
  {{- fail "One of `allowAll` or `customSelectors` must be specified." }}
  {{- end }}
  namespace: {{ template "prometheus-pushgateway.namespace" . }}
spec:
  podSelector:
    matchLabels:
      {{- include "prometheus-pushgateway.selectorLabels" . | nindent 6 }}
  ingress:
    - ports:
      - port: {{ .Values.service.targetPort }}
      {{- with .Values.networkPolicy.customSelectors }}
      from:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
