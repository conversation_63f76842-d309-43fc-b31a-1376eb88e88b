package cn.mlamp.insightflow.cms.enums;


import lombok.Getter;

@Getter
public enum VideoVoiceDataTypeEnum {

    BGM(1, "BGM"),
    TTS_VOICE(2, "音色");

    private final Integer code;
    private final String description;

    VideoVoiceDataTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据 code 获取枚举实例
     */
    public static VideoVoiceDataTypeEnum fromCode(Integer code) {
        for (VideoVoiceDataTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的 DataType: " + code);
    }
}
