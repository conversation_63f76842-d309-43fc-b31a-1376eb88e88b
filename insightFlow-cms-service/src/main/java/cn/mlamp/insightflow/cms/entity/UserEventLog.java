package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;

/**
 * 用户事件日志实体类
 */
@Data
@TableName("cms_user_event_log")
public class UserEventLog {
    /** 主键 自增id */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** TCC的用户Id */
    private Integer userId;

    /** 租户Id */
    private Integer tenantId;

    /** 事件类型，1：登录 */
    private Integer eventType;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 数据逻辑删除标记 */
    private Integer isDeleted;
}