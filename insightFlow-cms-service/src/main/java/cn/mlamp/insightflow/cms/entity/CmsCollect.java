package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 收藏表
 *
 * @TableName cms_collect
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "cms_collect")
@Data
public class CmsCollect extends BaseEntity implements Serializable {
    /**
     * 任务id
     */
    @TableField(value = "task_id")
    private Integer taskId;

    /**
     * 任务类型
     */
    @TableField(value = "task_type")
    private Integer taskType;

    /**
     * 分享id（收藏分享必填）
     */
    @TableField(value = "share_id")
    private Integer shareId;

    /**
     * 新任务id
     */
    @TableField(value = "new_task_id")
    private Integer newTaskId;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private Integer userId;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}