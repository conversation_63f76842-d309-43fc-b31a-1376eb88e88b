package cn.mlamp.insightflow.cms.enums.dam;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * @Author: husuper
 * @CreateTime: 2025-07-22
 */
@Getter
public enum DamProcessTypeEnum implements IEnum<Integer> {

    //处理类型1：手动；2:自动
    HAND(1, "手动处理"),
    AUTO(2, "自动处理");


    @JsonValue
    private final Integer value;
    private final String desc;

    DamProcessTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static DamProcessTypeEnum from(Integer value) {
        if (value == null) {
            return null;
        }
        for (DamProcessTypeEnum type : DamProcessTypeEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown DamProcessTypeEnum value: " + value);
    }


}
