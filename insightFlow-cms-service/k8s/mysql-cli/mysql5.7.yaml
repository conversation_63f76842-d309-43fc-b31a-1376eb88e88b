apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql-cli
  labels:
    app: mysql-cli
spec:
  selector:
    matchLabels:
      app: mysql-cli
  template:
    metadata:
      name: mysql-cli
      labels:
        app: mysql-cli
    spec:
      imagePullSecrets:
        - name: ai-pc-ai-pc-robot
      containers:
        - name: mysql-cli
          image: hub.intra.mlamp.cn/ai-pc/mysql:5.7
          command: ['sh', '-c', 'echo "MySQL client pod is running" && sleep infinity']
          env:
            - name: MYSQL_HOST
              value: "127.0.0.1"
            - name: MYSQL_PORT
              value: "3306"
            - name: MYSQL_USER
              value: "root"
            - name: MYSQL_PASSWORD
              value: "123456"
          resources:
            requests:
              cpu: "500m"
              memory: "1Gi"
            limits:
              cpu: "1"
              memory: "2Gi"