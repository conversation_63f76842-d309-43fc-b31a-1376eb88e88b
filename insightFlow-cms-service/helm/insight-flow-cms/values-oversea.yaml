image:
  repository: tsg-miaoacms-tcr1.tencentcloudcr.com/cms/insightflow-cms-service
  tag: 450889

imagePullSecrets:
  - name: tencentcloudcr

podAnnotations:
  fluentd.active: "true"
  fluentd.index: insight-flow-cms-log
  fluentd.regexp: '^(?<count>[0-9]*): (?<logtime>.*$)'
  fluentd.timeformat: '%Y_%m_%d %H:%M:%S'

api:
  replicaCount: 1
  persistence:
    enabled: false
    mountPath: /local/downloads
  resources:
    requests:
      cpu: 8
      memory: 16Gi
    limits:
      cpu: 16
      memory: 32Gi
  config:
    logLevel: debug
  volumes: []
  volumeMounts: []
  extraEnv:
    - name: SPRING_CONFIG_ADDITIONAL_LOCATION
      value: file:/app/config/

worker:
  daily:
    replicaCount: 2
    persistence:
      enabled: true
      mountPath: /local/downloads
      storageClassName: "cms-cfs"
      size: 50Gi
    resources:
      requests:
        cpu: 8
        memory: 16G
      limits:
        cpu: 16
        memory: 32G
    config:
      logLevel: debug
      processorsCount: 2
    volumes: []
    volumeMounts: []
    extraEnv:
      - name: SPRING_CONFIG_ADDITIONAL_LOCATION
        value: file:/app/config/
  customer:
    replicaCount: 1
    persistence:
      enabled: true
      mountPath: /local/downloads
      storageClassName: "cms-cfs"
      size: 50Gi
    resources:
      requests:
        cpu: 8
        memory: 16G
      limits:
        cpu: 16
        memory: 32G
    config:
      logLevel: debug
      processorsCount: 2
    volumes: []
    volumeMounts: []
    extraEnv:
      - name: SPRING_CONFIG_ADDITIONAL_LOCATION
        value: file:/app/config/
