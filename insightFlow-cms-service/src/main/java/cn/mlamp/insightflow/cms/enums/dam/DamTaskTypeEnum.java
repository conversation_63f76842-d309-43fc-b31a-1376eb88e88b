package cn.mlamp.insightflow.cms.enums.dam;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.annotation.Nullable;

/**
 * DAM任务类型枚举
 */
@Getter
@AllArgsConstructor
public enum DamTaskTypeEnum implements IEnum<Integer> {

    UPLOAD(1, "上传"),
    // AI分镜素材入库
    AI_SEGMENT(2, "AI分镜素材");

    @JsonValue
    private final Integer code;
    private final String desc;

    @Override
    public Integer getValue() {
        return code;
    }

    @JsonCreator
    @Nullable
    public static DamTaskTypeEnum getByCode(@Nullable Integer code) {
        if (code == null) {
            return null;
        }
        for (DamTaskTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("Invalid value '" + code + "'");
    }
} 