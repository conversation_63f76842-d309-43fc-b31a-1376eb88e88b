{{ if and (index .Values "backups" "s3" "enabled") (index .Values "backups" "s3" "secrets") }}
{{- if or (index .Values "backups" "s3" "secrets" "AWS_ACCESS_KEY_ID") (index .Values "backups" "s3" "secrets" "AWS_SECRET_ACCESS_KEY") }}
apiVersion: v1
kind: Secret
metadata:
  name: backup-s3
  labels:
    app.kubernetes.io/name: weaviate
    app.kubernetes.io/managed-by: helm
type: Opaque
data:
  {{- if index .Values "backups" "s3" "secrets" "AWS_ACCESS_KEY_ID" }}
  AWS_ACCESS_KEY_ID: {{ index .Values "backups" "s3" "secrets" "AWS_ACCESS_KEY_ID" | b64enc }}
  {{- end }}
  {{- if index .Values "backups" "s3" "secrets" "AWS_SECRET_ACCESS_KEY" }}
  AWS_SECRET_ACCESS_KEY: {{ index .Values "backups" "s3" "secrets" "AWS_SECRET_ACCESS_KEY" | b64enc }}
  {{- end }}
{{- end }}
{{ end }}
