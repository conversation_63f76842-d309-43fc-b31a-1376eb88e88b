package cn.mlamp.insightflow.cms.entity;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.mlamp.insightflow.cms.enums.DownloadStatusEnum;
import cn.mlamp.insightflow.cms.enums.LanguageEnum;
import cn.mlamp.insightflow.cms.enums.SourceTypeEnum;
import cn.mlamp.insightflow.cms.thirdparty.api.deepana.data.DeepanaVideoInfoData;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 上传视频信息，相关字段与 {@link QianchuanMaterialVideo} 对应，以便后续对比
 */
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_pull_task_deduped_datas")
public class CmsPullTaskDedupedData extends BaseEntity implements Serializable, VideoAnalysisData {

    private static final long serialVersionUID = 1L;

    public static final String CLASS_TYPE = "upload_video";

    @Override
    public String getClassType() {
        return CLASS_TYPE;
    }

    // ========== 基础标识字段 ==========
    // 任务 ID，关联 cms_pull_tasks
    private Integer taskId;
    // ES 唯一 ID（外部系统唯一标识）
    private String esId;
    // 用户 ID
    private Integer userId;
    // 租户 ID
    private Integer tenantId;
    // 视频分析任务Id
    private String dbUniqueId;

    // ========== 内容基础信息 ==========
    // 标题
    private String textTitle;
    // 内容
    private String textContent;
    // 来源
    private String kwSource;
    // 类型
    private Integer type;
    // 品牌
    private String brand;
    // 品牌 - 拼音
    private String brandSpell;
    // 发布时间
    private Date datePublishedAt;
    // 语言，约定为缩写，依靠 LanguageEnum 前后对齐
    private LanguageEnum language;

    // ========== 视频相关信息 ==========
    // 视频原地址
    private String kwUrl;
    // 视频链接
    // TODO: 后续会直接使用 oss id，这个信息可能会弃用
    private String kwVideoUrl;
    // 视频头图
    private String kwHeadImage;
    // 视频时长 ms
    private Long longVideoDuration;
    // 视频语音识别
    private String kwVideoContent;
    // 视频OSS ID
    private String videoOssId;
    // 视频头图OSS ID
    private String videoHeadPicOssId;
    // 视频比例
    private String videoAspectRatio;

    // ========== 互动数据统计 ==========
    // 视频播放数
    private Long playCount;
    // 阅读数
    private Long longViewCount;
    // 点赞数
    private Long longLikeCount;
    // 评论数
    private Long longCommentCount;
    // 转发数
    private Long longRepostCount;
    // 收藏数
    private Long longCollectCount;
    // 互动数
    private Long longInteractCount;

    // ========== 用户信息 ==========
    // 用户昵称
    private String textNickName;
    // 用户头像链接
    private String kwProfileImageUrl;
    // 用户主页链接
    private String kwUserUrl;
    // 发帖用户粉丝数
    private Long longFollowerCount;

    // ========== 业务分类标签 ==========
    // 圈层关键词行业
    private String kwKbIndustry;
    // 圈层二级标签
    private String kwTwoLevelTribeTag;
    // 通用情感 Plus 版
    private String kwCommonSentimentPlus;
    // 数据标签 Plus 版
    private String kwDataTagPlus;
    // 素材形式
    private String materialForm;

    // ========== 创意分析相关 ==========
    // 创意分
    private Float rating;
    // 创意原因
    private String creativeReasons;
    // 产品名称
    private String productName;
    // 卖点
    private String cellingPoint;
    // 受众人群
    private String aimingTribe;
    // 是否高光0：没有1：高光
    private Integer highlight;

    // ========== 数据处理状态 ==========
    // 数据来源类型：1 es，2 上传，3 链接
    private Integer sourceType;
    // 下载状态0：待下载，1：下载中，2：成功，3：失败
    private Integer downloadStatus;
    private String downloadDate;
    // 分析状态0:待分析；1：分析中，2：分析成功，3：分析失败
    private Integer analysisStatus;
    // 是否删除
    private Boolean boolIsDeleted;

    /**
     * 提供下载时的数据构建
     *
     * @param videoInfo     videoInfo
     * @param documentInfo  document info
     * @param videoInfoData 深实数据
     * @return data
     */
    public static CmsPullTaskDedupedData buildBy(CmsVideoInfo videoInfo,
                                                 CmsDocumentInfo documentInfo,
                                                 DeepanaVideoInfoData videoInfoData) {
        CmsPullTaskDedupedData.CmsPullTaskDedupedDataBuilder<?, ?> builder = CmsPullTaskDedupedData.builder();
        builder
                .taskId(videoInfo.getId())
                .esId(videoInfo.getEsId())
                // 视频解码
                // TODO: 明确含义 & 用途
                .type(3)
                .userId(videoInfo.getUserId())
                .tenantId(videoInfo.getTenantId())
                .downloadDate(DateUtil.format(documentInfo.getCreateTime(), DatePattern.NORM_DATE_PATTERN))
                .textContent(documentInfo.getDocName());
        if (videoInfoData != null) {
            builder
                    // 链接上传
                    .sourceType(SourceTypeEnum.LINK.getCode())
                    .downloadStatus(DownloadStatusEnum.DOWNLOADING.getCode())
                    .kwSource(videoInfoData.getPlatform().getValue())
                    .kwUrl(documentInfo.getLink())
                    .createTime(documentInfo.getCreateTime())
                    .videoOssId(videoInfoData.getVideoPath());
            if (videoInfoData.getIsDeleted()) {
                builder.boolIsDeleted(true);
            } else {
                builder
                        .boolIsDeleted(false)
                        .textContent(videoInfoData.getContent())
                        .datePublishedAt(
                                new Date(TimeUnit.SECONDS.toMillis(videoInfoData.getPublishedAt()))
                        )
                        .longRepostCount(videoInfoData.getRepostCount())
                        .longCommentCount(videoInfoData.getCommentCount())
                        .longLikeCount(videoInfoData.getLikeCount())
                        .longInteractCount(videoInfoData.getLikeCount() + videoInfoData.getCommentCount() + videoInfoData.getRepostCount())
                        .longCollectCount(videoInfoData.getCollectCount())
                        .longFollowerCount(videoInfoData.getFollowerCount())
                        .textNickName(videoInfoData.getNickName())
                        .kwProfileImageUrl(videoInfoData.getProfileImageUrl())
                        .kwUserUrl(videoInfoData.getUserUrl())
                        .longVideoDuration(
                                TimeUnit.MILLISECONDS.toSeconds(videoInfoData.getVideoDuration())
                        )
                        .kwHeadImage(videoInfoData.getHeadImageUrl())
                        .kwVideoUrl(videoInfoData.getVideoUrl());
            }
        } else {
            builder
                    .sourceType(SourceTypeEnum.UPLOAD.getCode())
                    .videoOssId(documentInfo.getObjId())
                    .downloadStatus(DownloadStatusEnum.SUCCESS.getCode());
        }
        return builder.build();
    }

    @JsonIgnore
    @Override
    public String getVideoId() {
        return esId;
    }

    @JsonIgnore
    @Override
    public String getOssid() {
        return videoOssId;
    }

    @JsonIgnore
    @Override
    public void setOssid(String ossid) {
        this.videoOssId = ossid;
    }

    @JsonIgnore
    @Override
    public String getTitle() {
        return textTitle;
    }

    @JsonIgnore
    @Override
    public String getContent() {
        return textContent;
    }

    @JsonIgnore
    @Override
    public void setOurDuration(Integer ourDuration) {
        this.setLongVideoDuration(Long.valueOf(ourDuration));
    }
}
