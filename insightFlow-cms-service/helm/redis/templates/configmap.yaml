{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if (include "redis.createConfigmap" .) }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ printf "%s-configuration" (include "common.names.fullname" .) }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
data:
  redis.conf: |-
    # User-supplied common configuration:
    {{- if .Values.commonConfiguration }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonConfiguration "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.auth.acl.enabled }}
    # The file with Redis ACL rules
    aclfile /opt/bitnami/redis/etc/users.acl
    {{- end }}
    # End of common configuration
  master.conf: |-
    dir {{ .Values.master.persistence.path }}
    # User-supplied master configuration:
    {{- if .Values.master.configuration }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.master.configuration "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.master.disableCommands }}
    {{- range .Values.master.disableCommands }}
    rename-command {{ . }} ""
    {{- end }}
    {{- end }}
    # End of master configuration
  replica.conf: |-
    dir {{ .Values.replica.persistence.path }}
    # User-supplied replica configuration:
    {{- if .Values.replica.configuration }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.replica.configuration "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.replica.disableCommands }}
    {{- range .Values.replica.disableCommands }}
    rename-command {{ . }} ""
    {{- end }}
    {{- end }}
    # End of replica configuration
  users.acl: |-
    {{- /* User-supplied ACL configuration */ -}}
    {{- if .Values.auth.acl.enabled}}
    {{- /* The default user uses the same password from `redis.password`; otherwise, it sets the nopass value. */ -}}
    {{- $password := include "redis.password" . }}
    user default on {{ if $password}}#{{ sha256sum $password}}{{ else }}nopass{{ end }} ~* &* +@all
    {{- if .Values.auth.acl.users -}}
    {{- /* custom users */ -}}
    {{- $userSecret := .Values.auth.acl.userSecret -}}
    {{- range .Values.auth.acl.users }}
    {{- $userPassword := .password | default "" }}
    {{- if $userSecret }}
    {{- $secretPassword := include "common.secrets.get" (dict "secret" $userSecret "key" .username "context" $) }}
    user {{ .username }} {{ default "on" .enabled }} {{ if $secretPassword }}#{{ sha256sum $secretPassword }}{{ else }}nopass{{ end }} {{ default "~*" .keys }} {{ default "&*" .channels }} {{ default "+@all" .commands }}
    {{- else }}
    user {{ .username }} {{ default "on" .enabled }} {{ if $userPassword }}#{{ sha256sum $userPassword }}{{ else }}nopass{{ end }} {{ default "~*" .keys }} {{ default "&*" .channels }} {{ default "+@all" .commands }}
    {{- end }}
    {{- end }}
    {{- end }}
    {{- end }}
    {{- /* End of ACL configuration */ -}}
  {{- if .Values.sentinel.enabled }}
  sentinel.conf: |-
    dir "/tmp"
    port {{ .Values.sentinel.containerPorts.sentinel }}
    {{- if .Values.sentinel.externalAccess.enabled }}
    {{- if .Values.sentinel.externalAccess.service.loadBalancerIP }}
    sentinel monitor {{ .Values.sentinel.masterSet }} {{ index .Values.sentinel.externalAccess.service.loadBalancerIP 0 }} {{ .Values.sentinel.service.ports.redis }} {{ .Values.sentinel.quorum }}
    {{- end }}
    {{- else }}
    sentinel monitor {{ .Values.sentinel.masterSet }} {{ template "common.names.fullname" . }}-node-0.{{ template "common.names.fullname" . }}-headless.{{ include "common.names.namespace" . }}.svc.{{ .Values.clusterDomain }} {{ .Values.sentinel.service.ports.redis }} {{ .Values.sentinel.quorum }}
    {{- end }}
    sentinel down-after-milliseconds {{ .Values.sentinel.masterSet }} {{ .Values.sentinel.downAfterMilliseconds }}
    sentinel failover-timeout {{ .Values.sentinel.masterSet }} {{ .Values.sentinel.failoverTimeout }}
    sentinel parallel-syncs {{ .Values.sentinel.masterSet }} {{ .Values.sentinel.parallelSyncs }}
    {{- if or .Values.sentinel.masterService.enabled .Values.sentinel.service.createMaster }}
      sentinel client-reconfig-script {{ .Values.sentinel.masterSet }} /opt/bitnami/scripts/start-scripts/push-master-label.sh
    {{- end }}
    {{- if .Values.auth.acl.sentinel }}
    # Sentinel ACL configuration, only for users with password
    {{- $userSecret := .Values.auth.acl.userSecret }}
    {{- range .Values.auth.acl.users }}
    {{- $userPassword := .password | default "" }}
    {{- $secretPassword := ternary "" (include "common.secrets.get" (dict "secret" $userSecret "key" .username "context" $)) (empty $userSecret) }}
    {{- if or $userPassword $secretPassword }}
    user {{ .username }} {{ default "on" .enabled }} #{{ coalesce $secretPassword $userPassword | sha256sum }} ~* &* +@all
    sentinel sentinel-user {{ .username }}
    sentinel sentinel-pass {{ coalesce $secretPassword $userPassword }}
    {{- end }}
    {{- end }}
    {{- end }}
    # User-supplied sentinel configuration:
    {{- if .Values.sentinel.configuration }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.sentinel.configuration "context" $ ) | nindent 4 }}
    {{- end }}
    # End of sentinel configuration
  {{- end }}
{{- end }}
