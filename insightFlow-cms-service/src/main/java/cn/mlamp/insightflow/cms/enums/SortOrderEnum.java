package cn.mlamp.insightflow.cms.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;

/**
 * 排序顺序
 *
 * <AUTHOR>
 */
@Getter
public enum SortOrderEnum implements IEnum<String> {
    ASC,
    DESC;

    @JsonCreator
    public static SortOrderEnum from(String value) {
        return SortOrderEnum.valueOf(value.toUpperCase());
    }

    @Override
    public String getValue() {
        return name();
    }
}
