package cn.mlamp.insightflow.cms.aspect;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import cn.mlamp.insightflow.cms.config.SignCheckProperties;
import cn.mlamp.insightflow.cms.exception.BusinessException;

@Slf4j
@Aspect
@Component
@Order(1) // 优先级高
@RequiredArgsConstructor
public class SignCheckAspect {
    private final SignCheckProperties signCheckProperties;

    @Pointcut("@annotation(cn.mlamp.insightflow.cms.annotation.SignCheck)")
    public void signCheckPointcut() {
    }

    @Before("signCheckPointcut()")
    public void doBefore(JoinPoint joinPoint) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes())
                .getRequest();

        String appId = request.getHeader("appId");
        String sign = request.getHeader("sign");

        if (appId == null || sign == null) {
            throw new BusinessException("签名参数缺失");
        }

        String secret = signCheckProperties.getAppSecrets().get(appId);
        if (secret == null) {
            throw new BusinessException("非法 appId");
        }

        if (secret.equalsIgnoreCase(sign)) {
            return;
        }

        String serverSign = DigestUtils.sha256Hex(appId + secret);

        if (!sign.equalsIgnoreCase(serverSign)) {
            throw new BusinessException("签名验证失败");
        }
    }
}
