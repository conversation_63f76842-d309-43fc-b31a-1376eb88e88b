package cn.mlamp.insightflow.cms.controller.aitou;

import cn.mlamp.insightflow.cms.annotation.SignCheck;
import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.service.aitou.IAiTouService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Map;

@RequestMapping("/ai-tou")
@RestController
public class AiTouController {

    @Lazy
    @Autowired
    private IAiTouService aiTouService;

    @SignCheck
    @PostMapping("/submit")
    public RespBody<Void> submit(@RequestBody(required = false) Map<String, Object> body) throws InterruptedException, IOException {
        aiTouService.submit();
        return RespBody.ok();
    }

}
