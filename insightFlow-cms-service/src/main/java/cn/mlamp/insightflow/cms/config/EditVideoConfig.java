package cn.mlamp.insightflow.cms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@ConfigurationProperties(prefix = "video-edit")
@Configuration
@Data
public class EditVideoConfig {
    private String ttsApiUrl;
    private String ttsAppid;
    private String ttsAccessToken;
    private String bgmAccessKey;
    private String bgmSecretAccessKey;
}
