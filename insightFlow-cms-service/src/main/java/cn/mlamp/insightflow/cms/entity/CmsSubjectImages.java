package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 主体图片表;
 *
 * <AUTHOR> re<PERSON><PERSON><PERSON>
 * @date : 2025-05-12
 */
@Data
@TableName("cms_subject_images")
public class CmsSubjectImages extends BaseEntity {
    /**
     * 主键 自增id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 图片id
     */
    private Integer imageId;

    /**
     * 关联主体ID
     */
    private Integer subjectId;

    /**
     * 文档id
     */
    private Integer docId;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 图片ossId
     */
    private String objId;

}