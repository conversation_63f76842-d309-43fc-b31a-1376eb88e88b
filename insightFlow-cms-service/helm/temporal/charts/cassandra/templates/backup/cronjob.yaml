{{- if .Values.backup.enabled }}
{{- $release := .Release }}
{{- $values := .Values }}
{{- $backup := $values.backup }}
{{- range $index, $schedule := $backup.schedule }}
---
apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: {{ template "cassandra.fullname" $ }}-backup-{{ $schedule.keyspace | replace "_" "-" }}
  labels:
    app: {{ template "cassandra.name" $ }}-cain
    chart: {{ template "cassandra.chart" $ }}
    release: "{{ $release.Name }}"
    heritage: "{{ $release.Service }}"
spec:
  schedule: {{ $schedule.cron | quote }}
  concurrencyPolicy: Forbid
  startingDeadlineSeconds: 120
  jobTemplate:
    spec:
      template:
        metadata:
          annotations:
            {{ toYaml $backup.annotations }}
        spec:
          restartPolicy: OnFailure
          serviceAccountName: {{ template "cassandra.serviceAccountName" $ }}
          containers:
          - name: cassandra-backup
            image: "{{ $backup.image.repository }}:{{ $backup.image.tag }}"
            command: ["cain"]
            args:
            - backup
            - --namespace
            - {{ $release.Namespace }}
            - --selector
            - release={{ $release.Name }},app={{ template "cassandra.name" $ }}
            - --keyspace
            - {{ $schedule.keyspace }}
            - --dst
            - {{ $backup.destination }}
            {{- with $backup.extraArgs }}
{{ toYaml . | indent 12 }}
            {{- end }}
            env:
{{- if $backup.google.serviceAccountSecret }}
            - name: GOOGLE_APPLICATION_CREDENTIALS
              value: "/etc/secrets/google/credentials.json"
{{- end }}
          {{- with $backup.env }}
{{ toYaml . | indent 12 }}
          {{- end }}
          {{- with $backup.resources }}
            resources:
{{ toYaml . | indent 14 }}
          {{- end }}
{{- if $backup.google.serviceAccountSecret }}
            volumeMounts:
            - name: google-service-account
              mountPath: /etc/secrets/google/
{{- end }}
{{- if $backup.google.serviceAccountSecret }}
          volumes:
          - name: google-service-account
            secret:
              secretName: {{ $backup.google.serviceAccountSecret | quote }}
{{- end }}
          affinity:
            podAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - weight: 1
                podAffinityTerm:
                  labelSelector:
                    matchExpressions:
                    - key: app
                      operator: In
                      values:
                      - {{ template "cassandra.fullname" $ }}
                    - key: release
                      operator: In
                      values:
                      - {{ $release.Name }}
                  topologyKey: "kubernetes.io/hostname"
          {{- with $values.tolerations }}
          tolerations:
{{ toYaml . | indent 12 }}
          {{- end }}
{{- end }}
{{- end }}
