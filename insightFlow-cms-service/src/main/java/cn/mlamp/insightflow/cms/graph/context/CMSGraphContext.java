package cn.mlamp.insightflow.cms.graph.context;

import cn.hutool.core.util.StrUtil;
import cn.mlamp.insightflow.cms.graph.nodes.AIScriptNode;
import cn.mlamp.insightflow.cms.graph.nodes.FeaturedVideosNode;
import lombok.Data;

@Data
public class CMSGraphContext {

    private FeaturedVideosNode.FeaturedVideosContext featuredVideosContext = new FeaturedVideosNode.FeaturedVideosContext();

    private AIScriptNode.AIScriptContext aiScriptContext = new AIScriptNode.AIScriptContext();

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder();
        if (featuredVideosContext != null) {
            final String context = featuredVideosContext.toString();
            if (StrUtil.isNotBlank(context)) {
                sb.append("爆款视频: \n").append(context).append("\n");
            }
        }
        if (aiScriptContext != null) {
            final String context = aiScriptContext.toString();
            if (StrUtil.isNotBlank(context)) {
                sb.append("视频脚本: \n").append(context).append("\n");
            }
        }
        return sb.toString();
    }

}
