apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: weaviate
  labels:
    name: weaviate
    app: weaviate
    app.kubernetes.io/name: weaviate
    app.kubernetes.io/managed-by: helm
spec:
  replicas: {{ .Values.replicas }}
  serviceName: {{ .Values.service.name }}-headless
  selector:
    matchLabels:
      app: weaviate
  template:
    metadata:
      labels:
        app: weaviate
        app.kubernetes.io/name: weaviate
        app.kubernetes.io/managed-by: helm
      {{- with .Values.annotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      {{- if (index .Values "serviceAccountName") }}
      serviceAccountName: {{ .Values.serviceAccountName }}
      {{- else if and (index .Values "backups" "s3" "enabled") (index .Values "backups" "s3" "serviceAccountName") }} # for backwards compatibility
      serviceAccountName: {{ .Values.backups.s3.serviceAccountName }}
      {{- end }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
      containers:
      - name: weaviate
        image: '{{ .Values.image.registry }}/{{ .Values.image.repo }}:{{ .Values.image.tag }}'
        imagePullPolicy: Always
        {{- with .Values.command }}
        command: {{ toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.args }}
        args: {{ toYaml . | nindent 10 }}
        {{- end }}
        resources:
{{ toYaml .Values.resources | indent 10 }}
        env:
        {{- if or $.Values.env $.Values.envSecrets }}
          {{- range $key, $value := $.Values.env }}
          - name: {{ $key }}
            value: {{ $value | quote }}
          {{- end }}
          {{- range $key, $secret := $.Values.envSecrets }}
          - name: {{ $key }}
            valueFrom:
              secretKeyRef:
                name: {{ $secret }}
                key: {{ $key | quote }}
          {{- end }}
          {{- end }}
          - name: STANDALONE_MODE
            value: 'true'
          - name: PERSISTENCE_DATA_PATH
            value: '/var/lib/weaviate'
          - name: DEFAULT_VECTORIZER_MODULE
            value: {{ index .Values "modules" "default_vectorizer_module" | trim }}
          {{ template "enabled_modules" . }}
          {{- if index .Values "modules" "text2vec-transformers" "enabled" }}
          - name: TRANSFORMERS_INFERENCE_API
            value: http://{{ index .Values "modules" "text2vec-transformers" "fullnameOverride" }}.{{ .Release.Namespace }}.svc.cluster.local:8080
          {{- else if index .Values "modules" "text2vec-transformers" "inferenceUrl" }}
          - name: TRANSFORMERS_INFERENCE_API
            value: {{ index .Values "modules" "text2vec-transformers" "inferenceUrl" }}
          {{- else }}
              {{- if index .Values "modules" "text2vec-transformers" "passageQueryServices" "passage" "enabled" }}
          - name: TRANSFORMERS_PASSAGE_INFERENCE_API
            value: http://{{ index .Values "modules" "text2vec-transformers" "passageQueryServices" "passage" "fullnameOverride" }}.{{ .Release.Namespace }}.svc.cluster.local:8080
              {{- else if index .Values "modules" "text2vec-transformers" "passageQueryServices" "passage" "inferenceUrl" }}
          - name: TRANSFORMERS_PASSAGE_INFERENCE_API
            value: {{ index .Values "modules" "text2vec-transformers" "passageQueryServices" "passage" "inferenceUrl" }}
              {{- end }}
              {{- if index .Values "modules" "text2vec-transformers" "passageQueryServices" "query" "enabled" }}
          - name: TRANSFORMERS_QUERY_INFERENCE_API
            value: http://{{ index .Values "modules" "text2vec-transformers" "passageQueryServices" "query" "fullnameOverride" }}.{{ .Release.Namespace }}.svc.cluster.local:8080
              {{- else if index .Values "modules" "text2vec-transformers" "passageQueryServices" "query" "inferenceUrl" }}
          - name: TRANSFORMERS_QUERY_INFERENCE_API
            value: {{ index .Values "modules" "text2vec-transformers" "passageQueryServices" "query" "inferenceUrl" }}
              {{- end }}
          {{- end }}
          {{- if index .Values "modules" "multi2vec-clip" "enabled" }}
          - name: CLIP_INFERENCE_API
            value: http://{{ index .Values "modules" "multi2vec-clip" "fullnameOverride" }}.{{ .Release.Namespace }}.svc.cluster.local:8080
          {{- else if index .Values "modules" "multi2vec-clip" "inferenceUrl" }}
          - name: CLIP_INFERENCE_API
            value: {{ index .Values "modules" "multi2vec-clip" "inferenceUrl" }}
          {{- end }}
          {{- if index .Values "modules" "qna-transformers" "enabled" }}
          - name: QNA_INFERENCE_API
            value: http://{{ index .Values "modules" "qna-transformers" "fullnameOverride" }}.{{ .Release.Namespace }}.svc.cluster.local:8080
          {{- else if index .Values "modules" "qna-transformers" "inferenceUrl" }}
          - name: QNA_INFERENCE_API
            value: {{ index .Values "modules" "qna-transformers" "inferenceUrl" }}
          {{- end }}
          {{- if index .Values "modules" "img2vec-neural" "enabled" }}
          - name: IMAGE_INFERENCE_API
            value: http://{{ index .Values "modules" "img2vec-neural" "fullnameOverride" }}.{{ .Release.Namespace }}.svc.cluster.local:8080
          {{- else if index .Values "modules" "img2vec-neural" "inferenceUrl" }}
          - name: IMAGE_INFERENCE_API
            value: {{ index .Values "modules" "img2vec-neural" "inferenceUrl" }}
          {{- end }}
          {{- if index .Values "modules" "text-spellcheck" "enabled" }}
          - name: SPELLCHECK_INFERENCE_API
            value: http://{{ index .Values "modules" "text-spellcheck" "fullnameOverride" }}.{{ .Release.Namespace }}.svc.cluster.local:8080
          {{- else if index .Values "modules" "text-spellcheck" "inferenceUrl" }}
          - name: SPELLCHECK_INFERENCE_API
            value: {{ index .Values "modules" "text-spellcheck" "inferenceUrl" }}
          {{- end }}
          {{- if index .Values "modules" "ner-transformers" "enabled" }}
          - name: NER_INFERENCE_API
            value: http://{{ index .Values "modules" "ner-transformers" "fullnameOverride" }}.{{ .Release.Namespace }}.svc.cluster.local:8080
          {{- else if index .Values "modules" "ner-transformers" "inferenceUrl" }}
          - name: NER_INFERENCE_API
            value: {{ index .Values "modules" "ner-transformers" "inferenceUrl" }}
          {{- end }}
          {{- if index .Values "modules" "sum-transformers" "enabled" }}
          - name: SUM_INFERENCE_API
            value: http://{{ index .Values "modules" "sum-transformers" "fullnameOverride" }}.{{ .Release.Namespace }}.svc.cluster.local:8080
          {{- else if index .Values "modules" "sum-transformers" "inferenceUrl" }}
          - name: SUM_INFERENCE_API
            value: {{ index .Values "modules" "sum-transformers" "inferenceUrl" }}
          {{- end }}
          {{ if or (and (index .Values "modules" "text2vec-openai" "enabled") (index .Values "modules" "text2vec-openai" "apiKey")) (and (index .Values "modules" "qna-openai" "enabled") (index .Values "modules" "qna-openai" "apiKey")) (and (index .Values "modules" "generative-openai" "enabled") (index .Values "modules" "generative-openai" "apiKey")) }}
          - name: OPENAI_APIKEY
            valueFrom:
              secretKeyRef:
                name: weaviate-openai
                key: apiKey
          {{- end  }}
          {{- if and (index .Values "modules" "text2vec-huggingface" "enabled") (index .Values "modules" "text2vec-huggingface" "apiKey") }}
          - name: HUGGINGFACE_APIKEY
            valueFrom:
              secretKeyRef:
                name: weaviate-huggingface
                key: apiKey
          {{- end  }}
          {{- if and (index .Values "modules" "text2vec-cohere" "enabled") (index .Values "modules" "text2vec-cohere" "apiKey") }}
          - name: COHERE_APIKEY
            valueFrom:
              secretKeyRef:
                name: weaviate-cohere
                key: apiKey
          {{- end  }}
          {{- if and (index .Values "backups" "filesystem" "enabled") (index .Values "backups" "filesystem" "envconfig") }}
            {{- range $key, $value := index .Values "backups" "filesystem" "envconfig" }}
          - name: {{ $key }}
            value: {{ $value | quote }}
            {{- end }}
          {{- end  }}
          {{- if index .Values "backups" "s3" "enabled" }}
            {{- if index .Values "backups" "s3" "envconfig" }}
              {{- range $key, $value := index .Values "backups" "s3" "envconfig" }}
          - name: {{ $key }}
            value: {{ $value | quote }}
              {{- end }}
            {{- end }}
            {{- if index .Values "backups" "s3" "envSecrets" }}
              {{- range $key, $secret := $.Values.backups.s3.envSecrets }}
          - name: {{ $key }}
            valueFrom:
              secretKeyRef:
                name: {{ $secret }}
                key: {{ $key | quote }}
              {{- end }}
            {{- end }}
            {{- if and (index .Values "backups" "s3" "secrets") (index .Values "backups" "s3" "secrets" "AWS_ACCESS_KEY_ID") }}
          - name: AWS_ACCESS_KEY_ID
            valueFrom:
              secretKeyRef:
                name: backup-s3
                key: AWS_ACCESS_KEY_ID
            {{- end }}
            {{- if and (index .Values "backups" "s3" "secrets") (index .Values "backups" "s3" "secrets" "AWS_SECRET_ACCESS_KEY") }}
          - name: AWS_SECRET_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                name: backup-s3
                key: AWS_SECRET_ACCESS_KEY
            {{- end }}
          {{- end  }}
          {{- if index .Values "backups" "gcs" "enabled" }}
            {{- if index .Values "backups" "gcs" "envconfig" }}
              {{- range $key, $value := index .Values "backups" "gcs" "envconfig" }}
          - name: {{ $key }}
            value: {{ $value | quote }}
              {{- end }}
            {{- end }}
            {{- if or (index .Values "backups" "gcs" "secrets") (index .Values "backups" "gcs" "envSecrets") }}
              {{- if or (index .Values "backups" "gcs" "secrets" "GOOGLE_APPLICATION_CREDENTIALS") (index .Values "backups" "gcs" "envSecrets" "GOOGLE_APPLICATION_CREDENTIALS") }}
          - name: GOOGLE_APPLICATION_CREDENTIALS
            value: "/etc/gcp/credentials.json"
              {{- end }}
            {{- end }}
          {{- end  }}
          {{- if index .Values "backups" "azure" "enabled" }}
            {{- if index .Values "backups" "azure" "envconfig" }}
              {{- range $key, $value := index .Values "backups" "azure" "envconfig" }}
          - name: {{ $key }}
            value: {{ $value | quote }}
              {{- end }}
            {{- end }}
            {{- if index .Values "backups" "azure" "envSecrets" }}
              {{- range $key, $secret := $.Values.backups.azure.envSecrets }}
          - name: {{ $key }}
            valueFrom:
              secretKeyRef:
                name: {{ $secret }}
                key: {{ $key | quote }}
              {{- end }}
            {{- end }}
            {{- if and (index .Values "backups" "azure" "secrets") (index .Values "backups" "azure" "secrets" "AZURE_STORAGE_ACCOUNT") }}
          - name: AZURE_STORAGE_ACCOUNT
            valueFrom:
              secretKeyRef:
                name: backup-azure
                key: AZURE_STORAGE_ACCOUNT
            {{- end }}
            {{- if and (index .Values "backups" "azure" "secrets") (index .Values "backups" "azure" "secrets" "AZURE_STORAGE_KEY") }}
          - name: AZURE_STORAGE_KEY
            valueFrom:
              secretKeyRef:
                name: backup-azure
                key: AZURE_STORAGE_KEY
            {{- end }}
            {{- if and (index .Values "backups" "azure" "secrets") (index .Values "backups" "azure" "secrets" "AZURE_STORAGE_CONNECTION_STRING") }}
          - name: AZURE_STORAGE_CONNECTION_STRING
            valueFrom:
              secretKeyRef:
                name: backup-azure
                key: AZURE_STORAGE_CONNECTION_STRING
            {{- end }}
          {{- end  }}
          - name: CLUSTER_JOIN
            value: {{ .Values.service.name }}-headless.{{ .Release.Namespace }}.svc.cluster.local
        ports:
          - containerPort: 8080
          {{- if .Values.env.PROMETHEUS_MONITORING_ENABLED }}
          - name: metrics
            containerPort: 2112
            protocol: TCP
          {{- end }}
        volumeMounts:
          - name: weaviate-config
            mountPath: /weaviate-config
          - name: weaviate-data
            mountPath: /var/lib/weaviate
          {{- if index .Values "backups" "gcs" "enabled" }}
            {{- if or (index .Values "backups" "gcs" "secrets") (index .Values "backups" "gcs" "envSecrets") }}
              {{- if or (index .Values "backups" "gcs" "secrets" "GOOGLE_APPLICATION_CREDENTIALS") (index .Values "backups" "gcs" "envSecrets" "GOOGLE_APPLICATION_CREDENTIALS") }}
          - name: gcs-service-account-credentials-volume
            mountPath: /etc/gcp
            readOnly: true
              {{- end }}
            {{- end }}
          {{- end }}
        {{- if .Values.startupProbe.enabled }}
        startupProbe:
          httpGet:
            path: /v1/.well-known/ready
            port: 8080
          initialDelaySeconds: {{ .Values.startupProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.startupProbe.periodSeconds }}
          failureThreshold: {{ .Values.startupProbe.failureThreshold }}
          successThreshold: {{ .Values.startupProbe.successThreshold }}
          timeoutSeconds: {{ .Values.startupProbe.timeoutSeconds }}
        {{- end }}
        livenessProbe:
          httpGet:
            path: /v1/.well-known/live
            port: 8080
          initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
          failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
          successThreshold: {{ .Values.livenessProbe.successThreshold }}
          timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
        readinessProbe:
          httpGet:
            path: /v1/.well-known/ready
            port: 8080
          initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
          failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
          successThreshold: {{ .Values.readinessProbe.successThreshold }}
          timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
      volumes:
        - name: weaviate-config
          configMap:
            {{ if .Values.custom_config_map.enabled }}name: {{ .Values.custom_config_map.name }} {{ else }}name: weaviate-config{{ end }}
        # - name: weaviate-persistence-data-vol
        #   persistentVolumeClaim:
        #     {{ if empty .Values.storage.fullnameOverride }}
        #     claimName: weaviate-persistence-data
        #     {{ else }}
        #     claimName: {{ .Values.storage.fullnameOverride }}
        #     {{ end }}
        {{- if index .Values "backups" "gcs" "enabled" }}
          {{- if and (index .Values "backups" "gcs" "secrets") (index .Values "backups" "gcs" "secrets" "GOOGLE_APPLICATION_CREDENTIALS") }}
        - name: gcs-service-account-credentials-volume
          secret:
            secretName: backup-gcs
            items:
            - key: GOOGLE_APPLICATION_CREDENTIALS
              path: credentials.json
          {{- end }}
          {{- if and (index .Values "backups" "gcs" "envSecrets") (index .Values "backups" "gcs" "envSecrets" "GOOGLE_APPLICATION_CREDENTIALS") }}
        - name: gcs-service-account-credentials-volume
          secret:
            secretName: {{ index .Values "backups" "gcs" "envSecrets" "GOOGLE_APPLICATION_CREDENTIALS" }}
            items:
            - key: GOOGLE_APPLICATION_CREDENTIALS
              path: credentials.json
          {{- end }}
        {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{ toYaml . | nindent 8 }}
      {{- end }}

  volumeClaimTemplates:
  - metadata:
      name: weaviate-data
      labels:
        app.kubernetes.io/name: weaviate
        app.kubernetes.io/managed-by: helm
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: {{ .Values.storage.storageClassName }}
      resources:
        requests:
          storage: {{ .Values.storage.size }}
