package cn.mlamp.insightflow.cms.config;


import cn.mlamp.insightflow.cms.common.resp.ErrorBody;
import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.common.resp.RespCode;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.exception.RateLimitException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.io.Serializable;

/**
 * 全局Facade通知处理
 * 主要做两个事情 1. 全局异常拦截并处理  2.DTO参数绑定
 */
@Slf4j
@RestControllerAdvice
@Component
public class GlobalFacadeAdvice {
    /**
     * INTERNAL_SERVER_ERROR RespCode
     */
    private static final RespCode INTERNAL_SERVER_ERROR = RespCode.INTERNAL_SERVER_ERROR;
    /**
     * BAD_REQUEST RepsCode
     */
    private static final RespCode BAD_REQUEST = RespCode.BAD_REQUEST;

    /**
     * 自定义 业务异常
     */
    @ExceptionHandler(value = BusinessException.class)
    public RespBody<Serializable> businessException(BusinessException e) {
        log.error(e.getMessage(), e);
        return RespBody.build(e.getStateCode(), e.getMessage(), e.getData());
    }

    /**
     * 参数验证异常
     */
    @ExceptionHandler(value = BindException.class)
    public RespBody<ErrorBody> bindException(BindException e) {
        log.error(e.getMessage(), e);
        BindingResult bindingResult = e.getBindingResult();
        String defaultMessage = null;
        for (ObjectError error : bindingResult.getAllErrors()) {
            if (error instanceof FieldError) {
                FieldError field = (FieldError) error;
                defaultMessage = field.getDefaultMessage();
                break;
            }
        }
        if (defaultMessage == null) {
            defaultMessage = BAD_REQUEST.getMessage();
        }
        return RespBody.error(BAD_REQUEST, defaultMessage, null);
    }

    /**
     * 内部服务器异常
     */
    @ExceptionHandler(value = {Exception.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public RespBody<ErrorBody> exception(Exception e) {
        log.error(e.getMessage(), e);
        return RespBody.error(INTERNAL_SERVER_ERROR, INTERNAL_SERVER_ERROR.getMessage(), null);
    }

    /**
     * 限流异常处理
     */
    @ExceptionHandler(value = RateLimitException.class)
    @ResponseStatus(HttpStatus.TOO_MANY_REQUESTS)
    public RespBody<ErrorBody> rateLimitException(RateLimitException e) {
        log.error("触发限流: {}", e.getMessage());
        return RespBody.error(RespCode.TOO_MANY_REQUESTS, e.getMessage(), null);
    }

}
