package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务表;
 *
 * <AUTHOR> husper
 * @date : 2025-3-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_task_info")
public class CmsTaskInfo extends BaseEntity {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 视频id
     */
    private String videoId;
    /**
     * 来源类型（1.上传视频 2.千川视频）
     */
    private Integer sourceType;
    /**
     * 任务名称
     */
    private String name;
    /**
     * 任务状态
     */
    private Integer taskStatus;
    /**
     * 任务类型
     */
    private Integer taskType;
    /**
     * 错误原因
     */
    private String errorMessage;
    /**
     * 错误码
     */
    private String errorCode;
    /**
     * JSON
     */
    private String taskArg;
    /**
     * 原视频分析id列表
     */
    private String sourceVideoIds;
    /**
     * 结果文档id列表
     */
    private String resultFileIds;
    /**
     * 任务额外信息，json存储
     */
    private String extra;
    /**
     * 收藏id
     */
    private Integer collectId;
    /**
     * 列表类型
     */
    private Integer listType;
    /**
     * 创建人id
     */
    private Integer userId;
    /**
     * 租户Id
     */
    private Integer tenantId;
}
