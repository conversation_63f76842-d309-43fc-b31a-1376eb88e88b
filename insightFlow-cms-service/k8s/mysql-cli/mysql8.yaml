apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql-cli
  labels:
    app: mysql-cli
spec:
  selector:
    matchLabels:
      app: mysql-cli
  template:
    metadata:
      name: mysql-cli
      labels:
        app: mysql-cli
    spec:
      containers:
        - name: mysql-cli
          image: mysql:8.0
          command: ['sh', '-c', 'echo "MySQL client pod is running" && sleep infinity']
          env:
            - name: MYSQL_HOST
              value: "**********"
            - name: MYSQL_PORT
              value: "3306"
            - name: MYSQL_USER
              value: "root"
            - name: MYSQL_PASSWORD
              value: "sSG8A#Dh7ahFxcyLmjXs"
          resources:
            requests:
              cpu: "500m"
              memory: "1Gi"
            limits:
              cpu: "1"
              memory: "2Gi"