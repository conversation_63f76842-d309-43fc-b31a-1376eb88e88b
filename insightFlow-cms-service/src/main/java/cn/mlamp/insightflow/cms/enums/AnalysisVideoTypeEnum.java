package cn.mlamp.insightflow.cms.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-24
 */

@AllArgsConstructor
@Getter
public enum AnalysisVideoTypeEnum {

    // 新增的枚举实例
    VIDEO_ANALYSIS("video_analysis", 1, "视频分析"), // 视频分析

//    GOLDEN_FIVE_SECONDS("golden_five_seconds", 2, "黄金5秒"); // 黄金5秒

    //    TRENDING_TOPICS("trending_topics", 3, "圈层热点"), // 圈层热点，这里为了区分已有的圈层热点，使用了不同的描述名称
//
    UPLOAD_VIDEO("upload_video", 4, "上传视频"), // 上传视频

    //Result中type=5是前5秒的ASR
    QIANCHUAN_VIDEO("qianchuan_video", 5, "千川视频"); // 上传视频


    private final String videoType;

    private final Integer videoCode;

    private final String showVideoType;

    public static Boolean contains(String videoType) {
        return Arrays.stream(values()).anyMatch(t -> t.getVideoType().equals(videoType));
    }

    public static Boolean contains(Integer videoCode) {
        return Arrays.stream(values()).anyMatch(t -> t.getVideoCode().equals(videoCode));
    }

    public static String getTypeByCode(Integer videoCode) {
        return Arrays.stream(values()).filter(t -> t.getVideoCode().equals(videoCode)).findFirst()
                .map(AnalysisVideoTypeEnum::getVideoType).orElse(null);
    }

    public static Integer getCodeByType(String videoType) {
        return Arrays.stream(values()).filter(t -> t.getVideoType().equals(videoType)).findFirst()
                .map(AnalysisVideoTypeEnum::getVideoCode).orElse(null);
    }

    @JsonCreator
    public static AnalysisVideoTypeEnum getByVideoTypeStr(String videoType) {
        for (AnalysisVideoTypeEnum type : AnalysisVideoTypeEnum.values()) {
            if (type.getVideoType().equals(videoType)) {
                return type;
            }
        }
        return null;
    }


    public static AnalysisVideoTypeEnum getByCode(Integer code) {
        for (AnalysisVideoTypeEnum typeEnum : AnalysisVideoTypeEnum.values()) {
            if (typeEnum.getVideoCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
