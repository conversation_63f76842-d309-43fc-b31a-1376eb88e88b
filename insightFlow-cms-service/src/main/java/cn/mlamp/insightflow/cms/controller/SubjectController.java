package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.common.resp.RespCode;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.model.query.SubjectCreateConfirmRequest;
import cn.mlamp.insightflow.cms.model.query.SubjectCreateUploadImageRequest;
import cn.mlamp.insightflow.cms.model.query.SubjectListRequest;
import cn.mlamp.insightflow.cms.model.vo.SubjectImageAnalyseVO;
import cn.mlamp.insightflow.cms.model.vo.SubjectInfoVO;
import cn.mlamp.insightflow.cms.model.vo.SubjectListVO;
import cn.mlamp.insightflow.cms.model.vo.SubjectTagsVo;
import cn.mlamp.insightflow.cms.service.ISubjectImagesService;
import cn.mlamp.insightflow.cms.service.ISubjectInfoService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @Author: renguangzong
 * @CreateTime: 2025-05-09
 */
@Slf4j
@RequestMapping("/subject")
@RestController
@RequiredArgsConstructor
@Tag(name = "主体库相关接口")
public class SubjectController {

    @Resource
    private ISubjectInfoService subjectInfoService;

    @Resource
    private ISubjectImagesService subjectImagesService;

    @GetMapping("/list")
    @Operation(summary = "主体库查询接口")
    public RespBody<Page<SubjectListVO>> getSubjectsById(SubjectListRequest subjectListRequest) {
        return RespBody.ok(subjectInfoService.getList(subjectListRequest));
    }


    @PostMapping("/add/uploadImage")
    @Operation(summary = "主体库添加/编辑-上传图片")
    public RespBody<SubjectImageAnalyseVO> addSubjectUploadImage(@RequestBody SubjectCreateUploadImageRequest subjectCreateUploadImageRequest) {
        if (subjectCreateUploadImageRequest.getSubjectName() == null) {
            throw new BusinessException(RespCode.BAD_REQUEST.getCode(), "主体名称不能为空");
        }
        if (subjectCreateUploadImageRequest.getOssIds().isEmpty() || subjectCreateUploadImageRequest.getOssIds().size() == 0) {
            throw new BusinessException(RespCode.BAD_REQUEST.getCode(), "主体图片至少上传一张");
        }
        return RespBody.ok(subjectImagesService.analyseImage(subjectCreateUploadImageRequest));
    }


    @GetMapping("/tagList")
    @Operation(summary = "主体库标签查询接口")
    public RespBody<SubjectTagsVo> getSubjectTags() {
        return RespBody.ok(subjectInfoService.getTagList());
    }


    @PostMapping("/add/confirm")
    @Operation(summary = "主体库添加/编辑-确认描述")
    public RespBody<?> addSubjectConfirmDescription(@RequestBody SubjectCreateConfirmRequest subjectCreateConfirmRequest) {
        if (subjectCreateConfirmRequest.getSubjectStyle() == null) {
            throw new BusinessException(RespCode.BAD_REQUEST.getCode(), "主体风格不能为空");
        }
        if (subjectCreateConfirmRequest.getSubjectDescription() == null) {
            throw new BusinessException(RespCode.BAD_REQUEST.getCode(), "主体描述不能为空");
        }
        subjectInfoService.addOrUpdateSubjectConfirmDescription(subjectCreateConfirmRequest);
        if (subjectCreateConfirmRequest.getSubjectId() != null) {
            return RespBody.ok("主体编辑成功");
        }
        return RespBody.ok("主体创建成功");
    }

    @DeleteMapping("/delete")
    @Operation(summary = "主体库删除")
    public RespBody<?> deleteSubject(@RequestBody List<Integer> ids) {
        subjectInfoService.deleteBatchByIds(ids);
        return RespBody.ok("主体删除成功");
    }

    @GetMapping("/detail/{subjectId}")
    @Operation(summary = "主体详情查看接口")
    public RespBody<SubjectInfoVO> getSubject(@PathVariable Integer subjectId) {
        if (subjectId == null) {
            throw new BusinessException(RespCode.BAD_REQUEST.getCode(), "主体id不能为空");
        }
        return RespBody.ok(subjectInfoService.getSubjectDetail(subjectId));
    }


}
