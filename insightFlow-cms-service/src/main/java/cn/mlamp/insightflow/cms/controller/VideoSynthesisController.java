package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.model.query.VideoSynthesisDetailUpdateRequest;
import cn.mlamp.insightflow.cms.model.query.VideoSynthesisListRequest;
import cn.mlamp.insightflow.cms.model.query.VideoSynthesisRequest;
import cn.mlamp.insightflow.cms.model.vo.VideoSynthesisListItemVO;
import cn.mlamp.insightflow.cms.model.vo.VideoSynthesisVO;
import cn.mlamp.insightflow.cms.service.IVideoSynthesisService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import jakarta.servlet.http.HttpServletResponse;

/**
 * 视频合成控制器
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/video/synthesis2")
@Tag(name = "视频合成接口")
public class VideoSynthesisController {

    private final IVideoSynthesisService videoSynthesisService;

    @PostMapping("/create")
    @Operation(summary = "创建视频合成任务")
    public RespBody<VideoSynthesisVO> createSynthesisTask(@RequestBody VideoSynthesisRequest request) {
        // 设置用户ID和租户ID
        request.setUserId(UserContext.getUserId());
        request.setTenantId(UserContext.getTenantId());

        // 创建视频合成任务
        VideoSynthesisVO result = videoSynthesisService.createSynthesisTask(request);
        // 异步执行视频合成任务
//        videoSynthesisService.synthesizeVideoAsync(request, result.getTaskId());
        return RespBody.ok(result);
    }

    @GetMapping("/detail/{taskId}")
    @Operation(summary = "获取视频合成任务详情")
    public RespBody<VideoSynthesisVO> getSynthesisTaskDetail(@PathVariable Integer taskId) {
        VideoSynthesisVO result = videoSynthesisService.getSynthesisTaskDetail(taskId);
        return RespBody.ok(result);
    }

    @GetMapping("/download/{taskId}")
    @Operation(summary = "下载视频合成结果")
    public RespBody<String> downloadSynthesisResult(@PathVariable Integer taskId) {
        String downloadUrl = videoSynthesisService.getDownloadUrl(taskId);
        return RespBody.ok(downloadUrl);
    }

    @GetMapping("/download/segments/{taskId}")
    @Operation(summary = "下载合成分段视频列表")
    public RespBody<List<String>> downloadSegmentVideos(@PathVariable Integer taskId) {
        List<String> downloadUrls = videoSynthesisService.getSegmentVideoDownloadUrls(taskId);
        return RespBody.ok(downloadUrls);
    }

    @GetMapping("/list")
    @Operation(summary = "获取视频合成任务列表")
    public RespBody<Page<VideoSynthesisListItemVO>> getSynthesisTaskList(VideoSynthesisListRequest request,
                                                                 @RequestParam(value = "listType", defaultValue = "1") Integer listType) {
        // 设置用户ID和租户ID
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        // 查询任务列表
        Page<VideoSynthesisListItemVO> result = videoSynthesisService.getSynthesisTaskList(request, userId, tenantId, listType);
        return RespBody.ok(result);
    }

    @GetMapping("/script/export/{taskId}")
    @Operation(summary = "导出视频合成脚本")
    public void exportSynthesisScript(@PathVariable Integer taskId, HttpServletResponse response) throws IOException {
        videoSynthesisService.exportSynthesisScript(taskId, response);
    }

    @PutMapping("/detail/update")
    @Operation(summary = "更新视频合成任务详情数据")
    public RespBody<Boolean> updateSynthesisTaskDetail(@RequestBody VideoSynthesisDetailUpdateRequest request) {
        // 设置用户ID和租户ID
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        // 更新任务详情数据
        boolean result = videoSynthesisService.updateSynthesisTaskDetail(request, userId, tenantId);
        return RespBody.ok(result);
    }

}
