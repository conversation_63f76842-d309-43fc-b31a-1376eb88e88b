package cn.mlamp.insightflow.cms.enums;

import lombok.Getter;

/**
 * 任务流状态枚举
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Getter
public enum TaskFlowStatusEnum {

    /**
     * 进行中
     */
    RUNNING(1, "进行中"),

    /**
     * 成功
     */
    SUCCESS(2, "成功"),

    /**
     * 失败
     */
    FAILED(3, "失败");

    private final Integer code;
    private final String msg;

    TaskFlowStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static TaskFlowStatusEnum getByCode(Integer code) {
        for (TaskFlowStatusEnum statusEnum : TaskFlowStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
