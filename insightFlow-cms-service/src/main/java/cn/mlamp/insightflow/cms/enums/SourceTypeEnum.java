package cn.mlamp.insightflow.cms.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;

import java.util.Objects;

/**
 * 素材来源类型
 *
 * @Author: husuper
 * @CreateTime: 2025-03-30
 */
@Getter
public enum SourceTypeEnum implements IEnum<Integer> {

    /**
     * 素材来源
     * 当前主要用于约束 {@link cn.mlamp.insightflow.cms.entity.CmsPullTaskDedupedData}
     * ! ES 已废弃
     */
    ES(1, "es"),
    UPLOAD(2, "上传"),
    LINK(3, "链接");

    private final int code;

    private final String msg;

    SourceTypeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @JsonCreator
    public static SourceTypeEnum from(Integer value) {
        if (value == null) {
            return null;
        }
        for (SourceTypeEnum type : SourceTypeEnum.values()) {
            if (Objects.equals(type.getValue(), value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown SourceTypeEnum value: " + value);
    }

    @Override
    public Integer getValue() {
        return code;
    }
}