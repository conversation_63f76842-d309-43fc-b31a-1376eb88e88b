{{- if and .Values.pgcli.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
{{ include "dify.ud.annotations" . | indent 4 }}
    descriptions: pgcli
  labels:
{{- include "dify.labels" . | nindent 4 }}
    component: pgcli
    # app: {{ template "dify.pgcli.fullname" . }}
{{ include "dify.ud.labels" . | indent 4 }}
  name: {{ template "dify.pgcli.fullname" . }}
spec:
  replicas: {{ .Values.pgcli.replicas }}
  selector:
    matchLabels:
{{- include "dify.selectorLabels" . | nindent 6 }}
      component: pgcli
  template:
    metadata:
      annotations:
{{ include "dify.ud.annotations" . | indent 8 }}
      labels:
{{- include "dify.selectorLabels" . | nindent 8 }}
        component: pgcli
        {{/*
        # Required labels for istio
        # app: {{ template "dify.pgcli.fullname" . }}
        # version: {{ (print "v" .Values.serviceMesh.version) | quote }}
        */}}
{{ include "dify.ud.labels" . | indent 8 }}
    spec:
      {{- if .Values.image.pgcli.pullSecrets }}
      imagePullSecrets:
      {{- range .Values.image.pgcli.pullSecrets }}
        - name: {{ . }}
      {{- end }}
      {{- end }}
      containers:
      - image: "{{ .Values.image.pgcli.repository }}:{{ .Values.image.pgcli.tag }}"
        imagePullPolicy: "{{ .Values.image.pgcli.pullPolicy }}"
        name: pgcli
        env:
        {{- if .Values.pgcli.extraEnv }}
          {{- toYaml .Values.pgcli.extraEnv | nindent 8 }}
        {{- end }}
        resources:
          {{- toYaml .Values.pgcli.resources | nindent 12 }}
        volumeMounts:
        - name: {{ template "dify.pgcli.fullname" . }}
          mountPath: /root/.config/pgcli/config
          readOnly: true
          subPath: config
      volumes:
      - name: {{ template "dify.pgcli.fullname" . }}
        configMap:
          defaultMode: 420
          name: {{ template "dify.pgcli.fullname" . }}
{{- end }}