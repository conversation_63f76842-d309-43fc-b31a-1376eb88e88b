{{- if $.Values.server.enabled }}
{{- range $rawService := (list "frontend" "internalFrontend" "matching" "history" "worker") }}
{{- $service := include "serviceName" (list $rawService) }}
{{- $serviceValues := index $.Values.server $rawService }}
{{- if or (not (hasKey $serviceValues "enabled")) $serviceValues.enabled }}
{{- if (default $.Values.server.metrics.serviceMonitor.enabled $serviceValues.metrics.serviceMonitor.enabled) }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "temporal.componentname" (list $ $service) }}
  labels:
    {{- include "temporal.resourceLabels" (list $ $service "") | nindent 4 }}
    {{- with (default $.Values.server.metrics.serviceMonitor.additionalLabels $serviceValues.metrics.serviceMonitor.additionalLabels) }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  endpoints:
  - port: metrics
    interval: {{ default $.Values.server.metrics.serviceMonitor.interval $serviceValues.metrics.serviceMonitor.interval }}
    {{- with (default $.Values.server.metrics.serviceMonitor.metricRelabelings $serviceValues.metrics.serviceMonitor.metricRelabelings) }}
    metricRelabelings:
      {{- toYaml . | nindent 4 }}
    {{- end }}
  jobLabel: {{ include "temporal.componentname" (list $ $service) }}
  namespaceSelector:
    matchNames:
      - "{{ $.Release.Namespace }}"
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "temporal.name" $ }}
      app.kubernetes.io/instance: {{ $.Release.Name }}
      app.kubernetes.io/component: {{ $service }}
      app.kubernetes.io/headless: 'true'
---
{{- end }}
{{- end }}
{{- end }}
{{- end }}
