{{- if or $.Values.server.enabled $.Values.schema.createDatabase.enabled $.Values.schema.setup.enabled $.Values.schema.update.enabled }}
  {{- range $store := (list "default" "visibility") }}
  {{- $storeConfig := index $.Values.server.config.persistence $store }}
  {{- $driver := include "temporal.persistence.driver" (list $ $store) -}}
  {{- $driverConfig := index $storeConfig $driver }}
  {{- if eq $driver "elasticsearch" -}}
  {{- $driverConfig = $.Values.elasticsearch -}}
  {{- end -}}
  {{- $secretName := include "temporal.componentname" (list $ (printf "%s-store" $store)) }}
  {{- if and (not $driverConfig.existingSecret) (eq (include "temporal.persistence.secretName" (list $ $store)) $secretName) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ $secretName }}
  labels:
    {{- include "temporal.resourceLabels" (list $ "" "secret") | nindent 4 }}
  {{- with $.Values.server.secretAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
type: Opaque
data:
  {{- if eq $driver "cassandra" }}
  password: {{ $driverConfig.password | b64enc | quote }}
  {{- else if eq $driver "sql" }}
  password: {{ include "temporal.persistence.sql.password" (list $ $store) | b64enc | quote }}
  {{- else if eq $driver "elasticsearch" }}
  password: {{ $driverConfig.password | b64enc | quote }}
  {{- end }}
---
{{- end }}
{{- end }}
{{- end }}
