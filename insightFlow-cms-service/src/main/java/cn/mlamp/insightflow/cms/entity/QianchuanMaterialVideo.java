package cn.mlamp.insightflow.cms.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import cn.mlamp.insightflow.cms.enums.LanguageEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 千川热点视频数据
 *
 * <AUTHOR>
 */
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_qianchuan_material_video")
public class QianchuanMaterialVideo extends BaseEntity implements VideoAnalysisData {

    public static final String CLASS_TYPE = "qianchuan_video";

    @Override
    public String getClassType() {
        return CLASS_TYPE;
    }

    // ========== 基础标识字段 ==========
    @TableId(type = IdType.AUTO)
    private Integer id;
    // 一般存储 es id
    @TableField("video_id")
    private String videoId;
    // 用户 ID
    private Integer userId;
    // 租户 ID
    private Integer tenantId;

    // ========== 内容基础信息 ==========
    private String title;
    private String brand;
    @TableField("brand_spell")
    private String brandSpell;
    @TableField("publish_time")
    private LocalDateTime publishTime;
    private LanguageEnum language;

    // ========== 视频相关信息 ==========
    private String ossid;
    // 封面图
    @TableField("cover_image")
    private String coverImage;
    private Integer duration;
    // 自定义视频时长（单位：秒）
    @TableField("our_duration")
    private Integer ourDuration;
    // 视频语音识别
    private String kwVideoContent;

    // ========== 互动数据统计 ==========
    // 视频播放数
    private Integer playCount;
    // 曝光
    private Integer exposure;
    // 点赞
    private Integer likes;
    // 评论
    private Integer comments;
    // 分享
    private Integer shares;
    // 点击
    private Integer clicks;
    // 收藏
    private Integer collectCount;
    // 完播率
    @TableField("completion_rate")
    private BigDecimal completionRate;
    // 点击率，百分比
    private BigDecimal ctr;

    @TableField("consume_range")
    private String consumeRange;
    @TableField("consume_range_weight")
    private Integer consumeRangeWeight;

    // ========== 用户信息 ==========
    @TableField("author_name")
    private String authorName;
    @TableField("author_avatar")
    private String authorAvatar;

    // ========== 业务分类标签 ==========
    private String industry;
    // 自定义行业分类
    @TableField("our_industry")
    private String ourIndustry;
    @TableField("ranking_type")
    private String rankingType;
    // 素材形式
    @TableField("material_form")
    private String materialForm;

    // ========== 创意分析相关 ==========
    /**
     * 创意分
     * 已废弃
     */
    private Float rating;
    // 产品名称
    private String productName;
    // 卖点
    // TODO: 应该为 selling
    private String cellingPoint;
    // 受众人群
    private String aimingTribe;
    // 是否高光0：没有1：高光
    private Integer highlight;

    // ========== 数据处理状态 ==========
    // 分析状态0:待分析；1：分析中，2：分析成功，3：分析失败
    private Integer analysisStatus;
    // 黑名单
    private Integer blackMark;
    // 排序
    private Integer sort;
    // 更新时间
    private LocalDateTime updated;
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;

    @JsonIgnore
    @Override
    public String getContent() {
        return null;
    }
}
