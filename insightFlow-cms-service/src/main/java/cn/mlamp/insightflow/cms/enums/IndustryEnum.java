package cn.mlamp.insightflow.cms.enums;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-30
 */
public enum IndustryEnum {

    BEAUTY("beauty", "美妆"),
    THREE_C("3c", "3c家电"),
    HEALTH("health", "医疗健康"),
    AUTOMOBILE("automobile", "汽车"),
    FOOD("food", "食品饮料"),
    COMMON("common", "通用");


    private final String code;

    private final String description;

    IndustryEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    //增加一个根据Code查询出枚举对象的方法
    public static IndustryEnum getByCode(String code) {
        for (IndustryEnum industryEnum : IndustryEnum.values()) {
            if (industryEnum.getCode().equals(code)) {
                return industryEnum;
            }
        }
        return COMMON;
    }
}
