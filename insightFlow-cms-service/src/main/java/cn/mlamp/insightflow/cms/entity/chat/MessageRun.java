package cn.mlamp.insightflow.cms.entity.chat;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.type.JdbcType;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;

import cn.mlamp.insightflow.cms.enums.chat.MessageRunStatus;
import cn.mlamp.insightflow.cms.enums.chat.MessageRunType;
import cn.mlamp.insightflow.cms.graph.entities.EnvInfo;
import cn.mlamp.insightflow.cms.model.vo.chat.ResourceVO;
import lombok.Data;

@TableName(value = "message_run", autoResultMap = true)
@Data
public class MessageRun {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "message_run_id")
    private String messageRunId;

    @TableField(value = "conversation_id")
    private Integer conversationId;

    @TableField(value = "message_id")
    private Integer messageId;

    @TableField(value = "type")
    private MessageRunType type;

    @TableField(value = "env", typeHandler = JacksonTypeHandler.class)
    private EnvInfo env;

    @TableField(value = "name")
    private String name;

    @TableField(value = "label")
    private String label;

    @TableField(value = "input")
    private String input;

    @TableField(value = "output")
    private String output;

    // JSON字符串
    @TableField(value = "resources", typeHandler = JacksonTypeHandler.class)
    private List<ResourceVO> resources;

    @TableField(value = "error")
    private String error;

    @TableField(value = "status")
    private MessageRunStatus status;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "update_time")
    private Date updateTime;

}
