package cn.mlamp.insightflow.cms.config;

import cn.mlamp.insightflow.cms.config.properties.WechatPayProperties;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.util.IOUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.IOException;
import java.io.InputStream;

@Configuration
@RequiredArgsConstructor
public class WechatPayConfig {

    @Bean(name ="wechatCertificateConfig")
    public Config createConfig(WechatPayProperties wechatPayProperties) throws IOException {
        return new RSAAutoCertificateConfig.Builder()
                .merchantId(wechatPayProperties.getMerchantId())
                .privateKey(this.getPrivateKeyStr(wechatPayProperties.getPrivateKeyPath()))
                .merchantSerialNumber(wechatPayProperties.getMerchantSerialNumber())
                .apiV3Key(wechatPayProperties.getApiV3Key())
                .build();
    }

    String getPrivateKeyStr(String keyPath) throws IOException {
        Resource resource = new ClassPathResource(keyPath);
        try (InputStream in = resource.getInputStream()) {
            return IOUtil.toString(in);
        }
    }
}