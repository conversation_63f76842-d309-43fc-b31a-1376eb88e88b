package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("cms_tenant_member_right_statistic")
public class CmsTenantMemberRightStatistic {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer tenantId;

    private Integer videoAnalysis;
    private Integer script;
    // 素材库专有存储空间使用量
    private Long damCapacity;
    private Integer damAiMark;
    private Long videoDuration;
    private Integer videoBgm;
    private Integer tts;
    private LocalDateTime lastMemberExpireTime;

    // 数据逻辑删除标记 0:未删除 1:已删除
    private Integer isDeleted;

    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime updateTime;
}