{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "temporal.serviceAccountName" $ }}
  labels:
    {{- include "temporal.resourceLabels" (list $ "" "") | nindent 4 }}
  annotations:
    helm.sh/hook: pre-install, pre-upgrade
    helm.sh/hook-weight: "-10"
    {{- with .Values.serviceAccount.extraAnnotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
{{- end -}}
