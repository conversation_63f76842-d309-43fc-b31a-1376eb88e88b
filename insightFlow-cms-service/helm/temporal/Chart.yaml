apiVersion: v2
appVersion: 1.27.2
dependencies:
- condition: cassandra.enabled
  name: cassandra
  repository: https://charts.helm.sh/incubator
  version: 0.14.3
- condition: prometheus.enabled
  name: prometheus
  repository: https://prometheus-community.github.io/helm-charts
  version: 25.22.0
- condition: elasticsearch.enabled
  name: elasticsearch
  repository: https://helm.elastic.co
  version: 7.17.3
- condition: grafana.enabled
  name: grafana
  repository: https://grafana.github.io/helm-charts
  version: 8.0.2
description: Temporal is a distributed, scalable, durable, and highly available orchestration
  engine to execute asynchronous long-running business logic in a scalable and resilient
  way.
home: https://temporal.io/
keywords:
- temporal
- workflow
- orchestration
maintainers:
- name: temporalio
  url: https://temporal.io/
name: temporal
sources:
- https://github.com/temporalio/temporal
type: application
version: 0.63.0
