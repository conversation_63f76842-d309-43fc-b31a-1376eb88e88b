{{- if and .Values.imageRenderer.enabled .Values.imageRenderer.networkPolicy.limitIngress }}
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ include "grafana.fullname" . }}-image-renderer-ingress
  namespace: {{ include "grafana.namespace" . }}
  annotations:
    comment: Limit image-renderer ingress traffic from grafana
spec:
  podSelector:
    matchLabels:
      {{- include "grafana.imageRenderer.selectorLabels" . | nindent 6 }}
      {{- with .Values.imageRenderer.podLabels }}
      {{- toYaml . | nindent 6 }}
      {{- end }}

  policyTypes:
    - Ingress
  ingress:
    - ports:
        - port: {{ .Values.imageRenderer.service.targetPort }}
          protocol: TCP
      from:
        - namespaceSelector:
            matchLabels:
              kubernetes.io/metadata.name: {{ include "grafana.namespace" . }}
          podSelector:
            matchLabels:
              {{- include "grafana.selectorLabels" . | nindent 14 }}
              {{- with .Values.podLabels }}
              {{- toYaml . | nindent 14 }}
              {{- end }}
        {{- with .Values.imageRenderer.networkPolicy.extraIngressSelectors -}}
        {{ toYaml . | nindent 8 }}
        {{- end }}
{{- end }}

{{- if and .Values.imageRenderer.enabled .Values.imageRenderer.networkPolicy.limitEgress }}
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ include "grafana.fullname" . }}-image-renderer-egress
  namespace: {{ include "grafana.namespace" . }}
  annotations:
    comment: Limit image-renderer egress traffic to grafana
spec:
  podSelector:
    matchLabels:
      {{- include "grafana.imageRenderer.selectorLabels" . | nindent 6 }}
      {{- with .Values.imageRenderer.podLabels }}
      {{- toYaml . | nindent 6 }}
      {{- end }}

  policyTypes:
    - Egress
  egress:
    # allow dns resolution
    - ports:
        - port: 53
          protocol: UDP
        - port: 53
          protocol: TCP
    # talk only to grafana
    - ports:
        - port: {{ .Values.service.targetPort }}
          protocol: TCP
      to:
        - namespaceSelector:
            matchLabels:
              name: {{ include "grafana.namespace" . }}
          podSelector:
            matchLabels:
              {{- include "grafana.selectorLabels" . | nindent 14 }}
              {{- with .Values.podLabels }}
              {{- toYaml . | nindent 14 }}
              {{- end }}
{{- end }}
