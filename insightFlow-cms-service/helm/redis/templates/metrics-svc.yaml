{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.metrics.enabled .Values.metrics.service.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-metrics" (include "common.names.fullname" .) }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: metrics
  {{- if or .Values.metrics.service.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.metrics.service.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.metrics.service.type }}
  {{- if and .Values.metrics.service.clusterIP (eq .Values.metrics.service.type "ClusterIP") }}
  clusterIP: {{ .Values.metrics.service.clusterIP }}
  {{- end }}
  {{- if eq .Values.metrics.service.type "LoadBalancer" }}
  externalTrafficPolicy: {{ .Values.metrics.service.externalTrafficPolicy }}
  {{- end }}
  {{- if and (eq .Values.metrics.service.type "LoadBalancer") .Values.metrics.service.loadBalancerIP }}
  loadBalancerIP: {{ .Values.metrics.service.loadBalancerIP }}
  {{- end }}
  {{- if and (eq .Values.metrics.service.type "LoadBalancer")  .Values.metrics.service.loadBalancerClass }}
  loadBalancerClass: {{ .Values.metrics.service.loadBalancerClass }}
  {{- end }}
  {{- if and (eq .Values.metrics.service.type "LoadBalancer") .Values.metrics.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges: {{- toYaml .Values.metrics.service.loadBalancerSourceRanges | nindent 4 }}
  {{- end }}
  ports:
    - name: http-metrics
      port: {{ coalesce .Values.metrics.service.ports.http .Values.metrics.service.port }}
      protocol: TCP
      targetPort: metrics
    {{- if .Values.metrics.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.metrics.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
{{- end }}
