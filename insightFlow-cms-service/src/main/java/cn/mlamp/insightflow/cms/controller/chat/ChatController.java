package cn.mlamp.insightflow.cms.controller.chat;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.enums.ErrorCode;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.model.dto.chat.ChatMessageDTO;
import cn.mlamp.insightflow.cms.model.vo.chat.ConversationVO;
import cn.mlamp.insightflow.cms.model.vo.chat.InfiniteScrollResult;
import cn.mlamp.insightflow.cms.model.vo.chat.MessageVO;
import cn.mlamp.insightflow.cms.service.chat.IChatService;

import java.util.Optional;

@RequestMapping("/chat")
@RestController
public class ChatController {

    @Autowired(required = false)
    private Optional<IChatService> chatServiceOptional;

    private IChatService getChatService() {
        if (chatServiceOptional.isPresent()) {
            return chatServiceOptional.get();
        } else {
            throw new BusinessException(ErrorCode.PARAM_ERROR, "Chat service is not available");
        }
    }

    @PostMapping(value = "/message", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter chat(@RequestBody ChatMessageDTO messageDTO) {
        final Integer tenantId = UserContext.getTenantId();
        final Integer userId = UserContext.getUserId();
        return getChatService().chat(tenantId, userId, messageDTO);
    }

    @GetMapping("/conversations")
    public RespBody<InfiniteScrollResult<ConversationVO>> listConversations(
            @RequestParam(required = false) String lastId,
            @RequestParam(required = false, defaultValue = "10") Integer limit
    ) {
        final Integer tenantId = UserContext.getTenantId();
        final Integer userId = UserContext.getUserId();
        return RespBody.ok(getChatService().listConversations(tenantId, userId, lastId, limit));
    }

    @GetMapping("/messages")
    public RespBody<InfiniteScrollResult<MessageVO>> listMessages(
            @RequestParam String conversationId,
            @RequestParam(required = false) String lastId,
            @RequestParam(required = false) Integer limit
    ) {
        final Integer tenantId = UserContext.getTenantId();
        final Integer userId = UserContext.getUserId();
        return RespBody.ok(getChatService().listMessages(tenantId, userId, conversationId, lastId, limit));
    }

}
