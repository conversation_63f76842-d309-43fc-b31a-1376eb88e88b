package cn.mlamp.insightflow.cms.entity.dam;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import cn.mlamp.insightflow.cms.enums.dam.DamTagTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * DAM标签表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Getter
@Setter
@TableName("cms_tag")
@Schema(name = "CmsTag", description = "DAM标签表")
public class DamTag implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "租户ID")
    @TableField("tenant_id")
    private Integer tenantId;

    @Schema(description = "创建者ID")
    @TableField("user_id")
    private Integer userId;

    @Schema(description = "标签名称")
    @TableField("name")
    private String name;

    @Schema(description = "标签类型：1-公共标签, 2-自定义标签")
    @TableField("type")
    private DamTagTypeEnum type;

    @Schema(description = "描述")
    @TableField("description")
    private String description;

    @Schema(description = "示例")
    @TableField("example")
    private String example;

    @Schema(description = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @Schema(description = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @Schema(description = "逻辑删除：0-未删除，1-已删除")
    @TableField("is_deleted")
    private Boolean isDeleted;


    public static DamTag fromPublicTag(DamPublicTag publicTag) {
        DamTag tag = new DamTag();

        tag.setName(publicTag.getName());
        tag.setType(DamTagTypeEnum.PUBLIC);
        tag.setDescription(publicTag.getDescription());
        tag.setExample(publicTag.getExample());
        return tag;
    }

}
