package cn.mlamp.insightflow.cms.graph.tools;

import com.fasterxml.jackson.databind.ObjectMapper;
import dev.langchain4j.agent.tool.Tool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 黄金3秒视频工具集
 * 按照Langgraph4j最佳实践实现的工具调用
 */
@Slf4j
@Component
public class ThreeSecondsTools {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Tool("搜索黄金3秒视频内容，根据行业和标签筛选")
    public String searchThreeSecondsVideos(
            String industry,
            String tag,
            String sortField,
            String sortOrder,
            int current,
            int pageSize
    ) {
        log.info("搜索黄金3秒视频: industry={}, tag={}, sortField={}, sortOrder={}, current={}, pageSize={}",
                industry, tag, sortField, sortOrder, current, pageSize);

        try {
            Map<String, Object> searchResult = performThreeSecondsSearch(industry, tag, sortField, sortOrder, current, pageSize);

            return objectMapper.writeValueAsString(Map.of(
                    "success", true,
                    "message", "黄金3秒视频搜索完成",
                    "data", searchResult
            ));

        } catch (Exception e) {
            log.error("搜索黄金3秒视频失败", e);
            return createErrorResponse("搜索失败: " + e.getMessage());
        }
    }

    @Tool("分析黄金3秒视频的开头技巧和吸引力要素")
    public String analyzeThreeSecondsPattern(String videoId) {
        log.info("分析黄金3秒模式: videoId={}", videoId);

        try {
            Map<String, Object> analysisResult = performPatternAnalysis(videoId);

            return objectMapper.writeValueAsString(Map.of(
                    "success", true,
                    "message", "黄金3秒模式分析完成",
                    "data", analysisResult
            ));

        } catch (Exception e) {
            log.error("分析黄金3秒模式失败", e);
            return createErrorResponse("分析失败: " + e.getMessage());
        }
    }

    @Tool("获取黄金3秒视频的开头文案和技巧")
    public String getThreeSecondsScript(String videoId) {
        log.info("获取黄金3秒脚本: videoId={}", videoId);

        try {
            Map<String, Object> scriptInfo = getVideoScript(videoId);

            return objectMapper.writeValueAsString(Map.of(
                    "success", true,
                    "message", "黄金3秒脚本获取完成",
                    "data", scriptInfo
            ));

        } catch (Exception e) {
            log.error("获取黄金3秒脚本失败", e);
            return createErrorResponse("获取脚本失败: " + e.getMessage());
        }
    }

    @Tool("根据行业推荐黄金3秒视频模板")
    public String recommendThreeSecondsTemplates(String industry, int count) {
        log.info("推荐黄金3秒模板: industry={}, count={}", industry, count);

        try {
            List<Map<String, Object>> templates = getIndustryTemplates(industry, count);

            return objectMapper.writeValueAsString(Map.of(
                    "success", true,
                    "message", "黄金3秒模板推荐完成",
                    "data", Map.of(
                            "templates", templates,
                            "industry", industry,
                            "count", templates.size()
                    )
            ));

        } catch (Exception e) {
            log.error("推荐黄金3秒模板失败", e);
            return createErrorResponse("推荐失败: " + e.getMessage());
        }
    }

    @Tool("分析黄金3秒视频的热门标签和趋势")
    public String analyzeThreeSecondsTrends(String timeRange) {
        log.info("分析黄金3秒趋势: timeRange={}", timeRange);

        try {
            Map<String, Object> trendAnalysis = performTrendAnalysis(timeRange);

            return objectMapper.writeValueAsString(Map.of(
                    "success", true,
                    "message", "黄金3秒趋势分析完成",
                    "data", trendAnalysis
            ));

        } catch (Exception e) {
            log.error("分析黄金3秒趋势失败", e);
            return createErrorResponse("趋势分析失败: " + e.getMessage());
        }
    }

    @Tool("生成黄金3秒视频开头建议")
    public String generateThreeSecondsAdvice(String industry, String targetAudience, String productType) {
        log.info("生成黄金3秒建议: industry={}, targetAudience={}, productType={}",
                industry, targetAudience, productType);

        try {
            Map<String, Object> advice = generateOpeningAdvice(industry, targetAudience, productType);

            return objectMapper.writeValueAsString(Map.of(
                    "success", true,
                    "message", "黄金3秒建议生成完成",
                    "data", advice
            ));

        } catch (Exception e) {
            log.error("生成黄金3秒建议失败", e);
            return createErrorResponse("生成建议失败: " + e.getMessage());
        }
    }

    // 私有方法实现具体业务逻辑

    private Map<String, Object> performThreeSecondsSearch(String industry, String tag, String sortField,
                                                          String sortOrder, int current, int pageSize) {
        // 模拟搜索逻辑
        return Map.of(
                "videos", List.of(
                        Map.of(
                                "id", "ts001",
                                "title", "美妆产品黄金3秒开头",
                                "industry", "美妆",
                                "tags", List.of("吸睛", "产品展示"),
                                "openingText", "你还在用这种方法化妆吗？",
                                "viewRate", 0.85,
                                "engagementRate", 0.12
                        ),
                        Map.of(
                                "id", "ts002",
                                "title", "科技产品黄金3秒",
                                "industry", "科技",
                                "tags", List.of("悬念", "问题引入"),
                                "openingText", "这个功能99%的人都不知道",
                                "viewRate", 0.78,
                                "engagementRate", 0.15
                        )
                ),
                "total", 50,
                "current", current,
                "pageSize", pageSize,
                "searchCriteria", Map.of(
                        "industry", industry,
                        "tag", tag,
                        "sortField", sortField,
                        "sortOrder", sortOrder
                )
        );
    }

    private Map<String, Object> performPatternAnalysis(String videoId) {
        // 模拟模式分析
        return Map.of(
                "videoId", videoId,
                "patterns", List.of(
                        Map.of(
                                "type", "问题引入",
                                "description", "通过提出问题吸引观众注意力",
                                "effectiveness", 0.85,
                                "examples", List.of("你知道吗？", "还在这样做？")
                        ),
                        Map.of(
                                "type", "数据震撼",
                                "description", "使用惊人数据或统计信息",
                                "effectiveness", 0.78,
                                "examples", List.of("99%的人不知道", "每天有1000万人")
                        )
                ),
                "keyElements", List.of("视觉冲击", "音效配合", "文字提示", "表情管理"),
                "successRate", 0.82
        );
    }

    private Map<String, Object> getVideoScript(String videoId) {
        // 模拟获取脚本
        return Map.of(
                "videoId", videoId,
                "openingScript", Map.of(
                        "text", "你还在为选择困难症烦恼吗？今天教你一个方法",
                        "duration", 3.0,
                        "tone", "疑问+解决",
                        "visualCues", List.of("产品特写", "表情夸张", "手势指向")
                ),
                "techniques", List.of(
                        "反问句开头",
                        "制造悬念",
                        "承诺价值"
                ),
                "industryTags", List.of("生活技巧", "产品推荐")
        );
    }

    private List<Map<String, Object>> getIndustryTemplates(String industry, int count) {
        // 模拟行业模板
        return List.of(
                Map.of(
                        "id", "template001",
                        "name", "问题引入模板",
                        "industry", industry,
                        "pattern", "你是否遇到过{问题}？",
                        "example", "你是否遇到过化妆总是不服帖的问题？",
                        "successRate", 0.85
                ),
                Map.of(
                        "id", "template002",
                        "name", "数据冲击模板",
                        "industry", industry,
                        "pattern", "{数字}%的人都不知道{秘密}",
                        "example", "90%的人都不知道这个护肤秘密",
                        "successRate", 0.78
                ),
                Map.of(
                        "id", "template003",
                        "name", "对比反差模板",
                        "industry", industry,
                        "pattern", "别人{状态A}，而我{状态B}",
                        "example", "别人化妆要1小时，而我只需要5分钟",
                        "successRate", 0.72
                )
        );
    }

    private Map<String, Object> performTrendAnalysis(String timeRange) {
        // 模拟趋势分析
        return Map.of(
                "timeRange", timeRange,
                "hotPatterns", List.of(
                        Map.of("pattern", "问题引入", "growth", "+25%"),
                        Map.of("pattern", "数据震撼", "growth", "+18%"),
                        Map.of("pattern", "情感共鸣", "growth", "+15%")
                ),
                "emergingTrends", List.of("AI话题", "环保理念", "健康生活"),
                "industryInsights", Map.of(
                        "美妆", "个性化需求增长",
                        "科技", "AI应用普及",
                        "生活", "便民服务受欢迎"
                ),
                "analysisTime", System.currentTimeMillis()
        );
    }

    private Map<String, Object> generateOpeningAdvice(String industry, String targetAudience, String productType) {
        // 模拟生成建议
        return Map.of(
                "industry", industry,
                "targetAudience", targetAudience,
                "productType", productType,
                "recommendations", List.of(
                        Map.of(
                                "approach", "问题导向",
                                "script", "你是否也有这样的困扰？",
                                "reason", "直击痛点，引起共鸣",
                                "suitability", 0.9
                        ),
                        Map.of(
                                "approach", "价值承诺",
                                "script", "3秒钟教你一个改变生活的技巧",
                                "reason", "明确价值，激发兴趣",
                                "suitability", 0.85
                        )
                ),
                "tips", List.of(
                        "语速要快，信息密度高",
                        "视觉元素要有冲击力",
                        "音效配合增强效果",
                        "表情和肢体语言要夸张"
                )
        );
    }

    private String createErrorResponse(String errorMessage) {
        try {
            return objectMapper.writeValueAsString(Map.of(
                    "success", false,
                    "message", errorMessage,
                    "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            return "{\"success\":false,\"message\":\"" + errorMessage + "\"}";
        }
    }
} 