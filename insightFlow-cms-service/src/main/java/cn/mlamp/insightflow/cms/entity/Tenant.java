package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 租户管理
 */
@Data
@TableName("cms_tenant")
public class Tenant extends BaseEntity implements Serializable {

    /**
     * TCC的租户Id
     */
    private Integer tenantId;

    /**
     * TCC的租户名称
     */
    private String name;

    /**
     * 用户统计
     */
    private Integer statistics;


}