podAnnotations:
  fluentd.active: "true"
  fluentd.index: insight-flow-cms-log-preview
  fluentd.regexp: '^(?<count>[0-9]*): (?<logtime>.*$)'
  fluentd.timeformat: '%Y_%m_%d %H:%M:%S'

api:
  replicaCount: 1
  resources:
    requests:
      cpu: 16
      memory: 32G
    limits:
      cpu: 32
      memory: 64G
  config:
    logLevel: debug
  volumes: []
  volumeMounts: []
  extraEnv: []

worker:
  replicaCount: 3
  resources:
    requests:
      cpu: 30
      memory: 40G
    limits:
      cpu: 60
      memory: 80G
  config:
    logLevel: debug
    processorsCount: 2
  volumes: []
  volumeMounts: []
  extraEnv: []
