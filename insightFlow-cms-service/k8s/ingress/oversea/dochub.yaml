apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dochub-ingress
  namespace: dochub
  annotations:
    kubernetes.io/ingress.internetChargeType: TRAFFIC_POSTPAID_BY_HOUR
    kubernetes.io/ingress.internetMaxBandwidthOut: "100"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  ingressClassName: qcloud
  tls:
    - hosts:
        - llm-ops-meowoo.mlamp.cn
      secretName: mlamp-cn-oknob5t4
  rules:
    - host: llm-ops-meowoo.mlamp.cn
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: dochub
                port:
                  number: 80


