package cn.mlamp.insightflow.cms.config;

import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.TimeUnit;

@Configuration
public class HttpConfig {

    @Primary
    @Bean
    public RestTemplate customRestTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        // 设置连接超时时间（单位：毫秒）
        factory.setConnectTimeout(180 * 1000);
        // 设置读取超时时间（单位：毫秒）
        factory.setReadTimeout(180 * 1000);
        return new RestTemplate(factory);
    }

    @Bean
    public OkHttpClient okHttpClient() {
        return new OkHttpClient.Builder().retryOnConnectionFailure(true)
                .connectionPool(new ConnectionPool(200, 59, TimeUnit.SECONDS)).connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(300, TimeUnit.SECONDS) // 5分钟读取超时，适合视频检测
                .writeTimeout(30, TimeUnit.SECONDS).hostnameVerifier((s, sslSession) -> true).build();
    }

}
