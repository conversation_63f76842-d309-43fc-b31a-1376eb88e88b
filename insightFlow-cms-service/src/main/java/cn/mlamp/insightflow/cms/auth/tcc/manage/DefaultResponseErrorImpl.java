package cn.mlamp.insightflow.cms.auth.tcc.manage;//package cn.mlamp.insightflow.cms.auth.tcc.manage;

import com.mz.ttc.response.ResponseErrorService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.PrintWriter;

/**
 * @Author: husuper
 * @CreateTime: 2025-01-16
 */
@Service
@Lazy
@Primary
public class DefaultResponseErrorImpl implements ResponseErrorService {
    private static final Logger log = LoggerFactory.getLogger(com.mz.ttc.response.DefaultResponseErrorImpl.class);
    private static final String MESSAGE = "{\"statusCode\": 401, \"message\": \"认证失败\"}";

    public DefaultResponseErrorImpl() {
    }

    public void responseError(HttpServletRequest request, HttpServletResponse response) {
        response.setContentType("application/json;charset=UTF-8");
        response.setContentLength("{\"statusCode\": 401, \"message\": \"认证失败\"}".getBytes().length);
        response.setStatus(401);

        try {
            log.info("responseError，登录失败,返回登录信息为:{}", "{\"statusCode\":401, \"message\": \"认证失败\"}");
            PrintWriter writer = response.getWriter();
            writer.write("{\"statusCode\": 401, \"message\": \"认证失败\"}");
        } catch (IOException var4) {
            log.error("responseError:回写401错误信息失败:{}", "{\"statusCode\": 401, \"message\": \"认证失败\"}", var4);
        }
    }
}

