apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "insight-flow-cms.worker.customer.fullname" . }}
  labels:
    {{ include "insight-flow-cms.labels" . | nindent 4 }}
    component: worker-customer
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.worker.customer.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{ include "insight-flow-cms.selectorLabels" . | nindent 6 }}
      component: worker-customer
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{ toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{ include "insight-flow-cms.labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{ toYaml . | nindent 8 }}
        {{- end }}
        component: worker-customer
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{ toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{ toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        {{ include "insight-flow-cms.s3fs-sidercar" . | nindent 8 }}
        - name: worker
          securityContext:
            {{ toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            {{ include "insight-flow-cms.worker.env" . | nindent 12 }}
          {{- if .Values.worker.customer.extraEnv }}
          {{ toYaml .Values.worker.customer.extraEnv | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          resources:
            {{ toYaml .Values.worker.customer.resources | nindent 12 }}
          volumeMounts:
            {{ include "insight-flow-cms.worker.volumeMounts" . | nindent 12 }}
          {{- with .Values.worker.customer.volumeMounts }}
            {{ toYaml . | nindent 12 }}
          {{- end }}
      volumes:
        - name: config
          configMap:
            name: {{ include "insight-flow-cms.worker.customer.fullname" . }}-config
            items:
              - key: application-{{ .Release.Name }}.yml
                path: application-{{ .Release.Name }}.yml
        {{ include "insight-flow-cms.worker.volumes" . | nindent 8 }}
      {{- with .Values.worker.customer.volumes }}
        {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.worker.customer.nodeSelector }}
      nodeSelector:
        {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.worker.customer.affinity }}
      affinity:
        {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.worker.customer.tolerations }}
      tolerations:
        {{ toYaml . | nindent 8 }}
      {{- end }}
