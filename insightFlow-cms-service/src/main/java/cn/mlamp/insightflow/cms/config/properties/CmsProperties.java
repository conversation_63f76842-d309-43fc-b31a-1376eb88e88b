package cn.mlamp.insightflow.cms.config.properties;

import cn.mlamp.insightflow.cms.enums.LanguageEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.*;

@Slf4j
@Data
@Configuration
@ConfigurationProperties("cms")
public class CmsProperties {

    @Value("${spring.profiles.active:prod}")
    private String profile = "prod";

    /**
     * 海外模式
     */
    private boolean overseaMode = false;

    /**
     * 是否启用认证中心
     */
    private boolean enableAuthCenter = true;

    private EnumSet<LanguageEnum> supportedLanguages;

    public boolean isProd() {
        return "prod".equalsIgnoreCase(profile);
    }

    public boolean isSupportedLanguage(LanguageEnum language) {
        return supportedLanguages.contains(language);
    }

    public void setSupportedLanguages(List<String> languages) {
        if (CollectionUtils.isEmpty(languages)) {
            supportedLanguages = EnumSet.noneOf(LanguageEnum.class);
        }
        supportedLanguages = EnumSet.copyOf(
                languages.stream().map(LanguageEnum::from).filter(Objects::nonNull).toList()
        );
        log.info("SupportedLanguages:{}", supportedLanguages);
    }

}
