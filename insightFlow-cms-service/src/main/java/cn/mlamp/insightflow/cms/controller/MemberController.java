package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.model.query.RightUsageListRequest;
import cn.mlamp.insightflow.cms.model.vo.MemberDiscountVO;
import cn.mlamp.insightflow.cms.model.vo.MemberListVO;
import cn.mlamp.insightflow.cms.model.vo.MemberProfileVO;
import cn.mlamp.insightflow.cms.model.vo.MemberRightAlertVO;
import cn.mlamp.insightflow.cms.model.vo.MemberRightUsageVO;
import cn.mlamp.insightflow.cms.service.MemberService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/members")
public class MemberController {
    private final MemberService memberService;

    /**
     * 试用会员领取提醒
     *
     * @return
     */
    @GetMapping("/trial-reminder")
    public RespBody<Boolean> getTrialReminder() {
        boolean result = memberService.getTrialReminder();
        return RespBody.ok(result);
    }

    /**
     * 领取试用会员
     *
     * @return
     */
    @PostMapping("/trial")
    public RespBody<Void> receiveTrialMember() {
        memberService.receiveTrialMember();
        return RespBody.ok();
    }

    /**
     * 会员权益低于阈值提示
     *
     * @return
     */
    @GetMapping("/right-alert")
    public RespBody<MemberRightAlertVO> alertRight() {
        MemberRightAlertVO vo = memberService.alertRight();
        return RespBody.ok(vo);
    }

    /**
     * 账户的会员信息概况
     *
     * @return
     */
    @GetMapping("/profile")
    public RespBody<MemberProfileVO> getMemberProfile() {
        MemberProfileVO vo = memberService.getMemberProfile();
        return RespBody.ok(vo);
    }

    /**
     * 获取会员信息列表
     *
     * @param duration
     * @return
     */
    @GetMapping("/list")
    public RespBody<IPage<MemberListVO>> listMembers(@RequestParam int duration) {
        IPage<MemberListVO> result = memberService.listMembers(duration);
        return RespBody.ok(result);
    }

    /**
     * 会员价格折扣信息
     *
     * @return
     */
    @GetMapping("/discount/list")
    public RespBody<IPage<MemberDiscountVO>> listDiscount() {
        IPage<MemberDiscountVO> result = memberService.listDiscount();
        return RespBody.ok(result);

    }

    /**
     * 会员权益使用记录列表
     *
     * @param request
     * @return
     */
    @PostMapping("/right-usage/list")
    public RespBody<IPage<MemberRightUsageVO>> listRightUsage(@RequestBody RightUsageListRequest request) {
        IPage<MemberRightUsageVO> result = memberService.listRightUsage(request);
        return RespBody.ok(result);
    }
}