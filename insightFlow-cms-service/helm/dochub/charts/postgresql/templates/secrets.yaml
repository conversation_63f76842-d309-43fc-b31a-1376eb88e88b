{{- $host := include "postgresql.primary.fullname" . }}
{{- $port := include "postgresql.service.port" . }}
{{- $postgresPassword := "" }}
{{- if .Values.auth.enablePostgresUser }}
{{- $postgresPassword = include "common.secrets.passwords.manage" (dict "secret" (include "postgresql.secretName" .) "key" $.Values.auth.secretKeys.adminPasswordKey "providedValues" (list "global.postgresql.auth.postgresPassword" "auth.postgresPassword") "context" $) | trimAll "\"" | b64dec }}
{{- end }}
{{- $replicationPassword := "" }}
{{- if eq .Values.architecture "replication" }}
{{- $replicationPassword = include "common.secrets.passwords.manage" (dict "secret" (include "postgresql.secretName" .) "key" $.Values.auth.secretKeys.replicationPasswordKey "providedValues" (list "auth.replicationPassword") "context" $) | trimAll "\"" | b64dec }}
{{- end }}
{{- $ldapPassword := "" }}
{{- if and .Values.ldap.enabled (or .Values.ldap.bind_password .Values.ldap.bindpw) }}
{{- $ldapPassword = coalesce .Values.ldap.bind_password .Values.ldap.bindpw }}
{{- end }}
{{- $customUser := include "postgresql.username" . }}
{{- $password := "" }}
{{- if not (empty (include "postgresql.username" .)) }}
{{- $password = include "common.secrets.passwords.manage" (dict "secret" (include "postgresql.secretName" .) "key" $.Values.auth.secretKeys.userPasswordKey "providedValues" (list "global.postgresql.auth.password" "auth.password") "context" $) | trimAll "\"" | b64dec }}
{{- end }}
{{- $database := include "postgresql.database" . }}
{{- if (include "postgresql.createSecret" .) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.names.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: Opaque
data:
  {{- if .Values.auth.enablePostgresUser }}
  postgres-password: {{ $postgresPassword | b64enc | quote }}
  {{- end }}
  {{- if not (empty (include "postgresql.username" .)) }}
  password: {{ $password | b64enc | quote }}
  {{- end }}
  {{- if eq .Values.architecture "replication" }}
  replication-password: {{ $replicationPassword | b64enc | quote }}
  {{- end }}
  # We don't auto-generate LDAP password when it's not provided as we do for other passwords
  {{- if and .Values.ldap.enabled (or .Values.ldap.bind_password .Values.ldap.bindpw) }}
  ldap-password: {{ $ldapPassword  | b64enc | quote }}
  {{- end }}
{{- end }}
{{- if .Values.serviceBindings.enabled }}
{{- if .Values.auth.enablePostgresUser }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.names.fullname" . }}-svcbind-postgres
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: servicebinding.io/postgresql
data:
  provider: {{ print "bitnami" | b64enc | quote }}
  type: {{ print "postgresql" | b64enc | quote }}
  host: {{ $host | b64enc | quote }}
  port: {{ $port | b64enc | quote }}
  username: {{ print "postgres" | b64enc | quote }}
  database: {{ print "postgres" | b64enc | quote }}
  password: {{ $postgresPassword | b64enc | quote }}
  uri: {{ printf "postgresql://postgres:%s@%s:%s/postgres" $postgresPassword $host $port | b64enc | quote }}
{{- end }}
{{- if and (not (empty $customUser)) (ne $customUser "postgres") }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.names.fullname" . }}-svcbind-custom-user
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: servicebinding.io/postgresql
data:
  provider: {{ print "bitnami" | b64enc | quote }}
  type: {{ print "postgresql" | b64enc | quote }}
  host: {{ $host | b64enc | quote }}
  port: {{ $port | b64enc | quote }}
  username: {{ $customUser | b64enc | quote }}
  password: {{ $password | b64enc | quote }}
  {{- if $database }}
  database: {{ $database | b64enc | quote }}
  {{- end }}
  uri: {{ printf "postgresql://%s:%s@%s:%s/%s" $customUser $password $host $port $database | b64enc | quote }}
{{- end }}
{{- end }}
