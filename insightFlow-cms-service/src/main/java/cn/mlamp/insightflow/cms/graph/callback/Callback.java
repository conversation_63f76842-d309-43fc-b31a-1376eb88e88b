package cn.mlamp.insightflow.cms.graph.callback;

import cn.mlamp.insightflow.cms.graph.entities.EnvInfo;
import cn.mlamp.insightflow.cms.graph.events.action.ActionType;
import cn.mlamp.insightflow.cms.model.vo.chat.ResourceVO;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.service.Result;

import java.io.Closeable;
import java.util.List;
import java.util.Map;

public interface Callback extends Closeable {

    void onWorkflowStarted(String query, EnvInfo env);

    void onWorkflowFinished(String answer);

    void onWorkflowError(Throwable t);

    void onMessage(String runId, String message);

    void onMessageEnd(String runId, String content);

    void onNodeStarted(String runId, String nodeName, String goal);

    void onNodeFinished(String runId, String nodeName, String response);

    void onNodeError(String runId, String nodeName, Throwable t);

    void onToolStarted(String runId, String toolName, String label, String input);

    void onToolFinished(String runId, String toolName, String label, String input, String output, ResourceVO... resources);

    void onToolError(String runId, String toolName, String label, String input, Throwable t);

    void onAction(String runId, ActionType actionType, Object parameters);

    default void onChatModelStarted(String runId, String modelName, List<ChatMessage> messages) {

    }

    default void onChatModelFinished(String runId, String modelName, List<ChatMessage> messages, Result<?> result) {

    }

    default void onChatModelError(String runId, String modelName, List<ChatMessage> messages, Throwable t) {
    }

    default void close() {
        // nothing
    }

}
