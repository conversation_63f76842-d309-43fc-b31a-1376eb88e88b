package cn.mlamp.insightflow.cms.constant;

import cn.hutool.core.date.DatePattern;

import java.time.format.DateTimeFormatter;

public class Constants {

    /**
     * 删除备份的查询批次大小
     */
    public static final int SEARCH_SIZE = 1000;

    public static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(DATE_TIME_PATTERN);

    public static final DateTimeFormatter DATE_COMPACT_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    public static final DateTimeFormatter CHINESE_DATE_TIME_FORMATTER = DateTimeFormatter
            .ofPattern(DatePattern.CHINESE_DATE_TIME_PATTERN);

    public static final String MIN_TIME_SUFFIX = " 00:00:00";

    // 用户权益提醒有效期，单位小时
    public static final int MEMBER_RIGHT_ALERT_TIMEOUT_HOURS = 24;

    // 试用会员名称
    public static final String TRIAL_MEMBER_NAME = "试用版";

    // 租户没有会员，展示的会员名称
    public static final String NO_MEMBER_NAME = "未开通会员";

    public static final DateTimeFormatter RFC3339_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss+08:00");

    public static final String WECHAT_PAY_SERVICE_BEAN_NAME = "wechatPayServiceImpl";

    // 支付有效时长，单位秒
    public static final int PAY_DURATION = 120;

    // 头图预签名超时
    public static final int PIC_PRESIGNED_EXPIRE_MIN = 15;

    // 视频预签名超时
    public static final int VIDEO_PRESIGNED_EXPIRE_MIN = 60;

    public static final String ASSET_RIGHT_PREFIX = "asset_";

    public static final String RECYCLE_BIN_ASSET_RIGHT_PREFIX = "recycle_bin_asset_";

}