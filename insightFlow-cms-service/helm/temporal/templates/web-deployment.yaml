{{- if .Values.web.enabled -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "temporal.componentname" (list $ "web") }}
  annotations:
    {{- include "temporal.resourceAnnotations" (list $ "web" "deployment") | nindent 4 }}
  labels:
    {{- include "temporal.resourceLabels" (list $ "web" "deployment") | nindent 4 }}
spec:
  replicas: {{ .Values.web.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "temporal.name" $ }}
      app.kubernetes.io/instance: {{ .Release.Name }}
      app.kubernetes.io/component: web
  template:
    metadata:
      annotations:
        {{- include "temporal.resourceAnnotations" (list $ "web" "pod") | nindent 8 }}
      labels:
        {{- include "temporal.resourceLabels" (list $ "web" "pod") | nindent 8 }}
    spec:
      {{- with .Values.web.additionalInitContainers }}
      initContainers:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{ include "temporal.serviceAccount" $ }}
      {{- if .Values.web.additionalVolumes }}
      volumes:
      {{- toYaml .Values.web.additionalVolumes | nindent 8 }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}-web
          image: "{{ .Values.web.image.repository }}:{{ .Values.web.image.tag }}"
          imagePullPolicy: {{ .Values.web.image.pullPolicy }}
          env:
            - name: TEMPORAL_ADDRESS
              value: "{{ include "temporal.fullname" $ }}-frontend.{{ .Release.Namespace }}.svc:{{ .Values.server.frontend.service.port }}"
          {{- if .Values.web.additionalEnv }}
          {{- toYaml .Values.web.additionalEnv | nindent 12 }}
          {{- end }}
          {{- if .Values.web.additionalEnvSecretName }}
          envFrom:
            - secretRef:
                name: {{ .Values.web.additionalEnvSecretName }}
          {{- end }}
          livenessProbe:
            initialDelaySeconds: 10
            tcpSocket:
              port: http
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            {{- toYaml .Values.web.resources | nindent 12 }}
          {{- with .Values.web.additionalVolumeMounts }}
          volumeMounts:
            {{- toYaml . | nindent 12 }}
          {{- end }}
      {{- with $.Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.web.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.web.affinity }}
      affinity:
      {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.web.tolerations }}
      tolerations:
      {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.web.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- toYaml . | nindent 8 }}
    {{- end }}
  {{- end }}
