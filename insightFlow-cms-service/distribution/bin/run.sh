#!/bin/bash

set -e

MODE=${MODE:-api}
# 检查MODE环境变量
if [ -z "$MODE" ]; then
    echo "Error: MODE environment variable is required (api or worker)"
    echo "Usage: MODE=api ./run.sh [additional_java_args...]"
    echo "       MODE=worker ./run.sh [additional_java_args...]"
    exit 1
fi

# 检测JAVA_HOME
if [ -z "$JAVA_HOME" ]; then
    JAVA_CMD=java
else
    JAVA_CMD="$JAVA_HOME/bin/java"
fi

# 应用目录
APP_HOME="$(cd "$(dirname "$0")/.." && pwd)"
CONF_DIR="$APP_HOME/conf"
EXTRA_CONF_DIR="$APP_HOME/conf.d"
LIB_DIR="$APP_HOME/lib"
BOOT_DIR="$APP_HOME/boot"
LOG_DIR="$APP_HOME/logs"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 统一的JVM参数（适用于所有模式）
if [ -f /.dockerenv ]; then
    # Docker环境下，使用容器内存百分比
    DEFAULT_JVM_OPTS="-XX:MaxRAMPercentage=75.0 -XX:InitialRAMPercentage=30.0"
else
    # 非Docker环境下，使用固定内存大小（可根据实际情况调整）
    DEFAULT_JVM_OPTS="-Xmx2g -Xms1g"
fi

# 根据MODE设置主类
case "${MODE}" in
    "api")
        MAIN_CLASS="cn.mlamp.insightflow.cms.CmsApplication"
        ;;
    "worker")
        MAIN_CLASS="cn.mlamp.insightflow.cms.CmsWorker"
        ;;
    *)
        echo "Error: Invalid MODE '$MODE'. Must be 'api' or 'worker'"
        exit 1
        ;;
esac

export LD_LIBRARY_PATH=/lib/x86_64-linux-gnu:$LD_LIBRARY_PATH
export PATH=/usr/bin:$PATH

# JVM参数：默认参数 + 环境变量JVM_OPS
JVM_OPTS="$DEFAULT_JVM_OPTS $JVM_OPS"

# 应用参数：基础配置
DEFAULT_APP_OPTS="--spring.config.location=file:$CONF_DIR/,file:$EXTRA_CONF_DIR/"

# 根据APP_ENV环境变量设置Spring profiles
if [ -n "$APP_ENV" ]; then
    DEFAULT_APP_OPTS="$DEFAULT_APP_OPTS --spring.profiles.active=$APP_ENV"
fi

# 应用参数：默认参数 + 环境变量APP_OPTS
APP_OPTS="$DEFAULT_APP_OPTS $APP_OPTS"

# 类路径
CLASSPATH="$CONF_DIR:$LIB_DIR/*:$BOOT_DIR/*"

# 启动命令（同步运行，支持追加参数$@）
echo "Starting CMS $MODE mode with class: $MAIN_CLASS"
exec $JAVA_CMD $JVM_OPTS -cp "$CLASSPATH" $MAIN_CLASS $APP_OPTS "$@"