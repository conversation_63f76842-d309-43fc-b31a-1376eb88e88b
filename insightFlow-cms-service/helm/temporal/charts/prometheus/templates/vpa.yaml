{{- if .Values.server.verticalAutoscaler.enabled -}}
{{- if .Capabilities.APIVersions.Has "autoscaling.k8s.io/v1/VerticalPodAutoscaler" }}
apiVersion: autoscaling.k8s.io/v1
{{- else }}
apiVersion: autoscaling.k8s.io/v1beta2
{{- end }}
kind: VerticalPodAutoscaler
metadata:
  name: {{ template "prometheus.server.fullname" . }}-vpa
  namespace: {{ include "prometheus.namespace" . }}
  labels:
    {{- include "prometheus.server.labels" . | nindent 4 }}
spec:
  targetRef:
    apiVersion: "apps/v1"
{{- if .Values.server.statefulSet.enabled }}
    kind: StatefulSet
{{- else }}
    kind: Deployment
{{- end }}
    name: {{ template "prometheus.server.fullname" . }}
  updatePolicy:
    updateMode: {{ .Values.server.verticalAutoscaler.updateMode | default "Off" | quote }}
  resourcePolicy:
    containerPolicies: {{ .Values.server.verticalAutoscaler.containerPolicies | default list | toYaml | trim | nindent 4 }}
{{- end -}}
