package cn.mlamp.insightflow.cms.events;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 视频轮询事件
 */
@Getter
public class VideoPollingEvent extends ApplicationEvent {

    /**
     * 任务ID
     */
    private final String taskId;

    /**
     * 用户ID
     */
    private final Integer userId;

    /**
     * 租户ID
     */
    private final Integer tenantId;

    /**
     * 任务类型：volcano（火山引擎）、jimeng（即梦AI）
     */
    private final String taskType;

    /**
     * 创建时间戳
     */
    private final Long createTime;

    public VideoPollingEvent(Object source, String taskId, Integer userId, Integer tenantId, String taskType) {
        super(source);
        this.taskId = taskId;
        this.userId = userId;
        this.tenantId = tenantId;
        this.taskType = taskType;
        this.createTime = System.currentTimeMillis();
    }

    /**
     * 创建火山引擎轮询事件
     */
    public static VideoPollingEvent createVolcanoEvent(Object source, String taskId, Integer userId, Integer tenantId) {
        return new VideoPollingEvent(source, taskId, userId, tenantId, "volcano");
    }

    /**
     * 创建即梦AI轮询事件
     */
    public static VideoPollingEvent createJimengEvent(Object source, String taskId, Integer userId, Integer tenantId) {
        return new VideoPollingEvent(source, taskId, userId, tenantId, "jimeng");
    }

    /**
     * 创建Vidu轮询事件
     */
    public static VideoPollingEvent createViduEvent(Object source, String taskId, Integer userId, Integer tenantId) {
        return new VideoPollingEvent(source, taskId, userId, tenantId, "vidu");
    }

    /**
     * 创建数字人视频轮询事件
     */
    public static VideoPollingEvent createDigitalHumanEvent(Object source, String taskId, Integer userId,
            Integer tenantId) {
        return new VideoPollingEvent(source, taskId, userId, tenantId, "digital_human");
    }

    /**
     * 创建妙绘视频轮询事件
     */
    public static VideoPollingEvent createDescriptiveVideoEvent(Object source, String taskId, Integer userId,
            Integer tenantId) {
        return new VideoPollingEvent(source, taskId, userId, tenantId, "descriptive_video");
    }

    /**
     * 创建文生视频轮询事件
     */
    public static VideoPollingEvent createTextToVideoEvent(Object source, String taskId, Integer userId,
            Integer tenantId) {
        return new VideoPollingEvent(source, taskId, userId, tenantId, "text_to_video");
    }
}
