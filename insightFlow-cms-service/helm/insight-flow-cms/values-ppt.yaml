podAnnotations:
  fluentd.active: "true"
  fluentd.index: insight-flow-cms-log-test
  fluentd.regexp: '^(?<count>[0-9]*): (?<logtime>.*$)'
  fluentd.timeformat: '%Y_%m_%d %H:%M:%S'

api:
  replicaCount: 1
  resources:
    requests:
      cpu: 4
      memory: 8G
    limits:
      cpu: 8
      memory: 16G
  config:
    logLevel: debug
  volumes:
    - name: cms-local-pvc-test
      persistentVolumeClaim:
        claimName: cms-local-pvc-test
    - name: log
      emptyDir: { }
  volumeMounts:
    - name: cms-local-pvc-test
      mountPath: /local/
    - name: log
      mountPath: /app/logs/
  extraEnv:
    - name: START_CMS_WORKER
      value: "false"
    - name: SPRING_CONFIG_ADDITIONAL_LOCATION
      value: file:/app/config/
    - name: download_path
      value: /local/downloads-ppt/

worker:
  replicaCount: 1
  resources:
    requests:
      cpu: 4
      memory: 8G
    limits:
      cpu: 8
      memory: 16G
  config:
    logLevel: debug
    processorsCount: 2
  volumes:
    - name: cms-local-pvc-test
      persistentVolumeClaim:
        claimName: cms-local-pvc-test
    - name: log
      emptyDir: { }
  volumeMounts:
    - name: cms-local-pvc-test
      mountPath: /local/
    - name: log
      mountPath: /app/logs/
  extraEnv:
    - name: START_CMS_WORKER
      value: "true"
    - name: SPRING_CONFIG_ADDITIONAL_LOCATION
      value: file:/app/config/
    - name: download_path
      value: /local/downloads-ppt/
