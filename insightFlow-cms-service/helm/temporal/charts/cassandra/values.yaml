## Cassandra image version
## ref: https://hub.docker.com/r/library/cassandra/
image:
  repo: cassandra
  tag: 3.11.5
  pullPolicy: IfNotPresent
  ## Specify ImagePullSecrets for Pods
  ## ref: https://kubernetes.io/docs/concepts/containers/images/#specifying-imagepullsecrets-on-a-pod
  # pullSecrets: myregistrykey

## Specify a service type
## ref: http://kubernetes.io/docs/user-guide/services/
service:
  type: ClusterIP
  annotations: ""

## Use an alternate scheduler, e.g. "stork".
## ref: https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
##
# schedulerName:

## Persist data to a persistent volume
persistence:
  enabled: true
  ## cassandra data Persistent Volume Storage Class
  ## If defined, storageClassName: <storageClass>
  ## If set to "-", storageClassName: "", which disables dynamic provisioning
  ## If undefined (the default) or set to null, no storageClassName spec is
  ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
  ##   GKE, AWS & OpenStack)
  ##
  # storageClass: "-"
  accessMode: ReadWriteOnce
  size: 10Gi

## Configure resource requests and limits
## ref: http://kubernetes.io/docs/user-guide/compute-resources/
## Minimum memory for development is 4GB and 2 CPU cores
## Minimum memory for production is 8GB and 4 CPU cores
## ref: http://docs.datastax.com/en/archived/cassandra/2.0/cassandra/architecture/architecturePlanningHardware_c.html
resources: {}
  # requests:
  #   memory: 4Gi
  #   cpu: 2
  # limits:
  #   memory: 4Gi
  #   cpu: 2

## Change cassandra configuration parameters below:
## ref: http://docs.datastax.com/en/cassandra/3.0/cassandra/configuration/configCassandra_yaml.html
## Recommended max heap size is 1/2 of system memory
## Recommended heap new size is 1/4 of max heap size
## ref: http://docs.datastax.com/en/cassandra/3.0/cassandra/operations/opsTuneJVM.html
config:
  cluster_domain: cluster.local
  cluster_name: cassandra
  cluster_size: 3
  seed_size: 2
  num_tokens: 256
  # If you want Cassandra to use this datacenter and rack name,
  # you need to set endpoint_snitch to GossipingPropertyFileSnitch.
  # Otherwise, these values are ignored and datacenter1 and rack1
  # are used.
  dc_name: DC1
  rack_name: RAC1
  endpoint_snitch: SimpleSnitch
  max_heap_size: 2048M
  heap_new_size: 512M
  start_rpc: false
  ports:
    cql: 9042
    thrift: 9160
    # If a JVM Agent is in place
    # agent: 61621

## Cassandra config files overrides
configOverrides: {}

## Cassandra docker command overrides
commandOverrides: []

## Cassandra docker args overrides
argsOverrides: []

## Custom env variables.
## ref: https://hub.docker.com/_/cassandra/
env: {}

## Liveness and Readiness probe values.
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/
livenessProbe:
  initialDelaySeconds: 90
  periodSeconds: 30
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 3
readinessProbe:
  initialDelaySeconds: 90
  periodSeconds: 30
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 3
  address: "${POD_IP}"

## Configure node selector. Edit code below for adding selector to pods
## ref: https://kubernetes.io/docs/user-guide/node-selection/
# selector:
  # nodeSelector:
    # cloud.google.com/gke-nodepool: pool-db

## Additional pod annotations
## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
podAnnotations: {}

## Additional pod labels
## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
podLabels: {}

## Additional pod-level settings
podSettings:
  # Change this to give pods more time to properly leave the cluster when not using persistent storage.
  terminationGracePeriodSeconds: 30

## Pod distruption budget
podDisruptionBudget: {}
  # maxUnavailable: 1
  # minAvailable: 2

podManagementPolicy: OrderedReady
updateStrategy:
  type: OnDelete

## Pod Security Context
securityContext:
  enabled: false
  fsGroup: 999
  runAsUser: 999

## Affinity for pod assignment
## Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
affinity: {}

## Node tolerations for pod assignment
## Ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
tolerations: []

rbac:
  # Specifies whether RBAC resources should be created
  create: false

serviceAccount:
  # Specifies whether a ServiceAccount should be created
  create: true
  # The name of the ServiceAccount to use.
  # If not set and create is true, a name is generated using the fullname template
  # name:

# Use host network for Cassandra pods
# You must pass seed list into config.seeds property if set to true
hostNetwork: false

## Backup cronjob configuration
## Ref: https://github.com/maorfr/cain
backup:
  enabled: false

  # Schedule to run jobs. Must be in cron time format
  # Ref: https://crontab.guru/
  schedule:
  - keyspace: keyspace1
    cron: "0 7 * * *"
  - keyspace: keyspace2
    cron: "30 7 * * *"

  annotations:
    # Example for authorization to AWS S3 using kube2iam
    # Can also be done using environment variables
    iam.amazonaws.com/role: cain

  image:
    repository: maorfr/cain
    tag: 0.6.0

  # Additional arguments for cain
  # Ref: https://github.com/maorfr/cain#usage
  extraArgs: []

  # Add additional environment variables
  env:
  # Example environment variable required for AWS credentials chain
  - name: AWS_REGION
    value: us-east-1

  resources:
    requests:
      memory: 1Gi
      cpu: 1
    limits:
      memory: 1Gi
      cpu: 1

  # Name of the secret containing the credentials of the service account used by GOOGLE_APPLICATION_CREDENTIALS, as a credentials.json file
  # google:
    # serviceAccountSecret:

  # Destination to store the backup artifacts
  # Supported cloud storage services: AWS S3, Minio S3, Azure Blob Storage, Google Cloud Storage
  # Additional support can added. Visit this repository for details
  # Ref: https://github.com/maorfr/skbn
  destination: s3://bucket/cassandra

## Cassandra exported configuration
## ref: https://github.com/criteo/cassandra_exporter
exporter:
  # If exporter is enabled this will create a ServiceMonitor by default as well
  servicemonitor: true
  enabled: false
  additionalLabels: {}
    # prometheus: default
  image:
    repo: criteord/cassandra_exporter
    tag: 2.0.2
  port: 5556
  jvmOpts: ""
  resources: {}
    # limits:
    #   cpu: 1
    #   memory: 1Gi
    # requests:
    #   cpu: 1
    #   memory: 1Gi
