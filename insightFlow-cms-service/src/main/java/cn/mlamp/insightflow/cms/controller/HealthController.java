package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 健康检查接口
 *
 * <AUTHOR> liuyuan
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "健康检查")
public class HealthController {

    @GetMapping("/health")
    @Operation(summary = "健康检查")
    public RespBody<?> health() {
        return RespBody.ok();
    }

}

