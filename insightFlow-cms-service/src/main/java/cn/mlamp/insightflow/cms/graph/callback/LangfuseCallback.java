package cn.mlamp.insightflow.cms.graph.callback;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import cn.mlamp.insightflow.cms.graph.entities.EnvInfo;
import cn.mlamp.insightflow.cms.graph.events.action.ActionType;
import cn.mlamp.insightflow.cms.graph.helper.ChatMessageHelper;
import cn.mlamp.insightflow.cms.graph.helper.LangfuseHelper;
import cn.mlamp.insightflow.cms.model.vo.chat.ResourceVO;
import com.langfuse.client.LangfuseClient;
import com.langfuse.client.resources.ingestion.IngestionClient;
import com.langfuse.client.resources.ingestion.requests.IngestionRequest;
import com.langfuse.client.resources.ingestion.types.*;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.service.Result;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nullable;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static cn.mlamp.insightflow.cms.graph.helper.LangfuseHelper.getCurrentDateTime;
import static cn.mlamp.insightflow.cms.graph.helper.LangfuseHelper.toOffsetDateTime;

@Slf4j
public class LangfuseCallback implements Callback {

    private static final String COMPLETION_STATUS = "完成";

    private static final String FAILED_STATUS = "失败";

    private final static ExecutorService EXECUTOR_SERVICE = Executors.newSingleThreadExecutor();

    private final LangfuseClient client;

    private final String traceId;

    private final String rootSpanId;

    private final String sessionId;

    private final String messageId;

    private final Integer userId;
    private final IngestionRequest.Builder requestBuilder;
    private String query;
    private EnvInfo env;
    private String currentNodeRunId;

    private String currentChatModelRunId;

    private long workflowStartTime;

    private long nodeStartTime;

    private long toolStartTime;

    private long chatStartTime;

    public LangfuseCallback(
            Integer userId,
            String conversationId,
            String messageId,
            LangfuseClient client
    ) {
        this.userId = userId;
        this.sessionId = conversationId;
        this.messageId = messageId;
        this.client = client;
        this.traceId = UUID.fastUUID().toString();
        this.rootSpanId = UUID.fastUUID().toString();
        this.requestBuilder = IngestionRequest.builder();
    }

    @Override
    public void onWorkflowStarted(String query, EnvInfo env) {
        this.workflowStartTime = System.currentTimeMillis();
        this.query = query;
        this.env = env;
    }

    @Override
    public void onWorkflowFinished(String answer) {
        this.requestBuilder.addBatch(IngestionEvent.spanCreate(buildRootSpan(answer)));
        this.requestBuilder.addBatch(IngestionEvent.traceCreate(buildTrace(answer)));
    }

    @Override
    public void onWorkflowError(Throwable t) {
        this.requestBuilder.addBatch(IngestionEvent.spanCreate(buildRootSpan(t)));
        this.requestBuilder.addBatch(IngestionEvent.traceCreate(buildTrace(t)));
    }

    @Override
    public void onChatModelStarted(String runId, String modelName, List<ChatMessage> messages) {
        this.chatStartTime = System.currentTimeMillis();
        this.currentChatModelRunId = runId;
    }

    @Override
    public void onChatModelFinished(String runId, String modelName, List<ChatMessage> messages, Result<?> result) {
        final CreateGenerationEvent event = CreateGenerationEvent.builder()
                .id(runId)
                .timestamp(getCurrentDateTime())
                .body(
                        CreateGenerationBody.builder()
                                .id(runId)
                                .traceId(traceId)
                                .parentObservationId(currentNodeRunId)
                                .name("ChatOpenAI")
                                .model(modelName)
                                .input(ChatMessageHelper.toMessageData(messages))
                                .output(result.content())
                                .completionStartTime(toOffsetDateTime(chatStartTime))
                                .startTime(toOffsetDateTime(chatStartTime))
                                .endTime(OffsetDateTime.now())
                                .usage(LangfuseHelper.toIngestionUsage(result.tokenUsage()))
                                .statusMessage(COMPLETION_STATUS)
                                .build()
                )
                .build();
        this.requestBuilder.addBatch(IngestionEvent.generationCreate(event));
        this.currentChatModelRunId = null;
    }

    @Override
    public void onChatModelError(String runId, String modelName, List<ChatMessage> messages, Throwable t) {
        final CreateGenerationEvent event = CreateGenerationEvent.builder()
                .id(runId)
                .timestamp(getCurrentDateTime())
                .body(
                        CreateGenerationBody.builder()
                                .id(runId)
                                .traceId(traceId)
                                .parentObservationId(currentNodeRunId)
                                .name("ChatOpenAI")
                                .model(modelName)
                                .input(ChatMessageHelper.toMessageData(messages))
                                .completionStartTime(toOffsetDateTime(chatStartTime))
                                .startTime(toOffsetDateTime(chatStartTime))
                                .endTime(OffsetDateTime.now())
                                .statusMessage(FAILED_STATUS)
                                .build()
                )
                .build();
        this.requestBuilder.addBatch(IngestionEvent.generationCreate(event));
        this.currentChatModelRunId = null;
    }

    @Override
    public void onMessage(String runId, String message) {
    }

    @Override
    public void onMessageEnd(String runId, String content) {
    }

    @Override
    public void onNodeStarted(String runId, String nodeName, String goal) {
        nodeStartTime = System.currentTimeMillis();
        this.currentNodeRunId = runId;
    }

    @Override
    public void onNodeFinished(String runId, String nodeName, String response) {
        this.currentNodeRunId = null;
        final CreateSpanEvent event = CreateSpanEvent.builder()
                .id(runId)
                .timestamp(getCurrentDateTime())
                .body(CreateSpanBody.builder()
                        .id(runId)
                        .traceId(traceId)
                        .parentObservationId(rootSpanId)
                        .name(nodeName)
                        .output(response)
                        .startTime(toOffsetDateTime(nodeStartTime))
                        .endTime(OffsetDateTime.now())
                        .statusMessage(COMPLETION_STATUS)
                        .build()
                )
                .build();
        this.requestBuilder.addBatch(IngestionEvent.spanCreate(event));
        this.currentNodeRunId = null;
    }

    @Override
    public void onNodeError(String runId, String nodeName, Throwable t) {
        final CreateSpanEvent event = CreateSpanEvent.builder()
                .id(runId)
                .timestamp(getCurrentDateTime())
                .body(CreateSpanBody.builder()
                        .id(runId)
                        .traceId(traceId)
                        .parentObservationId(rootSpanId)
                        .name(nodeName)
                        .output(t.getMessage())
                        .startTime(toOffsetDateTime(nodeStartTime))
                        .endTime(OffsetDateTime.now())
                        .statusMessage(FAILED_STATUS)
                        .build()
                )
                .build();
        this.requestBuilder.addBatch(IngestionEvent.spanCreate(event));
        this.currentNodeRunId = null;
    }

    @Override
    public void onToolStarted(String runId, String toolName, String label, String input) {
        this.toolStartTime = System.currentTimeMillis();
    }

    @Override
    public void onToolFinished(String runId, String toolName, String label, String input, String output, ResourceVO... resources) {
        final CreateSpanEvent event = CreateSpanEvent.builder()
                .id(runId)
                .timestamp(getCurrentDateTime())
                .body(
                        CreateSpanBody.builder()
                                .id(runId)
                                .traceId(traceId)
                                .parentObservationId(currentChatModelRunId)
                                .name(label)
                                .input(input)
                                .output(output)
                                .startTime(toOffsetDateTime(toolStartTime))
                                .endTime(OffsetDateTime.now())
                                .statusMessage(COMPLETION_STATUS)
                                .build()
                )
                .build();
        this.requestBuilder.addBatch(IngestionEvent.spanCreate(event));
    }

    @Override
    public void onToolError(String runId, String toolName, String label, String input, Throwable t) {
        final CreateSpanEvent event = CreateSpanEvent.builder()
                .id(runId)
                .timestamp(getCurrentDateTime())
                .body(
                        CreateSpanBody.builder()
                                .id(runId)
                                .traceId(traceId)
                                .parentObservationId(currentChatModelRunId)
                                .name(label)
                                .input(input)
                                .output(t.getMessage())
                                .startTime(toOffsetDateTime(toolStartTime))
                                .endTime(OffsetDateTime.now())
                                .statusMessage(FAILED_STATUS)
                                .build()
                )
                .build();
        this.requestBuilder.addBatch(IngestionEvent.spanCreate(event));
    }

    @Override
    public void onAction(String runId, ActionType actionType, Object parameters) {
        final CreateSpanEvent event = CreateSpanEvent.builder()
                .id(runId)
                .timestamp(getCurrentDateTime())
                .body(
                        CreateSpanBody.builder()
                                .id(runId)
                                .traceId(traceId)
                                .parentObservationId(currentNodeRunId)
                                .name(actionType.getValue())
                                .input(parameters)
                                .output(Map.of())
                                .startTime(OffsetDateTime.now())
                                .endTime(OffsetDateTime.now())
                                .build()
                )
                .build();
        this.requestBuilder.addBatch(IngestionEvent.spanCreate(event));
    }

    @Override
    public void close() {
        publishTrace();
    }

    public CreateSpanEvent buildRootSpan(String answer) {
        return buildRootSpan(answer, null);
    }

    public CreateSpanEvent buildRootSpan(Throwable t) {
        return buildRootSpan(null, t);
    }

    public CreateSpanEvent buildRootSpan(@Nullable String answer, @Nullable Throwable t) {
        final CreateSpanBody.Builder rootSpanBodyBuilder = CreateSpanBody.builder()
                .id(rootSpanId)
                .traceId(traceId)
                .name("CMS Agent Workflow")
                .startTime(toOffsetDateTime(workflowStartTime))
                .endTime(OffsetDateTime.now())
                .metadata(Map.of(
                        "conversationId", sessionId,
                        "type", "workflow"
                ))
                .input(Map.of(
                        "query", query,
                        "env", env
                ))
                .output(answer)
                .statusMessage(t != null ? LangfuseHelper.COMPLETION_FAILED_STATUS : LangfuseHelper.COMPLETION_COMPLETED_STATUS);

        final CreateSpanBody createSpanBody = rootSpanBodyBuilder.build();

        return CreateSpanEvent.builder()
                .id(rootSpanId)
                .timestamp(getCurrentDateTime())
                .body(createSpanBody)
                .build();
    }

    public TraceEvent buildTrace(String answer) {
        return buildTrace(answer, null);
    }

    public TraceEvent buildTrace(Throwable t) {
        return buildTrace(null, t);
    }

    private TraceEvent buildTrace(@Nullable String answer, @Nullable Throwable t) {
        long endTime = System.currentTimeMillis();
        long duration = endTime - workflowStartTime;

        final TraceBody.Builder traceBodyBuilder = TraceBody.builder()
                .id(traceId)
                .name("CMS Agent Workflow")
                .sessionId(sessionId)
                .userId(String.valueOf(userId))
                .timestamp(OffsetDateTime.now())
                .metadata(Map.of(
                        "conversationId", sessionId,
                        "startTime", workflowStartTime,
                        "endTime", endTime,
                        "duration", duration,
                        "type", "workflow",
                        "status", t != null ? "failed" : "completed"
                ))
                .input(Map.of(
                        "query", query,
                        "env", env
                ))
                .output(answer);

        final TraceBody traceBody = traceBodyBuilder.build();

        return TraceEvent.builder()
                .id(traceId)
                .timestamp(getCurrentDateTime())
                .body(traceBody)
                .build();
    }

    private void publishTrace() {
        // 异步发送所有监控数据
        CompletableFuture.runAsync(
                () -> {
                    try {
                        final IngestionClient ingestionClient = client.ingestion();
                        final IngestionResponse response = ingestionClient.batch(this.requestBuilder.build());
                        if (CollectionUtil.isNotEmpty(response.getErrors())) {
                            log.warn("Langfuse: 发送监控数据时发生错误: {}", response.getErrors());
                        }
                    } catch (Exception e) {
                        log.warn("Langfuse: 发送监控数据失败: {}", e.getMessage(), e);
                    }
                },
                EXECUTOR_SERVICE
        );
    }

}
