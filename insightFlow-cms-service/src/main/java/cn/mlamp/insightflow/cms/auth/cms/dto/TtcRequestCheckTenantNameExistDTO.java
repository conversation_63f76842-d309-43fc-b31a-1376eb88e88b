package cn.mlamp.insightflow.cms.auth.cms.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 校验租户名是否存在DTO
 *
 * <AUTHOR>
 * @since 2022-09-14 17:00:47
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TtcRequestCheckTenantNameExistDTO extends TtcRequestBaseDTO {

    /**
     * 租户名称
     */
    @NotBlank(message = "override_message.tenant_name_not_blank")
    @Size(max = 20, message = "override_message.tenant_name_out_of_length")
    private String tenantName;

}
