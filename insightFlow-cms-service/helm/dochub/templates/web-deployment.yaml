{{- if and .Values.web.enabled}}
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
{{ include "dify.ud.annotations" . | indent 4 }}
    descriptions: web server
  labels:
{{- include "dify.labels" . | nindent 4 }}
    component: web
    # app: {{ template "dify.web.fullname" . }}
{{ include "dify.ud.labels" . | indent 4 }}
  name: {{ template "dify.web.fullname" . }}
spec:
  replicas: {{ .Values.web.replicas }}
  selector:
    matchLabels:
{{- include "dify.selectorLabels" . | nindent 6 }}
      component: web
      {{/*
      # Required labels for istio
      # app: {{ template "dify.web.fullname" . }}
      # version: {{ (print "v" .Values.serviceMesh.version) | quote }}
      */}}
  template:
    metadata:
      annotations:
{{ include "dify.ud.annotations" . | indent 8 }}
      labels:
{{- include "dify.selectorLabels" . | nindent 8 }}
        component: web
        {{/*
        # Required labels for istio
        # app: {{ template "dify.web.fullname" . }}
        # version: {{ (print "v" .Values.serviceMesh.version) | quote }}
        */}}
{{ include "dify.ud.labels" . | indent 8 }}
    spec:
      {{- if .Values.image.web.pullSecrets }}
      imagePullSecrets:
      {{- range .Values.image.web.pullSecrets }}
        - name: {{ . }}
      {{- end }}
      {{- end }}
      containers:
      - image: "{{ .Values.image.web.repository }}:{{ .Values.image.web.tag }}"
        imagePullPolicy: "{{ .Values.image.web.pullPolicy }}"
        name: web
        env:
        {{- if .Values.web.extraEnv }}
          {{- toYaml .Values.web.extraEnv | nindent 8 }}
        {{- end }}
        ports:
          - name: web
            containerPort: 3000
            protocol: TCP
        readinessProbe:
          tcpSocket:
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 30
        livenessProbe:
          tcpSocket:
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 30
        resources:
          {{- toYaml .Values.web.resources | nindent 12 }}
    {{- if and (.Values.nodeSelector) (not .Values.web.nodeSelector) }}
      nodeSelector:
{{ toYaml .Values.nodeSelector | indent 8 }}
    {{- end }}
    {{- if .Values.web.nodeSelector }}
      nodeSelector:
{{ toYaml .Values.web.nodeSelector | indent 8 }}
    {{- end }}
    {{- if and (.Values.affinity) (not .Values.web.affinity) }}
      affinity:
{{ toYaml .Values.affinity | indent 8 }}
    {{- end }}
    {{- if .Values.web.affinity }}
      affinity:
{{ toYaml .Values.web.affinity | indent 8 }}
    {{- end }}
    {{- if and (.Values.tolerations) (not .Values.web.tolerations) }}
      tolerations:
{{ toYaml .Values.tolerations | indent 8 }}
    {{- end }}
    {{- if .Values.web.tolerations }}
      tolerations:
{{ toYaml .Values.web.tolerations | indent 8 }}
    {{- end }}
{{- end }}