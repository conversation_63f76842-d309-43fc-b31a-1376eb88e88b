package cn.mlamp.insightflow.cms.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: husuper
 * @CreateTime: 2025-07-25
 */
@Configuration
@Getter
public class WebhookConfig {

    @Value("${cms.notify.enabled:true}")
    private Boolean enabled = true;

    @Value("${wechat.webhook.url:https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d9162c60-bdd0-444a-8623-cd424abd1998}")
    private String webhookUrl;


    @Value("${wechat.business.webhook.url:https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d9162c60-bdd0-444a-8623-cd424abd1998}")
    private String businessWebhookUrl;

    @Value("${wechat.business.webhook.memberUrl}")
    private String memberWebhookUrl;
}
