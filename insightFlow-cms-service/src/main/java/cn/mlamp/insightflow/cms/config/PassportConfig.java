package cn.mlamp.insightflow.cms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: husuper
 * @CreateTime: 2025-07-30
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "passport")
public class PassportConfig {


    private String clientId;

    private String clientSecret;

    private String url;


}
