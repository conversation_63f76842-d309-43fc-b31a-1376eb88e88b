{{- if .Values.serviceAccounts.server.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    {{- include "prometheus.server.labels" . | nindent 4 }}
  name: {{ template "prometheus.serviceAccountName.server" . }}
  namespace: {{ include "prometheus.namespace" . }}
  annotations:
{{ toYaml .Values.serviceAccounts.server.annotations | indent 4 }}
{{- if kindIs "bool" .Values.server.automountServiceAccountToken }}
automountServiceAccountToken: {{ .Values.server.automountServiceAccountToken }}
{{- else if kindIs "bool" .Values.serviceAccounts.server.automountServiceAccountToken }}
automountServiceAccountToken: {{ .Values.serviceAccounts.server.automountServiceAccountToken }}
{{- end }}
{{- end }}
