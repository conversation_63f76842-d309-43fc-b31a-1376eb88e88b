{{- if and (eq .Values.rbac.create true) (eq .Values.kubeRBACProxy.enabled true) -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "prometheus-node-exporter.fullname" . }}
  labels:
    {{- include "prometheus-node-exporter.labels" . | nindent 4 }}
rules:
  {{-  if $.Values.kubeRBACProxy.enabled  }}
  - apiGroups: [ "authentication.k8s.io" ]
    resources:
      - tokenreviews
    verbs: [ "create" ]
  - apiGroups: [ "authorization.k8s.io" ]
    resources:
      - subjectaccessreviews
    verbs: [ "create" ]
  {{- end }}
{{- end -}}
