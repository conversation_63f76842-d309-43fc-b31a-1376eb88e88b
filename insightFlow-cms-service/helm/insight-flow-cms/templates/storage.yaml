{{- if .Values.api.persistence.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "insight-flow-cms.api.fullname" . }}-storage
  labels:
    {{ include "insight-flow-cms.labels" . | nindent 4 }}
    component: api-storage
  annotations:
    helm.sh/resource-policy: keep
spec:
  storageClassName: {{ .Values.api.persistence.storageClassName | default "gfs" }}
  accessModes:
      - ReadWriteMany
  resources:
    requests:
      storage: {{ .Values.api.persistence.size | default "10Gi" }}
{{- end }}
---
{{- if .Values.worker.persistence.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "insight-flow-cms.worker.fullname" . }}-storage
  labels:
    {{ include "insight-flow-cms.labels" . | nindent 4 }}
    component: worker-storage
  annotations:
    helm.sh/resource-policy: keep
spec:
  storageClassName: {{ .Values.worker.persistence.storageClassName | default "gfs" }}
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: {{ .Values.worker.persistence.size | default "10Gi" }}
{{- end }}
---