apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: clean-worker-share-storage
  labels:
    app: clean-worker-share-storage
spec:
  schedule: "0/30 * * * *"  # 每1小时执行一次
  concurrencyPolicy: Forbid  # 禁止并发执行
  jobTemplate:
    metadata:
      name: clean-worker-share-storage
      labels:
        app: clean-worker-share-storage
    spec:
      template:
        metadata:
          name: clean-worker-share-storage
          labels:
            app: clean-worker-share-storage
        spec:
          restartPolicy: Never
          containers:
            - name: cleaner
              image: hub.intra.mlamp.cn/public/docker-jdk-mvn-jdk17-amd64
              environment:
                - name: TARGET_DIR
                  value: /storage/downloads
              command:
                - bash
                - -c
                - |
                  echo "开始清理一小时前的文件夹..."
                  
                  # 检查是否支持 -mmin
                  if find $TARGET_DIR -type d -mmin +60 2>/dev/null; then
                    echo "使用 -mmin 参数清理..."
                    find $TARGET_DIR -type d -mmin +60 -exec echo "删除: {}" \;
                    find $TARGET_DIR -type d -mmin +60 -exec rm -rf {} \;
                  else
                    echo "使用shell脚本方式清理..."
                    # 方法2：使用shell脚本判断时间
                    current_time=$(date +%s)
                    one_hour_ago=$((current_time - 3600))

                    for dir in $TARGET_DIR/*/; do
                      if [ -d "$dir" ]; then
                        # 获取文件夹修改时间
                        dir_time=$(stat -c %Y "$dir" 2>/dev/null || echo "0")

                        if [ "$dir_time" -lt "$one_hour_ago" ] && [ "$dir_time" -gt "0" ]; then
                          echo "删除文件夹: $dir"
                          rm -rf "$dir"
                        else
                          echo "保留文件夹: $dir"
                        fi
                      fi
                    done
                  fi

                  echo "清理完成！"
              volumeMounts:
                - mountPath: /storage
                  name: storage
          volumes:
            - name: storage
              persistentVolumeClaim:
                claimName: cms-local-pvc