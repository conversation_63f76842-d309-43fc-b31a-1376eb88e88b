package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.annotation.HasMemberCheck;
import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.common.resp.RespCode;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.model.query.MultipartUploadRequest;
import cn.mlamp.insightflow.cms.service.FileMultipartUploadService;
import cn.mlamp.insightflow.cms.worker.utils.S3Util;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 文件分片上传（断点续传）控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/file/multipart")
@Tag(name = "文件断点续传接口")
@Slf4j
public class FileMultipartUploadController {

    @Resource
    private FileMultipartUploadService fileMultipartUploadService;

    @PostMapping("/create-session")
    @Operation(summary = "创建分片上传会话")
    @HasMemberCheck
    public RespBody<S3Util.MultipartUploadSession> createMultipartUploadSession(@Valid @RequestBody MultipartUploadRequest.CreateSessionRequest request) {
        try {
            S3Util.MultipartUploadSession session = fileMultipartUploadService.createUploadSession(
                    request.getFilename(),
                    request.getType(),
                    request.getFileUrl(),
                    request.getBucket(),
                    request.getContentType()
            );
            return RespBody.ok(session);
        } catch (Exception e) {
            log.error("创建分片上传会话失败", e);
            throw new BusinessException(RespCode.FAIL, "创建分片上传会话失败: " + e.getMessage());
        }
    }

    @PostMapping("/calculate-parts")
    @Operation(summary = "计算文件分片信息")
    @HasMemberCheck
    public RespBody<S3Util.FilePartInfo> calculatePartInfo(
            @Valid @RequestBody MultipartUploadRequest.CalculatePartRequest request) {
        try {
            S3Util.FilePartInfo partInfo = fileMultipartUploadService.calculatePartInfo(
                    request.getFileSize(), request.getPartSize());
            return RespBody.ok(partInfo);
        } catch (Exception e) {
            log.error("计算文件分片信息失败", e);
            throw new BusinessException(RespCode.FAIL, "计算文件分片信息失败: " + e.getMessage());
        }
    }

    @PostMapping("/generate-urls")
    @Operation(summary = "生成分片上传预签名URL列表")
    @HasMemberCheck
    public RespBody<List<S3Util.PresignedUploadUrl>> generateUploadUrls(
            @Valid @RequestBody MultipartUploadRequest.GenerateUrlsRequest request) {
        try {
            List<S3Util.PresignedUploadUrl> urls = fileMultipartUploadService.generateUploadUrls(
                    request.getOssId(),
                    request.getUploadId(),
                    request.getTotalParts(),
                    request.getExpirationMinutes() != null ? request.getExpirationMinutes() : 0);
            return RespBody.ok(urls);
        } catch (Exception e) {
            log.error("生成分片上传预签名URL列表失败", e);
            throw new BusinessException(RespCode.FAIL, "生成分片上传预签名URL列表失败: " + e.getMessage());
        }
    }

    @PostMapping("/generate-url")
    @Operation(summary = "生成单个分片上传预签名URL")
    @HasMemberCheck
    public RespBody<S3Util.PresignedUploadUrl> generateUploadUrl(
            @Valid @RequestBody MultipartUploadRequest.GenerateUrlRequest request) {
        try {
            S3Util.PresignedUploadUrl url = fileMultipartUploadService.generateUploadUrl(
                    request.getOssId(),
                    request.getUploadId(),
                    request.getPartNumber(),
                    request.getExpirationMinutes() != null ? request.getExpirationMinutes() : 0);
            return RespBody.ok(url);
        } catch (Exception e) {
            log.error("生成单个分片上传预签名URL失败", e);
            throw new BusinessException(RespCode.FAIL, "生成单个分片上传预签名URL失败: " + e.getMessage());
        }
    }

    @PostMapping("/complete")
    @Operation(summary = "完成分片上传")
    @HasMemberCheck
    public RespBody<S3Util.CompleteMultipartUploadResult> completeMultipartUpload(
            @Valid @RequestBody MultipartUploadRequest.CompleteUploadRequest request) {
        try {
            // 转换前端传来的分片信息
            List<S3Util.FrontendCompletedPart> completedParts = request.getCompletedParts().stream()
                    .map(part -> S3Util.FrontendCompletedPart.builder()
                            .partNumber(part.getPartNumber())
                            .eTag(part.getETag())
                            .build())
                    .toList();

            S3Util.CompleteMultipartUploadResult result = fileMultipartUploadService.completeUpload(
                    request.getOssId(), request.getUploadId(), completedParts);
            return RespBody.ok(result);
        } catch (Exception e) {
            log.error("完成分片上传失败", e);
            throw new BusinessException(RespCode.FAIL, "完成分片上传失败: " + e.getMessage());
        }
    }

    @PostMapping("/abort")
    @Operation(summary = "终止分片上传")
    @HasMemberCheck
    public RespBody<Void> abortMultipartUpload(
            @Valid @RequestBody MultipartUploadRequest.AbortUploadRequest request) {
        try {
            fileMultipartUploadService.abortUpload(request.getOssId(), request.getUploadId());
            return RespBody.ok();
        } catch (Exception e) {
            log.error("终止分片上传失败", e);
            throw new BusinessException(RespCode.FAIL, "终止分片上传失败: " + e.getMessage());
        }
    }
}
