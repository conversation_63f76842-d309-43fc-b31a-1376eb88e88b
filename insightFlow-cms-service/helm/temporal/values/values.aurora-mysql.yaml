server:
  config:
    persistence:
      default:
        driver: "sql"

        sql:
          driver: "mysql8"
          host: _HOST_
          port: 3306
          database: temporal
          user: _USERNAME_
          password: _PASSWORD_
          maxConns: 20
          maxIdleConns: 20
          maxConnLifetime: "1h"
          connectAttributes:
            tx_isolation: 'READ-COMMITTED'

      visibility:
        driver: "sql"

        sql:
          driver: "mysql8"
          host: _HOST_
          port: 3306
          database: temporal_visibility
          user: _USERNAME_
          password: _PASSWORD_
          maxConns: 20
          maxIdleConns: 20
          maxConnLifetime: "1h"
          connectAttributes:
            tx_isolation: 'READ-COMMITTED'          

cassandra:
  enabled: false

mysql:
  enabled: true

postgresql:
  enabled: false

schema:
  createDatabase:
    enabled: true
  setup:
    enabled: false
  update:
    enabled: false
