package cn.mlamp.insightflow.cms.entity;

import cn.mlamp.insightflow.cms.model.dto.VideoValueLinkVideoInfoDTO;
import cn.mlamp.insightflow.cms.model.dto.VideoValueVideoScoreInfoDTO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_video_value_task")
public class VideoValueTask extends BaseEntity implements Serializable {
    @TableField(value = "tenant_id")
    private Integer tenantId;

    // 创建者id
    private Integer userId;

    private String esId;

    // 任务名称
    private String name;

    /**
     * {@link cn.mlamp.insightflow.cms.enums.VideoValueTaskStatusEnum}
     */
    // 任务状态
    private String status;

    // 分享id
    private Integer shareId;

    // 黄金3秒
    private String gold3s;

    // 视频评分，json字符串
    private VideoValueVideoScoreInfoDTO scoreInfo;

    // 链接视频信息，json字符串
    private VideoValueLinkVideoInfoDTO linkVideoInfo;

    // 视频得分
    private Integer score;

    /**
     * {@link cn.mlamp.insightflow.cms.enums.VideoValueRankEnum}
     */
    // 视频段位
    private String rank;

    // 视频时长，单位秒
    private Long duration;

    // 视频存储oss id
    private String videoOssId;

    // 视频封面图片oss id
    private String imageOssId;

    // 用户上传的视频链接
    private String rawUrl;
    /**
     * 收藏id
     */
    private Integer collectId;
    /**
     * 列表类型
     */
    private Integer listType;
}
