package cn.mlamp.insightflow.cms.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;

/**
 * 深实下载状态
 *
 * <AUTHOR>
 */
@Getter
public enum DeepanaDownloadStatus implements IEnum<String> {
    COMPLETED("已完成"),
    PROCESSING("处理中"),
    ERROR("未找到"),
    ;
    private final String message;

    DeepanaDownloadStatus(String message) {
        this.message = message;
    }

    @JsonCreator
    public static DeepanaDownloadStatus from(String value) {
        return valueOf(value.toUpperCase());
    }

    @Override
    public String getValue() {
        return toString().toLowerCase();
    }
}
