global:
  password: "d5j7RKoPVzckWDDZ"
  imagePullSecrets:
    - ai-pc-ai-pc-robot
  security:
    allowInsecureImages: true

image:
  registry: hub.intra.mlamp.cn
  repository: ai-pc/redis
  pullSecret:
    - ai-pc-ai-pc-robot

master:
  persistence:
    enabled: false
  podSecurityContext:
    enabled: false
  containerSecurityContext:
    enabled: false
  serviceAccount:
    create: false
  pdb:
    create: false
  resources:
    requests:
      cpu: 1
      memory: 2Gi
    limits:
      cpu: 2
      memory: 4Gi

replica:
  persistence:
    enabled: false
  podSecurityContext:
    enabled: false
  containerSecurityContext:
    enabled: false
  resources:
    requests:
      cpu: 1
      memory: 2Gi
    limits:
      cpu: 2
      memory: 4Gi

sentinel:
  enabled: true
  replicas: 3
  image:
    registry: hub.intra.mlamp.cn
    repository: ai-pc/redis-sentinel
    pullSecret:
      - ai-pc-ai-pc-robot
  podSecurityContext:
    enabled: false
  containerSecurityContext:
    enabled: false
  resources:
    requests:
      cpu: "250m"
      memory: "512Mi"
    limits:
      cpu: "500m"
      memory: "1Gi"
auth:
  password: "d5j7RKoPVzckWDDZ"
