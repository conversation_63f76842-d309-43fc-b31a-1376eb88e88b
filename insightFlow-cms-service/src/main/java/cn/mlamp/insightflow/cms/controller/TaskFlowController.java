package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.model.vo.TaskFlowListVO;
import cn.mlamp.insightflow.cms.model.vo.TaskFlowTreeVO;
import cn.mlamp.insightflow.cms.service.ICmsTaskFlowService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 任务流管理控制器
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Slf4j
@RestController
@RequestMapping("/task-flow")
@RequiredArgsConstructor
@Tag(name = "任务流管理接口")
public class TaskFlowController {

    private final ICmsTaskFlowService taskFlowService;

    @GetMapping("/list")
    @Operation(summary = "获取任务流列表")
    public RespBody<Page<TaskFlowListVO>> getTaskFlowList(
            @Parameter(description = "当前页", required = false) @RequestParam(value = "current", defaultValue = "1") Integer current,
            @Parameter(description = "页大小", required = false) @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @Parameter(description = "任务类型", required = false) @RequestParam(value = "taskType", required = false) Integer taskType) {

        Integer tenantId = UserContext.getTenantId();

        Page<TaskFlowListVO> result = taskFlowService.getTaskFlowList(current, pageSize, taskType, null, tenantId);

        return RespBody.ok(result);
    }

    @GetMapping("/tree/{flowId}")
    @Operation(summary = "获取任务流树形结构")
    public RespBody<List<TaskFlowTreeVO>> getTaskFlowTree(
            @Parameter(description = "流程ID", required = true) @PathVariable Integer flowId) {

        Integer tenantId = UserContext.getTenantId();

        List<TaskFlowTreeVO> result = taskFlowService.getTaskFlowTree(flowId, null, tenantId);

        return RespBody.ok(result);
    }
}
