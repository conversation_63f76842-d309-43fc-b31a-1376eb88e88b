{{-  $t2vOpenAI := and (index .Values "modules" "text2vec-openai" "enabled") (index .Values "modules" "text2vec-openai" "apiKey") -}}
{{-  $qnaOpenAI := and (index .Values "modules" "qna-openai" "enabled") (index .Values "modules" "qna-openai" "apiKey") }}
{{-  $generativeOpenAI := and (index .Values "modules" "generative-openai" "enabled") (index .Values "modules" "generative-openai" "apiKey") }}
{{-  $openAI := or ($t2vOpenAI) ($qnaOpenAI) ($generativeOpenAI) }}
{{- if $openAI }}

{{- $apiKeys := list }}
{{- if $t2vOpenAI }}
  {{- $apiKeys = append $apiKeys (index .Values "modules" "text2vec-openai" "apiKey") -}}
{{- end }}
{{- if $qnaOpenAI }}
  {{- $apiKeys = append $apiKeys (index .Values "modules" "qna-openai" "apiKey") -}}
{{- end }}
{{- if $generativeOpenAI }}
  {{- $apiKeys = append $apiKeys (index .Values "modules" "generative-openai" "apiKey") -}}
{{- end }}

{{- if gt (len ($apiKeys | uniq)) 1 -}}
  {{- fail "OpenAI modules activated, but their keys differ. Use the same key on all activated OpenAI modules." }}
{{- end }}

apiVersion: v1
kind: Secret
metadata:
  name: weaviate-openai
  labels:
    app.kubernetes.io/name: weaviate
    app.kubernetes.io/managed-by: helm
type: Opaque
data:
  {{- if (index .Values "modules" "qna-openai" "apiKey") }}
  apiKey: {{ index .Values "modules" "qna-openai" "apiKey" | b64enc }}
  {{- else if (index .Values "modules" "generative-openai" "apiKey") }}
  apiKey: {{ index .Values "modules" "generative-openai" "apiKey" | b64enc }}
  {{- else }}
  apiKey: {{ index .Values "modules" "text2vec-openai" "apiKey" | b64enc }}
  {{- end }}
{{- end }}
