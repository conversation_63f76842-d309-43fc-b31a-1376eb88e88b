package cn.mlamp.insightflow.cms.entity;

import cn.mlamp.insightflow.cms.enums.MemberSourceTypeEnum;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("cms_tenant_member")
public class CmsTenantMember {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer tenantId;
    private Integer orderId;

    private String memberName;
    /**
     * {@link MemberSourceTypeEnum#getSourceType()}
     */
    private String memberSourceType;
    private Integer memberLevel;
    private String memberLevelName;

    private Integer hotStoryboard;
    private Integer gold3sSummary;
    private Long damCapacity;
    private Integer videoSynthesis;
    private LocalDateTime memberStartTime;
    private LocalDateTime memberExpireTime;

    // 数据逻辑删除标记 0:未删除 1:已删除
    private Integer isDeleted;

    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime updateTime;
}