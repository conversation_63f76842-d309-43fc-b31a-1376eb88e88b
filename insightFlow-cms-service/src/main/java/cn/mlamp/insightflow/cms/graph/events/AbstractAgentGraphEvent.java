package cn.mlamp.insightflow.cms.graph.events;

import lombok.Getter;

@Getter
public abstract class AbstractAgentGraphEvent<T> implements AgentGraphEvent<T> {

    private final String conversationId;

    private final String messageId;

    private final long createTime;

    public AbstractAgentGraphEvent(
            String conversationId,
            String messageId
    ) {
        this.conversationId = conversationId;
        this.messageId = messageId;
        this.createTime = System.currentTimeMillis();
    }

    @Override
    public Chunk<T> toChunk() {
        final Chunk<T> chunk = new Chunk<T>();
        chunk.setEvent(getEvent());
        chunk.setConversationId(conversationId);
        chunk.setMessageId(messageId);
        chunk.setRunId(getRunId());
        chunk.setCreateTime(createTime);
        chunk.setData(getEventData());
        return chunk;
    }

    protected abstract T getEventData();

    public String getRunId() {
        return null;
    }

}
