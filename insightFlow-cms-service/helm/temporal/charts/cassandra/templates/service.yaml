apiVersion: v1
kind: Service
metadata:
  name: {{ template "cassandra.fullname" . }}
  labels:
    app: {{ template "cassandra.name" . }}
    chart: {{ template "cassandra.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
  {{- with .Values.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  clusterIP: None
  type: {{ .Values.service.type }}
  ports:
    {{- if .Values.exporter.enabled }}
  - name: metrics
    port: 5556
    targetPort: {{ .Values.exporter.port }}
    {{- end }}
  - name: intra
    port: 7000
    targetPort: 7000
  - name: tls
    port: 7001
    targetPort: 7001
  - name: jmx
    port: 7199
    targetPort: 7199
  - name: cql
    port: {{ default 9042 .Values.config.ports.cql }}
    targetPort: {{ default 9042 .Values.config.ports.cql }}
  - name: thrift
    port: {{ default 9160 .Values.config.ports.thrift }}
    targetPort: {{ default 9160 .Values.config.ports.thrift }}
  {{- if .Values.config.ports.agent }}
  - name: agent
    port: {{ .Values.config.ports.agent }}
    targetPort: {{ .Values.config.ports.agent }}
  {{- end }}
  selector:
    app: {{ template "cassandra.name" . }}
    release: {{ .Release.Name }}
