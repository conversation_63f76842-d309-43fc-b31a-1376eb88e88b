{{- if .Values.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    {{- include "prometheus-pushgateway.defaultLabels" . | nindent 4 }}
    {{- with .Values.serviceAccountLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ include "prometheus-pushgateway.serviceAccountName" . }}
  namespace: {{ template "prometheus-pushgateway.namespace" . }}
automountServiceAccountToken: {{ .Values.automountServiceAccountToken }}
{{- end }}
