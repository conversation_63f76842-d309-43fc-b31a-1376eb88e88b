package cn.mlamp.insightflow.cms.graph.events;

import cn.mlamp.insightflow.cms.enums.chat.MessageRunStatus;
import lombok.Data;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * Agent Graph消息事件 用于传递节点执行过程中的消息信息
 */
@Getter
public class AgentGraphToolErrorEvent extends AbstractAgentGraphEvent<AgentGraphToolErrorEvent.EventData> {

    private final EventType event = EventType.TOOL_ERROR;

    private final String runId;

    private final String name;

    private final String label;

    private final String input;

    private final String error;

    private final Map<String, Object> metadata;

    public AgentGraphToolErrorEvent(
            String conversationId,
            String messageId,
            String runId,
            String name,
            String label,
            String input,
            String error
    ) {
        this(conversationId, messageId, runId, name, label, input, error, new HashMap<>());
    }

    public AgentGraphToolErrorEvent(
            String conversationId,
            String messageId,
            String runId,
            String name,
            String label,
            String input,
            String error,
            Map<String, Object> metadata
    ) {
        super(conversationId, messageId);
        this.runId = runId;
        this.name = name;
        this.label = label;
        this.input = input;
        this.error = error;
        this.metadata = new HashMap<>(metadata);
    }

    @Override
    protected EventData getEventData() {
        final EventData eventData = new EventData();
        eventData.setError(error);
        eventData.setElapsedTime(System.currentTimeMillis() - getCreateTime());
        eventData.setCreateTime(getCreateTime());
        eventData.setFinishTime(System.currentTimeMillis());
        return eventData;
    }

    @Data
    public static class EventData {

        private MessageRunStatus status = MessageRunStatus.FAILED;

        private String error;

        private long elapsedTime;

        private long createTime;

        private long finishTime;

    }

}
