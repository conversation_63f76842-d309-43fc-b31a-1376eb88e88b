{{ if and (index .Values "backups" "gcs" "enabled") (index .Values "backups" "gcs" "secrets") }}
{{- if index .Values "backups" "gcs" "secrets" "GOOGLE_APPLICATION_CREDENTIALS" }}
apiVersion: v1
kind: Secret
metadata:
  name: backup-gcs
  labels:
    app.kubernetes.io/name: weaviate
    app.kubernetes.io/managed-by: helm
type: Opaque
data:
  GOOGLE_APPLICATION_CREDENTIALS: {{ index .Values "backups" "gcs" "secrets" "GOOGLE_APPLICATION_CREDENTIALS" | b64enc }}
{{- end }}
{{ end }}
