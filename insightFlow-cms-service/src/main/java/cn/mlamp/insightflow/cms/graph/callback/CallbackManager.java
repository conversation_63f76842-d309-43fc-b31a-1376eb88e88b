package cn.mlamp.insightflow.cms.graph.callback;

import cn.mlamp.insightflow.cms.graph.entities.EnvInfo;
import cn.mlamp.insightflow.cms.graph.events.action.ActionType;
import cn.mlamp.insightflow.cms.model.vo.chat.ResourceVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.service.Result;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CallbackManager {

    private static final ThreadLocal<CallbackManager> CALLBACK_MANAGER_THREAD_LOCAL = new InheritableThreadLocal<>();

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private final List<Callback> callbacks;

    public CallbackManager(Callback... callbacks) {
        this.callbacks = Arrays.asList(callbacks);
    }

    public static void set(CallbackManager callbackManager) {
        CALLBACK_MANAGER_THREAD_LOCAL.set(callbackManager);
    }

    public static void clear() {
        CALLBACK_MANAGER_THREAD_LOCAL.remove();
    }

    public static CallbackManager get() {
        CallbackManager callbackManager = CALLBACK_MANAGER_THREAD_LOCAL.get();
        if (callbackManager == null) {
            throw new RuntimeException("无法获取CallbackManger");
        }
        return callbackManager;
    }

    public void onWorkflowStarted(String query, EnvInfo envInfo) {
        callbacks.forEach(callback -> callback.onWorkflowStarted(query, envInfo));
    }

    public void onWorkflowFinished(String answer) {
        callbacks.forEach(callback -> callback.onWorkflowFinished(answer));
    }

    public void onWorkflowError(Throwable t) {
        callbacks.forEach(callback -> callback.onWorkflowError(t));
    }

    public void onChatModelStarted(String runId, String modelName, List<ChatMessage> messages) {
        callbacks.forEach(callback -> callback.onChatModelStarted(runId, modelName, messages));
    }

    public void onChatModelFinished(String runId, String modelName, List<ChatMessage> messages, Result<?> result) {
        callbacks.forEach(callback -> callback.onChatModelFinished(runId, modelName, messages, result));
    }

    public void onChatModelError(String runId, String modelName, List<ChatMessage> messages, Throwable t) {
        callbacks.forEach(callback -> callback.onChatModelError(runId, modelName, messages, t));
    }

    public void onMessage(String runId, String message) {
        callbacks.forEach(callback -> callback.onMessage(runId, message));
    }

    public void onMessageEnd(String runId, String content) {
        callbacks.forEach(callback -> callback.onMessageEnd(runId, content));
    }

    public void onNodeStarted(String runId, String nodeName, String goal) {
        callbacks.forEach(callback -> callback.onNodeStarted(runId, nodeName, goal));
    }

    public void onNodeFinished(String runId, String nodeName, String response) {
        callbacks.forEach(callback -> callback.onNodeFinished(runId, nodeName, response));
    }

    public void onNodeError(String runId, String nodeName, Throwable t) {
        callbacks.forEach(callback -> callback.onNodeError(runId, nodeName, t));
    }

    public void onToolStarted(String runId, String toolName, String label, String input) {
        callbacks.forEach(callback -> callback.onToolStarted(runId, toolName, label, input));
    }

    public void onToolFinished(String runId, String toolName, String label, String input, String output, ResourceVO... resources) {
        callbacks.forEach(callback -> callback.onToolFinished(runId, toolName, label, input, output, resources));
    }

    public void onToolError(String runId, String toolName, String label, String input, Throwable t) {
        callbacks.forEach(callback -> callback.onToolError(runId, toolName, label, input, t));
    }

    public void onAction(String runId, ActionType actionType, Object parameters) {
        callbacks.forEach(callback -> callback.onAction(runId, actionType, parameters));
    }

    public void close() {
        callbacks.forEach(Callback::close);
    }

}
