{{- if .Values.sandbox.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "dify.sandbox.fullname" . }}
  labels:
{{- include "dify.labels" . | nindent 4 }}
    component: sandbox
{{ include "dify.ud.labels" . | indent 4 }}
data:
  pip.conf: |-
    [global]
    index-url = {{ .Values.sandbox.pip.indexUrl }}
  python-requirements.txt: |-
    numpy==2.0.1
    openpyxl==3.1.5
    pandas==2.2.2
    fuzzywuzzy==0.18.0
    scikit-learn==1.5.1

{{- end }}