package cn.mlamp.insightflow.cms.enums;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-30
 */
public enum VideoInfoStatusEnum {
    /**
     * 1：待处理；2：处理中，3：完成，4：失败
     */


    WAITING(1, "待处理"),


    PROCESSING(2, "处理中"),

    SUCCESS(3, "完成"),

    ERROR(4, "失败");


    private final int code;

    private final String msg;

    VideoInfoStatusEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
