package cn.mlamp.insightflow.cms.graph.entities;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.mlamp.insightflow.cms.graph.entities.frontend.FrontendInfo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.DayOfWeek;
import java.time.LocalDateTime;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class EnvInfo {

    private FrontendInfo frontend;

    private String getTimeInfo() {
        // 获取准确星期几
        final LocalDateTime now = LocalDateTime.now();
        final DayOfWeek dayOfWeek = now.getDayOfWeek();
        final String dayOfWeekStr = switch (dayOfWeek) {
            case MONDAY -> "星期一";
            case TUESDAY -> "星期二";
            case WEDNESDAY -> "星期三";
            case THURSDAY -> "星期四";
            case FRIDAY -> "星期五";
            case SATURDAY -> "星期六";
            case SUNDAY -> "星期日";
        };
        return LocalDateTimeUtil.format(now, "yyyy年MM月dd日 HH:mm:ss " + dayOfWeekStr);
    }

    public String toPrettyEnv() {
        final StringBuilder sb = new StringBuilder();
        sb.append(getTimeInfo());
        sb.append("\n\n当前环境信息:\n");
        sb.append("页面:\n");
        sb.append("- URL: ").append(StrUtil.nullToEmpty(frontend.getUrl())).append("\n");
        sb.append("- 当前页面: ").append(StrUtil.nullToEmpty(frontend.getCurrent())).append("\n");
        return sb.toString();
    }

}
