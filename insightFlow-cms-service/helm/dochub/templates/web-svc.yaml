{{- if .Values.web.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "dify.web.fullname" . }}
  labels:
{{ include "dify.labels" . | indent 4 }}
{{- if .Values.web.service.labels }}
{{ toYaml .Values.web.service.labels | indent 4 }}
{{- end }}
    component: "web"
{{- with .Values.web.service.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
{{- end }}
spec:
  type: ClusterIP
  {{- if .Values.web.service.clusterIP }}
  clusterIP: {{ .Values.web.service.clusterIP }}
  {{- end }}
  ports:
    - name: web
      port: {{ .Values.web.service.port }}
      protocol: TCP
      targetPort: web
  selector:
{{ include "dify.selectorLabels" . | indent 4 }}
    component: "web"
{{- end }}