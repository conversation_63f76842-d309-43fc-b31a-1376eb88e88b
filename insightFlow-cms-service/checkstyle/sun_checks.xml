<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
        "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN"
        "https://checkstyle.org/dtds/configuration_1_3.dtd">

<!--
  Checkstyle configuration that checks the sun coding conventions from:
    - the Java Language Specification at
      https://docs.oracle.com/javase/specs/jls/se11/html/index.html
    - the Sun Code Conventions at https://www.oracle.com/java/technologies/javase/codeconventions-contents.html
    - the Javadoc guidelines at
      https://www.oracle.com/technical-resources/articles/java/javadoc-tool.html
    - the JDK Api documentation https://docs.oracle.com/en/java/javase/11/
    - some best practices
-->

<module name="Checker">
    <!-- 文件级别的检查 -->
    <property name="severity" value="error"/>

    <property name="fileExtensions" value="java, properties, xml"/>

    <!-- 检查文件是否包含tab字符 -->
    <module name="FileTabCharacter">
        <property name="eachLine" value="true"/>
    </module>

    <!-- 检查文件长度 -->
    <module name="FileLength">
        <property name="max" value="2000"/>
    </module>

    <!-- 检查行长度 -->
    <module name="LineLength">
        <property name="max" value="150"/>
    </module>

    <!-- 移除了文件末尾必须有新行的检查 -->

    <!-- 检查单个空行数量 -->
    <module name="RegexpSingleline">
        <property name="format" value="\s+$"/>
        <property name="minimum" value="0"/>
        <property name="maximum" value="0"/>
        <property name="message" value="Line has trailing spaces."/>
    </module>

    <!-- 设置header -->
    <!-- <module name="Header">
      <property name="headerFile" value="${checkstyle.header.file}"/>
      <property name="fileExtensions" value="java"/>
    </module> -->

    <!-- 语法树检查 -->
    <module name="TreeWalker">
        <!-- 命名约定 -->
        <module name="ConstantName"/>
        <module name="LocalFinalVariableName"/>
        <module name="LocalVariableName"/>
        <module name="MemberName"/>
        <module name="MethodName"/>
        <module name="PackageName"/>
        <module name="ParameterName"/>
        <module name="StaticVariableName"/>
        <module name="TypeName"/>

        <!-- 导入检查 -->
        <!-- 移除了AvoidStarImport规则，允许使用星号导入 -->
        <module name="IllegalImport"/> <!-- 默认拒绝sun.* -->
        <module name="RedundantImport"/>
        <module name="UnusedImports">
            <property name="processJavadoc" value="true"/>
        </module>

        <!-- 大小限制 -->
        <module name="MethodLength">
            <property name="max" value="250"/>
        </module>
        <module name="ParameterNumber">
            <property name="max" value="8"/>
            <property name="tokens" value="METHOD_DEF"/>
        </module>

        <!-- 空白检查 -->
        <module name="EmptyForIteratorPad"/>
        <module name="GenericWhitespace"/>
        <module name="MethodParamPad"/>
        <module name="NoWhitespaceAfter"/>
        <module name="NoWhitespaceBefore"/>
        <module name="OperatorWrap"/>
        <module name="ParenPad"/>
        <module name="TypecastParenPad"/>
        <module name="WhitespaceAfter"/>
        <module name="WhitespaceAround"/>

        <!-- 修饰符检查 -->
        <module name="ModifierOrder"/>
        <module name="RedundantModifier"/>

        <!-- 代码块检查 -->
        <module name="AvoidNestedBlocks"/>
        <module name="EmptyBlock"/>
        <module name="LeftCurly"/>
        <module name="NeedBraces"/>
        <module name="RightCurly"/>

        <!-- 代码问题检查 -->
        <module name="EmptyStatement"/>
        <module name="EqualsHashCode"/>
        <module name="HiddenField">
            <property name="ignoreConstructorParameter" value="true"/>
            <property name="ignoreSetter" value="true"/>
        </module>
        <module name="IllegalInstantiation"/>
        <module name="InnerAssignment"/>
        <module name="MagicNumber">
            <property name="ignoreNumbers" value="-1, 0, 1, 2"/>
            <property name="ignoreHashCodeMethod" value="true"/>
        </module>
        <module name="MissingSwitchDefault"/>
        <module name="MultipleVariableDeclarations"/>
        <module name="SimplifyBooleanExpression"/>
        <module name="SimplifyBooleanReturn"/>

        <!-- 类设计检查 -->
        <module name="DesignForExtension"/>
        <module name="HideUtilityClassConstructor"/>
        <module name="InterfaceIsType"/>
        <module name="VisibilityModifier"/>

        <!-- 其他杂项 -->
        <module name="ArrayTypeStyle"/>
        <module name="TodoComment"/>
        <module name="UpperEll"/>
    </module>
</module>
