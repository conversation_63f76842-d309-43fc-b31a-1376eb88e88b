#!/bin/bash

base_path=$(
    cd $(dirname $0)
    pwd
)

if [[ $# != 2 ]]; then
  echo "usage: "
  echo "  bash ./update_values.sh <key> <value>"
  exit 1
fi

key=$1
value=$2

values_path=${base_path}/../insight-flow-cms/values.yaml

if command -v yq >/dev/null 2>&1; then
    :
else
    echo "未安装工具: yq, 可使用brew直接安装, 或自行下载二进制文件: https://github.com/mikefarah/yq"
    exit 1
fi

if [[ $(yq "${key} != null" ${values_path}) != 'true' ]]; then
  echo "* null or illegal key in values.yaml: ${key} "
  echo "* use correct key such .image.tag"
  exit 1
fi

echo "set $key=$value"
yq -i ${key}"="${value} ${values_path}
