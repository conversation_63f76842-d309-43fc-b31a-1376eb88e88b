package cn.mlamp.insightflow.cms.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.model.dto.ConsumptionExportDTO;
import cn.mlamp.insightflow.cms.model.query.ConsumptionQueryDTO;
import cn.mlamp.insightflow.cms.model.vo.ConsumptionVO;
import cn.mlamp.insightflow.cms.model.vo.MobileInfoVO;
import cn.mlamp.insightflow.cms.model.vo.TenantTokenSummaryVO;
import cn.mlamp.insightflow.cms.service.TenantTokenService;
import cn.mlamp.insightflow.cms.service.TokenUseDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-19
 */
@Slf4j
@RequestMapping("/tenant/token")
@RestController
@RequiredArgsConstructor
@Tag(name = "租户Token接口")
public class TenantTokenController {

    private final TenantTokenService tenantTokenService;

    private final TokenUseDetailService tokenUseDetailService;

    @GetMapping("/summary")
    @Operation(summary = "获取租户Token信息接口")
    public RespBody<TenantTokenSummaryVO> getSummary() {
        Integer tenantId = UserContext.getTenantId();
        return RespBody.ok(tenantTokenService.getSummary(tenantId));
    }


    @GetMapping("/get/user/token")
    @Operation(summary = "保存用户Token明细")
    public RespBody<?> getaUserToken(String observationId, Integer taskId, Integer taskType, String taskName, Integer tenantId,
                                     String userId, String userName) {
        tokenUseDetailService.countTokenUse(observationId, taskId, taskType, taskName, tenantId, userId, userName);
        return RespBody.ok("成功");
    }


    @PostMapping("/list-consumption")
    @Operation(summary = "获取累积消费列表")
    public RespBody<Page<ConsumptionVO>> listConsumption(@RequestBody ConsumptionQueryDTO queryDTO) {
        Integer tenantId = UserContext.getTenantId();
        Page<ConsumptionVO> list = tokenUseDetailService.listConsumptions(tenantId, queryDTO);
        return RespBody.ok(list);
    }

    @GetMapping("/export-consumption")
    @Operation(summary = "导出累积消费列表")
    public void exportConsumption(ConsumptionQueryDTO queryDTO, HttpServletResponse response) throws IOException {
        Integer tenantId = UserContext.getTenantId();

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

        // 流式导出，每次查询1000条
        int pageSize = 1000;
        int pageNum = 1;

        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).inMemory(true).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet("消费列表").head(ConsumptionExportDTO.class).build();

            while (true) {
                // 设置分页参数
                queryDTO.setPageNum(pageNum);
                queryDTO.setPageSize(pageSize);

                Page<ConsumptionVO> page = tokenUseDetailService.listConsumptions(tenantId, queryDTO);
                List<ConsumptionVO> list = page.getRecords();
                if (list.isEmpty()) {
                    break;
                }

                // 转换为导出DTO
                List<ConsumptionExportDTO> exportList = new ArrayList<>(list.size());
                for (ConsumptionVO vo : list) {
                    ConsumptionExportDTO exportDTO = new ConsumptionExportDTO();
                    exportDTO.setTaskName(vo.getTaskName());
                    exportDTO.setTaskType(vo.getTaskType());
                    exportDTO.setUsageTime(vo.getUsageTime());
                    exportDTO.setTokens(vo.getTokens());
                    exportDTO.setUserName(vo.getUserName());
                    exportList.add(exportDTO);
                }

                excelWriter.write(exportList, writeSheet);
                pageNum++;

                // 如果返回数量小于pageSize，说明是最后一页
                if (list.size() < pageSize) {
                    break;
                }
            }
        }
    }

    // ==================== 移动端接口 ====================

    @GetMapping("/mobile/list-consumption")
    @Operation(summary = "移动端批量查询消费列表")
    public RespBody<Page<ConsumptionVO>> mobileListConsumption(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        Integer tenantId = UserContext.getTenantId();

        // 构建查询参数
        ConsumptionQueryDTO queryDTO = new ConsumptionQueryDTO();
        queryDTO.setPageNum(current);
        queryDTO.setPageSize(pageSize);

        Page<ConsumptionVO> list = tokenUseDetailService.listMobileConsumptions(tenantId, queryDTO);
        return RespBody.ok(list);
    }

    @GetMapping("/mobile/user-info")
    @Operation(summary = "移动端用户信息查询")
    public RespBody<MobileInfoVO> mobileUserInfo() {
        Integer tenantId = UserContext.getTenantId();
        MobileInfoVO summary = tokenUseDetailService.getMobileinfo(tenantId);
        return RespBody.ok(summary);
    }

}
