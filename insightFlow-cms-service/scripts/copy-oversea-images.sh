#!/bin/bash

SOURCE_REGISTRY=hub.intra.mlamp.cn/ai-pc
TARGET_REGISTRY=tsg-miaoacms-tcr1.tencentcloudcr.com/cms

images=(
    "insightflow-cms-service:452103"
    "temporalio-server:1.27.2"
    "temporalio-admin-tools:1.27.2-tctl-1.18.2-cli-1.3.0"
    "temporalio-ui:2.37.1"
    "redis-sentinel:8.0.2-debian-12-r2"
    "redis:8.0.2-debian-12-r3"
)

for image in ${images[@]} ; do
    skopeo copy docker://$SOURCE_REGISTRY/$image docker://$TARGET_REGISTRY/$image
done

SOURCE_REGISTRY=hub.intra.mlamp.cn/mz-llm
images=(
    "dochub-api:73ffd25b531717857b5b904f6e75f5027230ee83"
    "dochub-sandbox:v0.2.10"
    "dochub-web:73ffd25b531717857b5b904f6e75f5027230ee83"
    "redis:7.0.11-debian-11-r12"
    "postgresql:16.2.0-0.6.2-0.2.1-20240516"
    "pgvector:16-0.6.2-0.2.1"
    "nginx:latest"
)

for image in ${images[@]} ; do
    skopeo copy docker://$SOURCE_REGISTRY/$image docker://$TARGET_REGISTRY/$image
done
