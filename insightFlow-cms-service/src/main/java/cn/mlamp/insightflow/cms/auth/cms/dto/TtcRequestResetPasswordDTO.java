package cn.mlamp.insightflow.cms.auth.cms.dto;

import cn.mlamp.insightflow.cms.constant.CommonConstant;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Ttc重置密码DTO
 *
 * <AUTHOR>
 * @since 2022-09-13 16:28:07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
public class TtcRequestResetPasswordDTO extends TtcRequestBaseDTO {

    /**
     * 类型： 1-邮箱验证 2-手机号验证
     */
    @ToString.Include
    @NotNull(message = "override_message.verification_type_not_null")
    private Integer verificationType;
    /**
     * 根据verificationType，填写 邮箱或手机号
     */
    @ToString.Include
    @NotBlank(message = "override_message.verification_target_not_blank")
    private String verificationTarget;
    /**
     * 验证码
     */
    @ToString.Include
    @Pattern(regexp = CommonConstant.VERIFICATION_CODE_REGX, message = "override_message.verification_code_invalid")
    private String verificationCode;
    /**
     * 密码
     */
    @ToString.Exclude
    @NotBlank(message = "override_message.password_not_blank")
    @Pattern(regexp = CommonConstant.PASSWORD_REGX, message = "override_message.password_invalid")
    private String password;
    /**
     * 密码 - 二次
     */
    @ToString.Exclude
    @NotBlank(message = "override_message.password_check_not_blank")
    private String passwordCheck;

}
