---
description:
globs: src/main/java/cn/mlamp/insightflow/cms/graph/**/*.java
alwaysApply: false
---
背景:
基于langgraph4j构建多Agent协作场景，目的是解决CMS基于大模型提供更方便、快捷、自动化的智能体验，只需要一句话，就可以完成所有步骤。

Node:
SupervisorNode: 协调多Agent进行合作
FeaturedVideosNode: Agent, Tools, 根据用户需求搜索、推荐一些热门视频
AIScriptNode: Agent, Tools, 支持根据用户需求，生成、更新的AI视频脚本

代码要求:
1. 相关代码全部集中在src/main/java/cn/mlamp/insightflow/cms/graph中，只有接口放在src/main/java/cn/mlamp/insightflow/cms/controller

入参:
```json
{
	"conversationId": "",
	"query": "",
	"env": {
		"frontend": {
			"url": "https://insight-flow-cms-test.mlamp.cn/selection-content", // 当前页面url
			"current": "featuredVideos", // scriptGenerate, featuredVideos, videoAnalysis, threeSeconds, taskCenter, materialLibrary, aiScript, videoGenerate
			"scriptGenerate": {
				"form": {
					"upload": {
						"type": "url", // url, file
						"url": {
							"files": [], // urls
						},
						"file": {
							"files": [], // ossObjIds
						}
					}
				}
			},
			"featuredVideos": {
				"form": {
					"search": {
						"selectType": 0, // 按综合搜索，按品牌搜索，按产品搜索，按标题搜索，按口播文案搜索
						"keyword": "",
						"rankingType": "",
						"industry": "",
						"materialForm": "",
						"consumeRange": "",
						"durationMin": 0,
						"durationMax": 15,
						"publishTimeStart": "2025-05-30T00:00:00",
						"publishTimeEnd": "2025-06-06T23:59:59",
						"pageNum": 1,
						"pageSize": 20,
						"sortOptions": []
					}
				}
			},
			"videoAnalysis": {
				"id": 123456
				"form": {
					"aiScript": {
						"paraphrasedContent": { // 仿写内容
							"brand": "",
							"product": "",
							"sellingPoint": "",
							"scene": "",
							"startGold3s": "",
							"targetAudience": "",
							"storyboardIds": []
						}
					}
				}
			},
			"aiScript": {
				"id": 123456,
				"form": {
					"videoGenerate": {
						"storyBoards": []
					}
				}
			},
			"videoGenerate": {
				"id": 123456,
				"form": {
					"videoCompositing": {
						"taskInfo": "",
						"assetIds": [],
						"videoOssIds": []
					}
				}
			},
			"threeSeconds": {
				"form": {
					"search": {
						"industry": "",
						"tag": "",
						"sortField": "",
						"sortOrder": "",
						"current": 1,
						"pageSize": 10
					}
				}
			},
			"taskCenter": {
			},
			"materialLibrary": {
			}
		}
	}
}
```

出参(SSE):
```json
{
	"event": "started", // 完成
	"conversationId": "xxxxx",
	"messageId": "xxxxx",
	"createTime": 1749217431000
	"data": {
	}
}
```

```json
{
	"event": "finished", // 完成
	"conversationId": "xxxxx",
	"messageId": "xxxxx",
	"createTime": 1749217431000
	"data": {
		"elapsedTime": 1000, // 耗时ms
		"createTime": 1749217431000, // 对话开始时间
		"finishTime": 1749217431000 // 对话结束时间
	}
}
```

```json
{
	"event": "error", // 失败
	"conversationId": "xxxxx",
	"messageId": "xxxxx",
	"createTime": 1749217431000
	"data": {
		"elapsedTime": 1000, // 耗时ms
		"error": "",
		"createTime": 1749217431000, // 对话开始时间
		"finishTime": 1749217431000 // 对话结束时间
	}
}
```

```json
{
	"event": "message", // 消息
	"conversationId": "xxxxx",
	"messageId": "xxxxx",
    "runId": "xxxxxxxx",
	"createTime": 1749217431000
	"data": {
		"text": "" // 流式响应文本内容
	}
}
```

```json
{
	"event": "message_end", // 消息
	"conversationId": "xxxxx",
	"messageId": "xxxxx",
    "runId": "xxxxxxxx",
	"createTime": 1749217431000
	"data": {
                "text": "xxxxx" // message全量文本数据
	}
}
```

```json
{
	"event": "tool_started", // 开始执行tool
	"conversationId": "xxxxx",
	"messageId": "xxxxx",
    "runId": "xxxxxxxx",
	"createTime": 1749217431000
	"data": {
		"name": "tool_name", // tool名称,
        "label": "工具名",
		"input": "", // json字符串
		"env": {},
		"createTime": 1749217431000
	}
}
```

```json
{
	"event": "tool_finished", // 执行tool完毕
	"conversationId": "xxxxx",
	"messageId": "xxxxx",
    "runId": "xxxxxxxx",
	"createTime": 1749217431000
	"data": {
		"name": "xxx", // tool名称
		"label": "", // 用于显示
		"env": {},
		"input": "", // json字符串
		"output": "",
		"resources": [
			{
				"type": "aiScript", // videoGenerate
				"aiScript": {
					"id": 123456
				},
				"videoGenerate": {
					"id": 123456
				}
			}
		],
                "elapsedTime": 1000, // 耗时ms
		"createTime": 1749217431000,
                "finishTime": 1749217431000
	}
}
```

```json
{
	"event": "tool_error", // 执行tool错误
	"conversationId": "xxxxx",
	"messageId": "xxxxx",
    "runId": "xxxxxxxx",
	"createTime": 1749217431000
	"data": {
		"name": "xxx", // tool名称
		"label": "", // 用于显示
		"env": {},
		"input": "", // json字符串
		"output": "",
        "error": "", // 错误信息
		"resources": [
			{
				"type": "aiScript", // videoGenerate
				"aiScript": {
					"id": 123456
				},
				"videoGenerate": {
					"id": 123456
				}
			}
		],
        "elapsedTime": 1000, // 耗时ms
		"createTime": 1749217431000,
        "finishTime": 1749217431000
	}
}
```

```json
{
	"event": "action", // 前端执行
	"conversationId": "xxxxx",
	"messageId": "xxxxx",
	"createTime": 1749217431000
	"data": {
		"type": "dropdown",
		"dropdown": {
			options: [
				{"label": "a1", "value": "b1"}
			]
		}
	}
}
```

```json
{
	"event": "ping", // 10s 一次，保持连接存活
}
```

