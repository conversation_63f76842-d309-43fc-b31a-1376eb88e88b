package cn.mlamp.insightflow.cms.enums;

import cn.mlamp.insightflow.cms.constant.Constants;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 支付渠道
 */
@RequiredArgsConstructor
public enum PayChannelEnum {
    // 微信支付
    WECHAT("wechat", Constants.WECHAT_PAY_SERVICE_BEAN_NAME),

    // 支付宝
    // ALIPAY("alipay", ""),

    ;

    @Getter
    private final String channelName;

    /**
     * @{link cn.mlamp.insightflow.cms.service.PayService} 实现类bean名称
     */
    @Getter
    private final String beanName;

    private static final Map<String, PayChannelEnum> NAME_TO_ENUM = Arrays.stream(PayChannelEnum.values())
            .collect(Collectors.toUnmodifiableMap(PayChannelEnum::getChannelName, v -> v));

    public static PayChannelEnum fromChannelName(String channelName) {
        return NAME_TO_ENUM.get(channelName);
    }

}