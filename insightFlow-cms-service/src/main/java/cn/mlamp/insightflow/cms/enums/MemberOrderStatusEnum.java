package cn.mlamp.insightflow.cms.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 会员订单状态枚举
 */
@RequiredArgsConstructor
public enum MemberOrderStatusEnum {
    // 未支付
    NOT_PAY("not_pay"),
    // 支付完成
    PAID("paid"),
    // 超时未支付
    PAY_TIMEOUT("pay_timeout"),

    // 未生效
    NOT_EFFECT("not_effect"),
    // 已生效
    EFFECT("effect"),
    // 失效
    INVALID("invalid"),

    ;

    @Getter
    private final String status;
}