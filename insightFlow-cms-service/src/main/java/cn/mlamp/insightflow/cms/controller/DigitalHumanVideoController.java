package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.annotation.HasMemberRightCheck;
import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.enums.MemberRightEnum;
import cn.mlamp.insightflow.cms.model.dto.DigitalHumanVideoDTO;
import cn.mlamp.insightflow.cms.model.vo.DigitalHumanVideoVO;
import cn.mlamp.insightflow.cms.service.DigitalHumanVideoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 数字人视频生成控制器
 */
@Slf4j
@RestController
@RequestMapping("/digital-human-video")
@Tag(name = "数字人视频生成接口")
public class DigitalHumanVideoController {

    @Autowired
    private DigitalHumanVideoService digitalHumanVideoService;

    /**
     * 创建数字人视频生成任务
     */
    @PostMapping("/create")
    @Operation(summary = "创建数字人视频生成任务")
    @HasMemberRightCheck(rights = {MemberRightEnum.VIDEO_DURATION})
    public RespBody<DigitalHumanVideoVO.DigitalHumanVideoCreateResponse> createDigitalHumanVideoTask(
            @RequestBody @Valid DigitalHumanVideoDTO.DigitalHumanVideoCreateRequest request) {

        // 获取用户信息
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        // 创建任务并获取任务ID
        DigitalHumanVideoVO.DigitalHumanVideoCreateResponse result = digitalHumanVideoService
                .createDigitalHumanVideoTask(request, userId, tenantId);

        // 异步执行数字人视频生成任务
        digitalHumanVideoService.generateDigitalHumanVideoAsync(request, result.getTaskId(), userId, tenantId);

        return RespBody.ok(result);
    }

}
