{{- if .Values.service.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "prometheus-node-exporter.fullname" . }}
  namespace: {{ include "prometheus-node-exporter.namespace" . }}
  labels:
    {{- include "prometheus-node-exporter.labels" $ | nindent 4 }}
  {{- with .Values.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
{{- if .Values.service.ipDualStack.enabled }}
  ipFamilies: {{ toYaml .Values.service.ipDualStack.ipFamilies | nindent 4 }}
  ipFamilyPolicy: {{ .Values.service.ipDualStack.ipFamilyPolicy }}
{{- end }}
{{- if .Values.service.externalTrafficPolicy }}
  externalTrafficPolicy: {{ .Values.service.externalTrafficPolicy }}
{{- end }}
  type: {{ .Values.service.type }}
{{- if and (eq .Values.service.type "ClusterIP") .Values.service.clusterIP }}
  clusterIP: "{{ .Values.service.clusterIP }}"
{{- end }}
  ports:
    - port: {{ .Values.service.port }}
      {{- if ( and (eq .Values.service.type "NodePort" ) (not (empty .Values.service.nodePort)) ) }}
      nodePort: {{ .Values.service.nodePort }}
      {{- end }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: {{ .Values.service.portName }}
  selector:
    {{- include "prometheus-node-exporter.selectorLabels" . | nindent 4 }}
{{- end }}
