package cn.mlamp.insightflow.cms.graph.tools;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.mlamp.insightflow.cms.graph.callback.CallbackManager;
import cn.mlamp.insightflow.cms.graph.entities.EnvInfo;
import cn.mlamp.insightflow.cms.graph.entities.frontend.FrontendInfo;
import cn.mlamp.insightflow.cms.graph.helper.EnvHelper;
import cn.mlamp.insightflow.cms.graph.nodes.FeaturedVideosNode;
import cn.mlamp.insightflow.cms.model.dto.QianchuanVideoHotspotQueryDTO;
import cn.mlamp.insightflow.cms.model.vo.QianchuanMaterialVideoVO;
import cn.mlamp.insightflow.cms.service.QianchuanVideoHotspotService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.ObjectMapper;
import dev.langchain4j.agent.tool.P;
import dev.langchain4j.agent.tool.Tool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 精选视频工具集 按照Langgraph4j最佳实践实现的工具调用
 */
@Slf4j
@Component
public class FeaturedVideosTools {

    private static final String ALL = "all";

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private QianchuanVideoHotspotService hotspotService;

    private boolean isCurrentPage() {
        final EnvInfo env = EnvHelper.getEnv();
        final FrontendInfo frontend = env.getFrontend();
        if (frontend == null) {
            log.warn("当前环境前端信息未设置");
            return true;
        }
        final String current = frontend.getCurrent();
        return !Objects.equals(current, FeaturedVideosNode.NAME);
    }

    private QianchuanVideoHotspotQueryDTO getQueryDTO(EnvInfo envInfo) {
        final FrontendInfo frontend = envInfo.getFrontend();
        final FrontendInfo.FeaturedVideosDTO featuredVideos = frontend.getFeaturedVideos();
        if (featuredVideos == null || featuredVideos.getForm() == null || featuredVideos.getForm().getSearch() == null) {
            return new QianchuanVideoHotspotQueryDTO();
        }
        return featuredVideos.getForm().getSearch();
    }

    private String fixAll(String value) {
        if (StrUtil.isBlank(value) || ALL.equalsIgnoreCase(value)) {
            return null; // 返回null表示查询全部
        }
        return value;
    }

    @Tool("搜索爆款视频")
    public String searchFeaturedVideos(
            @P(value = "搜索关键词, 可输入品牌、产品、标题、口播文案关键词", required = false) String keyword,
            @P(value = "榜单, 枚举: 新兴榜, 总榜", required = false) String rankingType,
            @P(value = "素材形式, 枚举: 单人口播, 多人口播, 真人展示, 商品口播, 商品展示, 剧情演绎", required = false) String materialForm,
            @P(value = "消耗区间, 枚举: 0-10w, 10w-20w, 20w-30w, 30w-40w, 40w-50w, 60w-70w, 70w-80w, 80w-90w, 100w以上", required = false) String consumeRange,
            @P(value = "视频时长(秒), 枚举: 15s以内, 15s-30s, 30s-45s, 45s-60s, 60s以上", required = false) String duration,
            @P(value = "发布时间范围, 格式: yyyy-MM-dd yyyy-MM-dd, 示例: \"2025-05-08 2025-05-17\": 最近7天", required = false) String publishTime
//            @P(value = "页码, 从1开始", required = false) Integer pageNum
//            @P(value = "每页数量, 默认20", required = false) Integer pageSize
    ) {
        final String runId = UUID.randomUUID().toString();
        final CallbackManager callbackManager = CallbackManager.get();

        final QianchuanVideoHotspotQueryDTO queryDTO = new QianchuanVideoHotspotQueryDTO();
        queryDTO.setKeyword(keyword);
        queryDTO.setRankingType(rankingType);
        queryDTO.setMaterialForm(fixAll(materialForm));
        queryDTO.setConsumeRange(fixAll(consumeRange));

        queryDTO.setPageNum(1); // 默认页码为1
        queryDTO.setPageSize(10); // 默认每页10条

        if (StrUtil.isNotBlank(duration)) {
            switch (duration) {
                case "15s以内":
                    queryDTO.setDurationMin(0);
                    queryDTO.setDurationMax(15);
                    break;
                case "15s-30s":
                    queryDTO.setDurationMin(15);
                    queryDTO.setDurationMax(30);
                    break;
                case "30s-45s":
                    queryDTO.setDurationMin(30);
                    queryDTO.setDurationMax(45);
                    break;
                case "45s-60s":
                    queryDTO.setDurationMin(45);
                    queryDTO.setDurationMax(60);
                    break;
                case "60s以上":
                    queryDTO.setDurationMin(60);
                    queryDTO.setDurationMax(null); // 无上限
                    break;
                default:
                    return "无效的视频时长格式";
            }
        }

        if (StrUtil.isNotBlank(publishTime)) {
            if (publishTime.contains(" ")) {
                String[] parts = publishTime.split(" ");
                if (parts.length == 2) {
                    queryDTO.setPublishTimeStart(LocalDateTimeUtil.parse(parts[0] + " 00:00:00", "yyyy-MM-dd HH:mm:ss"));
                    queryDTO.setPublishTimeEnd(LocalDateTimeUtil.parse(parts[1] + " 23:59:59", "yyyy-MM-dd HH:mm:ss"));
                } else {
                    log.warn("发布时间格式错误, '{}' 格式 应为: 'yyyy-MM-dd yyyy-MM-dd'", publishTime);
                }
            }
        }

        callbackManager.onToolStarted(runId, "searchFeaturedVideos", "搜索精选视频", JSONUtil.toJsonStr(queryDTO));
        final String result = this.internalSearchFeaturedVideos(queryDTO);
        callbackManager.onToolFinished(runId, "searchFeaturedVideos", "搜索精选视频", JSONUtil.toJsonStr(queryDTO), result);

        return result;
    }

    private String internalSearchFeaturedVideos(QianchuanVideoHotspotQueryDTO queryDTO) {
        try {
            final IPage<QianchuanMaterialVideoVO> resultPage = hotspotService.listVideos(queryDTO);
            List<QianchuanMaterialVideoVO> records = resultPage.getRecords();
            if (CollectionUtil.isEmpty(records)) {
                return "没有找到相关视频";
            }

            final int limit = Math.min(records.size(), 3);
            // 最多显示3个视频
            return """
                    共搜索到了%d个视频, 并通过其它方式响应用户, 直接认为已完成搜索
                    前%d个视频列表:
                    %s
                    """.formatted(
                    resultPage.getTotal(),
                    limit, // 最多显示3个视频
                    records.stream()
                            .limit(limit)
                            .map(this::formatVideoInfo)
                            .collect(Collectors.joining("\n"))
            );
        } catch (Exception e) {
            log.error("搜索精选视频失败", e);
            return "搜索失败, 请重试...";
        }
    }

    @Tool("获取指定位置的爆款视频详情")
    public String getFeaturedVideos(
            @P(value = "视频所在视频列表中的位置, 0是第一个") List<Integer> indexes
    ) {
        final String runId = UUID.randomUUID().toString();
        final CallbackManager callbackManager = CallbackManager.get();
        callbackManager.onToolStarted(runId, "getFeaturedVideos", "获取指定位置的爆款视频详情", JSONUtil.toJsonStr(indexes));
        final String result = internalGetFeaturedVideos(indexes);
        callbackManager.onToolFinished(runId, "getFeaturedVideos", "获取指定位置的爆款视频详情", JSONUtil.toJsonStr(indexes), result);
        return result;
    }

    private String internalGetFeaturedVideos(List<Integer> indexes) {
        try {
            if (isCurrentPage()) {
                return "请在精选视频页面再使用此功能";
            }
            if (indexes == null || indexes.isEmpty()) {
                return "请提供要查询的视频索引列表";
            }
            final QianchuanVideoHotspotQueryDTO queryDTO = getQueryDTO(EnvHelper.getEnv());
            IPage<QianchuanMaterialVideoVO> page = hotspotService.listVideos(queryDTO);

            final List<QianchuanMaterialVideoVO> records = page.getRecords();
            if (CollectionUtil.isEmpty(records)) {
                return "没有找到相关视频";
            }

            final List<String> result = new ArrayList<>();
            for (int index : indexes) {
                if (index < 0 || index >= records.size()) {
                    result.add("视频索引" + index + "不存在");
                    continue;
                }
                final QianchuanMaterialVideoVO video = records.get(index);
                if (video == null) {
                    result.add("视频索引" + index + "不存在");
                    continue;
                }
                result.add(formatVideoInfo(video));
            }

            return String.join("\n", result);
        } catch (Exception e) {
            log.error("搜索精选视频失败", e);
            return "搜索失败, 请重试...";
        }
    }

    private String formatVideoInfo(QianchuanMaterialVideoVO video) {
        return """
                视频ID: %s, 标题: %s, 消耗: %s, 播放量: %d, 点赞量: %d, 评论量: %d, 完播率: %s, 分享量: %d, 视频时长: %d秒
                """.formatted(
                video.getId(),
                video.getTitle(),
                video.getConsumeRange(),
                video.getClicks(),
                video.getLikes(),
                video.getComments(),
                video.getCompletionRate(),
                video.getShares(),
                video.getDuration()
        );
    }
}
