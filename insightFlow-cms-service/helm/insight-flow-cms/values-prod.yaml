podAnnotations:
  fluentd.active: "true"
  fluentd.index: insight-flow-cms-log
  fluentd.regexp: '^(?<count>[0-9]*): (?<logtime>.*$)'
  fluentd.timeformat: '%Y_%m_%d %H:%M:%S'

image:
  tag: 451478

api:
  replicaCount: 1
  persistence:
    enabled: true
    mountPath: "/local/downloads"
    storageClassName: "new-gfs"
    size: 100Gi
  resources:
    requests:
      cpu: 16
      memory: 32G
    limits:
      cpu: 32
      memory: 64G
  config:
    logLevel: debug
  volumes: []
  volumeMounts: []
  extraEnv: []

worker:
  persistence:
    pvc:
      enabled: true
      mountPath: "/local/downloads"
      storageClassName: "new-gfs"
      size: 300Gi
  daily:
    replicaCount: 3
    resources:
      requests:
        cpu: 30
        memory: 40G
      limits:
        cpu: 60
        memory: 80G
    config:
      logLevel: debug
      processorsCount: 2
    volumes: []
    volumeMounts: []
    extraEnv: []
  customer:
    replicaCount: 1
    resources:
      requests:
        cpu: 30
        memory: 40G
      limits:
        cpu: 60
        memory: 80G
    config:
      logLevel: debug
      processorsCount: 2
    volumes: []
    volumeMounts: []
    extraEnv: []
  network:
    replicaCount: 2
    resources:
      requests:
        cpu: 2
        memory: 4G
      limits:
        cpu: 4
        memory: 8G
    config:
      logLevel: debug
      processorsCount: 2
    volumes: []
    volumeMounts: []
    extraEnv: []
    affinity:
      nodeAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
          nodeSelectorTerms:
            - matchExpressions:
                - key: kubernetes.io/hostname
                  operator: In
                  values:
                    - nb-paas-k8s48
                    - nb-paas-k8s49
    tolerations:
      - effect: NoSchedule
        key: egress
        operator: Equal
        value: preferred
