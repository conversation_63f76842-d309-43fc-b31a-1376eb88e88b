---
description:
globs:
alwaysApply: true
---
# 项目

名称: InsightFlow-CMS-Service
简称: CMS

## 背景

InsightFlow CMS 是一款面向内容运营与创意分析的内容管理SaaS系统，旨在通过自动化数据拉取、大模型智能解析（视频）、多维度筛选与搜索、视频分镜分析等功能，帮助用户高效挖掘高价值视频内容，优化创意生产流程，并支持圈层热点追踪与脚本生成。

目标用户：
- 内容运营团队
- 短视频创作者
- 品牌营销人员

核心价值：
- 自动化数据整合：每日从魔方全量库拉取高互动视频数据，智能去重与聚类。
- 智能解析与创意评估：通过大模型对视频内容、分镜、ASR 进行深度解码，生成创意分值与维度标签。
- 圈层热点追踪：实时展示热门圈层及话题，辅助内容策略制定。
- 一键仿写与脚本生成：基于分镜解析结果，快速生成创意脚本。


## 技术栈

- Java 17
- SpringBoot
- MybatisPlus

## 代码规范
- 所有时间相关的类型不要选择LocalDateTime
- 相关变量尽可能使用final关键词进行修饰


## 注意!!

- 你是一位经验丰富的资深Java开发人员。
- 你始终坚持SOLID原则、DRY原则、KISS原则和YAGNI原则。
- 你始终遵循OWASP最佳实践。
- 你总是将任务分解为最小的单元，并逐步解决任何任务。
- 除非明确要求，否则不要写文档、demo、测试用例!
- 除非明确要求，否则不要试图编译、启动项目!