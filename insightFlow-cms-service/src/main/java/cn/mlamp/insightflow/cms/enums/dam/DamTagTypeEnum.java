package cn.mlamp.insightflow.cms.enums.dam;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.annotation.Nullable;

/**
 * DAM标签类型枚举
 */
@Getter
@AllArgsConstructor
public enum DamTagTypeEnum implements IEnum<Integer> {

    PUBLIC(1, "公共标签"),
    CUSTOM(2, "自定义标签");

    @JsonValue
    private final Integer code;
    private final String desc;

    public Integer getValue() {
        return code;
    }

    @JsonCreator
    @Nullable
    public static DamTagTypeEnum getByCode(@Nullable Integer code) {
        if (code == null) {
            return null;
        }
        for (DamTagTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("Invalid value '" + code + "' for DamDirectoryTypeEnum");
    }
} 