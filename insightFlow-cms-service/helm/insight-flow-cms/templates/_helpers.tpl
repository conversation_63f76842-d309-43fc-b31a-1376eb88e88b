{{/*
Expand the name of the chart.
*/}}
{{- define "insight-flow-cms.name" }}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "insight-flow-cms.fullname" }}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{- define "insight-flow-cms.api.fullname" -}}
{{ template "insight-flow-cms.fullname" . }}-api
{{- end }}

{{- define "insight-flow-cms.worker.fullname" -}}
{{ template "insight-flow-cms.fullname" . }}-worker
{{- end }}

{{- define "insight-flow-cms.worker.daily.fullname" -}}
{{ template "insight-flow-cms.worker.fullname" . }}-daily
{{- end }}

{{- define "insight-flow-cms.worker.customer.fullname" -}}
{{ template "insight-flow-cms.worker.fullname" . }}-customer
{{- end }}

{{- define "insight-flow-cms.worker.network.fullname" -}}
{{ template "insight-flow-cms.worker.fullname" . }}-network
{{- end }}

{{- define "insight-flow-cms.service.name" -}}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- printf "%s-api-%s-srv" $name .Release.Name | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "insight-flow-cms.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "insight-flow-cms.labels" -}}
helm.sh/chart: {{ include "insight-flow-cms.chart" . }}
{{ include "insight-flow-cms.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "insight-flow-cms.selectorLabels" -}}
app.kubernetes.io/name: {{ include "insight-flow-cms.fullname" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "insight-flow-cms.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "insight-flow-cms.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{- define "insight-flow-cms.worker.env" -}}
- name: MODE
  value: worker
- name: APP_ENV
  value: {{ .Release.Name }}
- name: SPRING_PROFILES_ACTIVE
  value: {{ .Release.Name }}
{{ include "insight-flow-cms.worker.download-path" . }}
{{- end }}

{{- define "insight-flow-cms.worker.download-path" -}}
{{- if .Values.worker.persistence.s3fs.enabled }}
- name: download_path
  value: {{ .Values.worker.persistence.s3fs.mountPath }}
{{- else if .Values.worker.persistence.pvc.enabled }}
- name: download_path
  value: {{ .Values.worker.persistence.pvc.mountPath }}
{{- else }}
- name: download_path
  value: /tmp
{{- end }}
{{- end }}

{{- define "insight-flow-cms.s3fs-prestop" -}}
echo "正在停止s3fs进程..."
# 找到s3fs进程并发送SIGTERM
PID=$(pgrep s3fs)
if [ -n "$PID" ]; then
  echo "发送SIGTERM到进程 $PID"
  kill -TERM $PID
  sleep 5

  # 如果还在运行，发送SIGKILL
  if kill -0 $PID 2>/dev/null; then
    echo "强制终止进程 $PID"
    kill -KILL $PID
  fi
fi

MOUNT_POINT={{ .Values.worker.persistence.s3fs.mountPath }}

# 卸载挂载点 - 使用正确路径
for i in {1..5}; do
  if mountpoint -q $MOUNT_POINT; then
    echo "尝试卸载 $MOUNT_POINT (第${i}次)"
    umount $MOUNT_POINT 2>/dev/null || \
    umount -f $MOUNT_POINT 2>/dev/null || \
    umount -l $MOUNT_POINT 2>/dev/null
    sleep 2
  else
    echo "挂载点已卸载"
    break
  fi
done

# 最后的强制清理
fusermount -u $MOUNT_POINT 2>/dev/null || true
echo "s3fs停止完成"
{{- end }}

{{- define "insight-flow-cms.s3fs-sidercar" -}}
{{- if .Values.worker.persistence.s3fs.enabled }}
- name: s3fs-mount
  image: hub.intra.mlamp.cn/ai-pc/efrecon/s3fs:1.95
  securityContext:
    privileged: true
    capabilities:
      add:
        - SYS_ADMIN
  env:
    - name: AWS_S3_URL
      value: {{ .Values.worker.persistence.s3fs.endpoint }}
    - name: AWS_S3_BUCKET
      value: {{ .Values.worker.persistence.s3fs.bucket }}
    - name: AWS_ACCESS_KEY_ID
      value: {{ .Values.worker.persistence.s3fs.accessKey }}
    - name: AWS_SECRET_ACCESS_KEY
      value: {{ .Values.worker.persistence.s3fs.secretKey }}
  command:
    - /bin/sh
    - -c
    - |
      echo "Mounting S3 bucket..."
      echo "${AWS_ACCESS_KEY_ID}:${AWS_SECRET_ACCESS_KEY}" > /etc/passwd-s3fs
      chmod 600 /etc/passwd-s3fs

      s3fs ${AWS_S3_BUCKET}:{{ .Values.worker.persistence.s3fs.subPath | default "/" }} {{ .Values.worker.persistence.s3fs.mountPath }} \
           -f \
           -o passwd_file=/etc/passwd-s3fs \
           -o url=${AWS_S3_URL} \
           -o use_path_request_style \
           -o allow_other \
           -o use_cache=/tmp/s3cache \
           -o compat_dir \
           -o enable_noobj_cache
  volumeMounts:
    - mountPath: /mnt/s3
      name: s3-storage
      mountPropagation: Bidirectional
  lifecycle:
    preStop:
      exec:
        command:
          - /bin/sh
          - -c
          - |
{{ include "insight-flow-cms.s3fs-prestop" . | indent 12 }}
{{- end }}
{{- end }}

{{- define "insight-flow-cms.worker.volumeMounts" -}}
- name: config
  mountPath: /app/conf.d
- name: logs
  mountPath: /app/logs
{{- if .Values.worker.persistence.pvc.enabled }}
- name: storage
  mountPath: {{ .Values.worker.persistence.pvc.mountPath }}
{{- end }}
{{- if .Values.worker.persistence.s3fs.enabled }}
- name: s3-storage
  mountPath: {{ .Values.worker.persistence.s3fs.mountPath }}
{{- end }}
{{- end }}

{{- define "insight-flow-cms.worker.volumes" -}}
- name: logs
  emptyDir: {}
{{- if .Values.worker.persistence.pvc.enabled }}
- name: storage
  persistentVolumeClaim:
    claimName: {{ include "insight-flow-cms.worker.fullname" . }}-storage
{{- end }}
{{- if .Values.worker.persistence.s3fs.enabled }}
- name: s3-storage
  emptyDir: {}
{{- end }}
{{- end }}}