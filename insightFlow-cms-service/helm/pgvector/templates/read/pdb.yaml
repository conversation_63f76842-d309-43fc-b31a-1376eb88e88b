{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and ( eq .Values.architecture "replication" ) .Values.readReplicas.pdb.create }}
apiVersion: {{ include "common.capabilities.policy.apiVersion" . }}
kind: PodDisruptionBudget
metadata:
  name: {{ include "postgresql.v1.readReplica.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  {{- $labels := include "common.tplvalues.merge" ( dict "values" ( list .Values.readReplicas.labels .Values.commonLabels ) "context" . ) }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" $labels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: read
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  {{- if .Values.readReplicas.pdb.minAvailable }}
  minAvailable: {{ .Values.readReplicas.pdb.minAvailable }}
  {{- end  }}
  {{- if or .Values.readReplicas.pdb.maxUnavailable ( not .Values.readReplicas.pdb.minAvailable ) }}
  maxUnavailable: {{ .Values.readReplicas.pdb.maxUnavailable | default 1 }}
  {{- end  }}
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.readReplicas.podLabels .Values.commonLabels ) "context" . ) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: read
{{- end }}
