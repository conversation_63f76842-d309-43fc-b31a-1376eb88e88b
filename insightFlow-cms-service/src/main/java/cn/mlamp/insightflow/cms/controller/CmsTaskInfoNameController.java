package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.entity.CmsTaskDetail;
import cn.mlamp.insightflow.cms.model.query.CmsTaskInfoNameRequest;
import cn.mlamp.insightflow.cms.model.query.SubjectListRequest;
import cn.mlamp.insightflow.cms.model.vo.SubjectListVO;
import cn.mlamp.insightflow.cms.service.ICmsTaskDetailService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RequestMapping("/taskName")
@RestController
@RequiredArgsConstructor
@Tag(name = "修改视频合成name接口")
public class CmsTaskInfoNameController {

    @Autowired
    private ICmsTaskDetailService cmsTaskDetailService;

    @PostMapping("/update")
    @Operation(summary = "视频合成任务name修改接口")
    public RespBody<?> getSubjectsById(@RequestBody CmsTaskInfoNameRequest cmsTaskInfoNameRequest) {
        cmsTaskDetailService.updateTaskName(cmsTaskInfoNameRequest);
        return RespBody.ok();
    }


}
