package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.model.query.ChangeVideoValueTaskNameRequest;
import cn.mlamp.insightflow.cms.model.query.CreateVideoValueTaskRequest;
import cn.mlamp.insightflow.cms.model.query.DeleteVideoValueTaskRequest;
import cn.mlamp.insightflow.cms.model.vo.GetVideoValueTaskVO;
import cn.mlamp.insightflow.cms.model.vo.ListVideoValueTasksVO;
import cn.mlamp.insightflow.cms.service.VideoValueService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/video-value")
public class VideoValueController {
    private final VideoValueService videoValueService;

    public VideoValueController(VideoValueService videoValueService) {
        this.videoValueService = videoValueService;
    }

    /**
     * 上传视频，生成视频价值任务
     */
    @PostMapping("/tasks")
    public RespBody createTask(@RequestBody CreateVideoValueTaskRequest request) {
        videoValueService.createTask(request);
        return RespBody.ok(Boolean.TRUE);
    }

    /**
     * 修改任务名称
     *
     * @param request
     * @return
     */
    @PutMapping("/tasks/name")
    public RespBody changeName(@RequestBody ChangeVideoValueTaskNameRequest request) {
        videoValueService.changeTaskName(request);
        return RespBody.ok(Boolean.TRUE);
    }

    /**
     * 删除任务
     *
     * @param request
     * @return
     */
    @DeleteMapping("/tasks")
    public RespBody deleteTask(@RequestBody DeleteVideoValueTaskRequest request) {
        videoValueService.deleteTask(request);
        return RespBody.ok(Boolean.TRUE);
    }

    /**
     * 视频价值分页列表
     *
     * @param current
     * @param pageSize
     * @return
     */
    @GetMapping("/list-tasks")
    public RespBody<IPage<ListVideoValueTasksVO>> listTasks(@RequestParam int current,
                                                            @RequestParam int pageSize,
                                                            @RequestParam(value = "listType", defaultValue = "1") Integer listType) {
        Integer userId = UserContext.getUserId();
        IPage<ListVideoValueTasksVO> result = videoValueService.listTasks(current, pageSize, listType, userId);
        return RespBody.ok(result);
    }

    /**
     * 获取视频价值任务详情
     *
     * @param id
     * @return
     */
    @GetMapping("/tasks")
    public RespBody<GetVideoValueTaskVO> getTask(@RequestParam int id) {
        GetVideoValueTaskVO vo = videoValueService.getTask(id);
        return RespBody.ok(vo);
    }

    /**
     * 示例列表
     *
     * @return
     */
    @GetMapping("/list-example-tasks")
    public RespBody<IPage<ListVideoValueTasksVO>> listExampleTasks() {
        IPage<ListVideoValueTasksVO> result = videoValueService.listExampleTasks();
        return RespBody.ok(result);
    }

    /**
     * 示例详情
     *
     * @return
     */
    @GetMapping("/example-tasks")
    public RespBody<GetVideoValueTaskVO> getExampleTask() {
        GetVideoValueTaskVO vo = videoValueService.getExampleTask();
        return RespBody.ok(vo);
    }
}
