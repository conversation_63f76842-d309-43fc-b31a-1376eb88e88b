package cn.mlamp.insightflow.cms.entity.dam;

import lombok.Getter;
import lombok.Setter;
import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;

@Getter
@Setter
@TableName("cms_classify_relation")
public class DamClassifyRelation {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;

    /**
     * 创建者ID
     */
    @TableField(value = "user_id")
    private Integer userId;

    /**
     * 分类id
     */
    @TableField(value = "classify_id")
    private Integer classifyId;

    /**
     * 素材id
     */
    @TableField(value = "asset_id")
    private Integer assetId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 逻辑删除：0-未删除，1-已删除
     */
    @TableField(value = "is_deleted")
    private Integer isDeleted;
}