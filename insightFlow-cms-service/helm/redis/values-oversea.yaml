global:
  password: "HlPE6QfWBQiQsNst"
  imagePullSecrets:
    - tencentcloudcr
  security:
    allowInsecureImages: true

fullnameOverride: redis-sentinel

auth:
  password: "HlPE6QfWBQiQsNst"

image:
  registry: tsg-miaoacms-tcr1.tencentcloudcr.com
  repository: cms/redis
  pullSecret:
    - tencentcloudcr

master:
  persistence:
    enabled: false
  podSecurityContext:
    enabled: false
  containerSecurityContext:
    enabled: false
  serviceAccount:
    create: false
  pdb:
    create: false
  resources:
    requests:
      cpu: 1
      memory: 2Gi
    limits:
      cpu: 2
      memory: 4Gi

replica:
  persistence:
    enabled: false
  podSecurityContext:
    enabled: false
  containerSecurityContext:
    enabled: false
  resources:
    requests:
      cpu: 1
      memory: 2Gi
    limits:
      cpu: 2
      memory: 4Gi

sentinel:
  enabled: true
  replicas: 3
  image:
    registry: tsg-miaoacms-tcr1.tencentcloudcr.com
    repository: cms/redis-sentinel
    pullSecret:
      - tencentcloudcr
  persistence:
    enabled: false
  podSecurityContext:
    enabled: false
  containerSecurityContext:
    enabled: false
  resources:
    requests:
      cpu: "500m"
      memory: "512Mi"
    limits:
      cpu: "1"
      memory: "1Gi"
