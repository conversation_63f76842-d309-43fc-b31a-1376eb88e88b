/*
 *
 *                                  Apache License
 *                            Version 2.0, January 2004
 *                         https://www.apache.org/licenses/
 *
 *    TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION
 *
 *    1. Definitions.
 *
 *       "License" shall mean the terms and conditions for use, reproduction,
 *       and distribution as defined by Sections 1 through 9 of this document.
 *
 *       "Licensor" shall mean the copyright owner or entity authorized by
 *       the copyright owner that is granting the License.
 *
 *       "Legal Entity" shall mean the union of the acting entity and all
 *       other entities that control, are controlled by, or are under common
 *       control with that entity. For the purposes of this definition,
 *       "control" means (i) the power, direct or indirect, to cause the
 *       direction or management of such entity, whether by contract or
 *       otherwise, or (ii) ownership of fifty percent (50%) or more of the
 *       outstanding shares, or (iii) beneficial ownership of such entity.
 *
 *       "You" (or "Your") shall mean an individual or Legal Entity
 *       exercising permissions granted by this License.
 *
 *       "Source" form shall mean the preferred form for making modifications,
 *       including but not limited to software source code, documentation
 *       source, and configuration files.
 *
 *       "Object" form shall mean any form resulting from mechanical
 *       transformation or translation of a Source form, including but
 *       not limited to compiled object code, generated documentation,
 *       and conversions to other media types.
 *
 *       "Work" shall mean the work of authorship, whether in Source or
 *       Object form, made available under the License, as indicated by a
 *       copyright notice that is included in or attached to the work
 *       (an example is provided in the Appendix below).
 *
 *       "Derivative Works" shall mean any work, whether in Source or Object
 *       form, that is based on (or derived from) the Work and for which the
 *       editorial revisions, annotations, elaborations, or other modifications
 *       represent, as a whole, an original work of authorship. For the purposes
 *       of this License, Derivative Works shall not include works that remain
 *       separable from, or merely link (or bind by name) to the interfaces of,
 *       the Work and Derivative Works thereof.
 *
 *       "Contribution" shall mean any work of authorship, including
 *       the original version of the Work and any modifications or additions
 *       to that Work or Derivative Works thereof, that is intentionally
 *       submitted to Licensor for inclusion in the Work by the copyright owner
 *       or by an individual or Legal Entity authorized to submit on behalf of
 *       the copyright owner. For the purposes of this definition, "submitted"
 *       means any form of electronic, verbal, or written communication sent
 *       to the Licensor or its representatives, including but not limited to
 *       communication on electronic mailing lists, source code control systems,
 *       and issue tracking systems that are managed by, or on behalf of, the
 *       Licensor for the purpose of discussing and improving the Work, but
 *       excluding communication that is conspicuously marked or otherwise
 *       designated in writing by the copyright owner as "Not a Contribution."
 *
 *       "Contributor" shall mean Licensor and any individual or Legal Entity
 *       on behalf of whom a Contribution has been received by Licensor and
 *       subsequently incorporated within the Work.
 *
 *    2. Grant of Copyright License. Subject to the terms and conditions of
 *       this License, each Contributor hereby grants to You a perpetual,
 *       worldwide, non-exclusive, no-charge, royalty-free, irrevocable
 *       copyright license to reproduce, prepare Derivative Works of,
 *       publicly display, publicly perform, sublicense, and distribute the
 *       Work and such Derivative Works in Source or Object form.
 *
 *    3. Grant of Patent License. Subject to the terms and conditions of
 *       this License, each Contributor hereby grants to You a perpetual,
 *       worldwide, non-exclusive, no-charge, royalty-free, irrevocable
 *       (except as stated in this section) patent license to make, have made,
 *       use, offer to sell, sell, import, and otherwise transfer the Work,
 *       where such license applies only to those patent claims licensable
 *       by such Contributor that are necessarily infringed by their
 *       Contribution(s) alone or by combination of their Contribution(s)
 *       with the Work to which such Contribution(s) was submitted. If You
 *       institute patent litigation against any entity (including a
 *       cross-claim or counterclaim in a lawsuit) alleging that the Work
 *       or a Contribution incorporated within the Work constitutes direct
 *       or contributory patent infringement, then any patent licenses
 *       granted to You under this License for that Work shall terminate
 *       as of the date such litigation is filed.
 *
 *    4. Redistribution. You may reproduce and distribute copies of the
 *       Work or Derivative Works thereof in any medium, with or without
 *       modifications, and in Source or Object form, provided that You
 *       meet the following conditions:
 *
 *       (a) You must give any other recipients of the Work or
 *           Derivative Works a copy of this License; and
 *
 *       (b) You must cause any modified files to carry prominent notices
 *           stating that You changed the files; and
 *
 *       (c) You must retain, in the Source form of any Derivative Works
 *           that You distribute, all copyright, patent, trademark, and
 *           attribution notices from the Source form of the Work,
 *           excluding those notices that do not pertain to any part of
 *           the Derivative Works; and
 *
 *       (d) If the Work includes a "NOTICE" text file as part of its
 *           distribution, then any Derivative Works that You distribute must
 *           include a readable copy of the attribution notices contained
 *           within such NOTICE file, excluding those notices that do not
 *           pertain to any part of the Derivative Works, in at least one
 *           of the following places: within a NOTICE text file distributed
 *           as part of the Derivative Works; within the Source form or
 *           documentation, if provided along with the Derivative Works; or,
 *           within a display generated by the Derivative Works, if and
 *           wherever such third-party notices normally appear. The contents
 *           of the NOTICE file are for informational purposes only and
 *           do not modify the License. You may add Your own attribution
 *           notices within Derivative Works that You distribute, alongside
 *           or as an addendum to the NOTICE text from the Work, provided
 *           that such additional attribution notices cannot be construed
 *           as modifying the License.
 *
 *       You may add Your own copyright statement to Your modifications and
 *       may provide additional or different license terms and conditions
 *       for use, reproduction, or distribution of Your modifications, or
 *       for any such Derivative Works as a whole, provided Your use,
 *       reproduction, and distribution of the Work otherwise complies with
 *       the conditions stated in this License.
 *
 *    5. Submission of Contributions. Unless You explicitly state otherwise,
 *       any Contribution intentionally submitted for inclusion in the Work
 *       by You to the Licensor shall be under the terms and conditions of
 *       this License, without any additional terms or conditions.
 *       Notwithstanding the above, nothing herein shall supersede or modify
 *       the terms of any separate license agreement you may have executed
 *       with Licensor regarding such Contributions.
 *
 *    6. Trademarks. This License does not grant permission to use the trade
 *       names, trademarks, service marks, or product names of the Licensor,
 *       except as required for reasonable and customary use in describing the
 *       origin of the Work and reproducing the content of the NOTICE file.
 *
 *    7. Disclaimer of Warranty. Unless required by applicable law or
 *       agreed to in writing, Licensor provides the Work (and each
 *       Contributor provides its Contributions) on an "AS IS" BASIS,
 *       WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
 *       implied, including, without limitation, any warranties or conditions
 *       of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
 *       PARTICULAR PURPOSE. You are solely responsible for determining the
 *       appropriateness of using or redistributing the Work and assume any
 *       risks associated with Your exercise of permissions under this License.
 *
 *    8. Limitation of Liability. In no event and under no legal theory,
 *       whether in tort (including negligence), contract, or otherwise,
 *       unless required by applicable law (such as deliberate and grossly
 *       negligent acts) or agreed to in writing, shall any Contributor be
 *       liable to You for damages, including any direct, indirect, special,
 *       incidental, or consequential damages of any character arising as a
 *       result of this License or out of the use or inability to use the
 *       Work (including but not limited to damages for loss of goodwill,
 *       work stoppage, computer failure or malfunction, or any and all
 *       other commercial damages or losses), even if such Contributor
 *       has been advised of the possibility of such damages.
 *
 *    9. Accepting Warranty or Additional Liability. While redistributing
 *       the Work or Derivative Works thereof, You may choose to offer,
 *       and charge a fee for, acceptance of support, warranty, indemnity,
 *       or other liability obligations and/or rights consistent with this
 *       License. However, in accepting such obligations, You may act only
 *       on Your own behalf and on Your sole responsibility, not on behalf
 *       of any other Contributor, and only if You agree to indemnify,
 *       defend, and hold each Contributor harmless for any liability
 *       incurred by, or claims asserted against, such Contributor by reason
 *       of your accepting any such warranty or additional liability.
 *
 *    END OF TERMS AND CONDITIONS
 *
 *    APPENDIX: How to apply the Apache License to your work.
 *
 *       To apply the Apache License to your work, attach the following
 *       boilerplate notice, with the fields enclosed by brackets "{}"
 *       replaced with your own identifying information. (Don't include
 *       the brackets!)  The text should be enclosed in the appropriate
 *       comment syntax for the file format. We also recommend that a
 *       file or class name and description of purpose be included on the
 *       same "printed page" as the copyright notice for easier
 *       identification within third-party archives.
 *
 *    Copyright 2024 onsamepage.ai
 *
 *    Licensed under the Apache License, Version 2.0 (the "License");
 *    you may not use this file except in compliance with the License.
 *    You may obtain a copy of the License at
 *
 *        https://www.apache.org/licenses/LICENSE-2.0
 *
 *    Unless required by applicable law or agreed to in writing, software
 *    distributed under the License is distributed on an "AS IS" BASIS,
 *    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *    See the License for the specific language governing permissions and
 *    limitations under the License.
 */
package cn.mlamp.insightflow.cms.common.redis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * redis 缓存接口
 */
@Component
@Slf4j
public class RedisService<V> {

    private final RedisTemplate<String, V> redisTemplate;

    /**
     * 默认过期时长，单位：秒
     */
    public static final long DEFAULT_EXPIRE = 60 * 60 * 24;

    /**
     * 不设置过期时长
     */
    public static final long NOT_EXPIRE = -1;

    private final String prefixFormat = "%s*";

    public RedisService(RedisTemplate<String, V> redisTemplate, StringRedisSerializer stringRedisSerializer, RedisSerializer redisSerializer) {
        this.redisTemplate = redisTemplate;
        // 值采用json序列化
        this.redisTemplate.setValueSerializer(redisSerializer);
        //使用StringRedisSerializer来序列化和反序列化redis的key值
        this.redisTemplate.setKeySerializer(stringRedisSerializer);
        this.redisTemplate.afterPropertiesSet();

    }


    /**
     * 判断key值是否存在
     *
     * @param key
     * @return
     */
    public boolean existsKey(String key) {
        return this.redisTemplate.hasKey(key);
    }

    /**
     * 重名名key，如果newKey已经存在，则newKey的原值被覆盖
     *
     * @param oldKey
     * @param newKey
     */
    public void renameKey(String oldKey, String newKey) {
        this.redisTemplate.rename(oldKey, newKey);
    }

    /**
     * newKey不存在时才重命名
     *
     * @param oldKey
     * @param newKey
     * @return 修改成功返回true
     */
    public boolean renameKeyNotExist(String oldKey, String newKey) {
        return this.redisTemplate.renameIfAbsent(oldKey, newKey);
    }

    /**
     * 删除key
     *
     * @param key
     */
    public void deleteKey(String key) {
        this.redisTemplate.delete(key);
    }

    /**
     * 删除多个key
     *
     * @param keys
     */
    public void deleteKey(String... keys) {
        Set<String> kSet = Stream.of(keys).map(k -> k).collect(Collectors.toSet());
        this.redisTemplate.delete(kSet);
    }

    /**
     * 删除Key的集合
     *
     * @param keys
     */
    public void deleteKey(Collection<String> keys) {
        Set<String> kSet = keys.stream().map(k -> k).collect(Collectors.toSet());
        this.redisTemplate.delete(kSet);
    }

    /**
     * 设置key的生命周期
     *
     * @param key
     * @param time
     * @param timeUnit
     */
    public void expireKey(String key, long time, TimeUnit timeUnit) {
        this.redisTemplate.expire(key, time, timeUnit);
    }

    /**
     * 指定key在指定的日期过期
     *
     * @param key
     * @param date
     */
    public void expireKeyAt(String key, Date date) {
        this.redisTemplate.expireAt(key, date);
    }

    /**
     * 查询key的生命周期
     *
     * @param key
     * @param timeUnit
     * @return
     */
    public long getKeyExpire(String key, TimeUnit timeUnit) {
        return this.redisTemplate.getExpire(key, timeUnit);
    }

    /**
     * 将key设置为永久有效
     *
     * @param key
     */
    public void persistKey(String key) {
        this.redisTemplate.persist(key);
    }

    /**
     * 获取缓存里面的key
     *
     * @param k 缓存的key
     * @return 如果不存在 则返回null
     */
    public V get(String k) {
        if (this.existsKey(k)) {
            return this.redisTemplate.opsForValue().get(k);
        } else {
            return null;
        }
    }


    /**
     * 往缓存里面放数据
     *
     * @param k 缓存的key
     * @param v 缓存的value
     */
    public void put(String k, V v) {
        this.redisTemplate.opsForValue().set(k, v);
    }

    /**
     * 缓存里面添加数据，并设置有效期
     *
     * @param k          缓存key
     * @param v          缓存value
     * @param expireTime 失效时间
     * @param timeUnit   失效时间单位
     */
    public void put(String k, V v, long expireTime, TimeUnit timeUnit) {
        this.redisTemplate.opsForValue().set(k, v, expireTime, timeUnit);
    }


    /**
     * 删除缓存的索引
     *
     * @param ks 缓存的key
     */
    public void delete(String... ks) {
        if (Objects.nonNull(ks)) {
            this.redisTemplate.delete(Arrays.stream(ks).collect(Collectors.toList()));
        }
    }

    /**
     * 获取某个前缀的缓存的key和value
     *
     * @param prefixKey key 的前缀
     * @return 返回的结果
     */
    public Map<String, V> list(String prefixKey) {
        Set<String> keys = this.listKey(prefixKey);
        if (Objects.isNull(keys) || keys.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<String, V> res = new HashMap<>();
        ValueOperations<String, V> operations = redisTemplate.opsForValue();
        for (String key : keys) {
            res.put(key, operations.get(key));
        }
        return res;
    }


    /**
     * 获取某一个前缀的key
     *
     * @param prefixKey key的前缀
     * @return 列表
     */
    public Set<String> listKey(String prefixKey) {
        return this.redisTemplate.keys(String.format(prefixFormat, prefixKey));
    }

    /**
     * 将值添加到列表右端
     *
     * @param key   列表的key
     * @param value 要添加的值
     * @return 添加后列表的长度
     */
    public Long rightPush(String key, V value) {
        return this.redisTemplate.opsForList().rightPush(key, value);
    }

    /**
     * 将值添加到列表左端
     *
     * @param key   列表的key
     * @param value 要添加的值
     * @return 添加后列表的长度
     */
    public Long leftPush(String key, V value) {
        return this.redisTemplate.opsForList().leftPush(key, value);
    }

    /**
     * 获取列表中指定范围的元素
     *
     * @param key   列表的key
     * @param start 开始索引
     * @param end   结束索引
     * @return 指定范围的元素列表
     */
    public List<V> range(String key, long start, long end) {
        return this.redisTemplate.opsForList().range(key, start, end);
    }

    /**
     * 获取列表的长度
     *
     * @param key 列表的key
     * @return 列表的长度
     */
    public Long size(String key) {
        return this.redisTemplate.opsForList().size(key);
    }

    /**
     * 获取列表中指定索引的元素
     *
     * @param key   列表的key
     * @param index 索引
     * @return 指定索引的元素
     */
    public V index(String key, long index) {
        return this.redisTemplate.opsForList().index(key, index);
    }

    /**
     * 获取列表中所有元素
     *
     * @param key 列表的key
     * @return 列表中所有元素
     */
    public List<V> getAll(String key) {
        Long size = this.size(key);
        if (size == null || size == 0) {
            return new ArrayList<>();
        }
        return this.range(key, 0, size - 1);
    }

    /**
     * 向Hash中放入一个键值对
     *
     * @param key     Hash的key
     * @param hashKey 键
     * @param value   值
     */
    public void hashSet(String key, String hashKey, V value) {
        this.redisTemplate.opsForHash().put(key, hashKey, value);
    }

    /**
     * 向Hash中放入多个键值对
     *
     * @param key Hash的key
     * @param map 键值对集合
     */
    public void hashPutAll(String key, Map<String, V> map) {
        this.redisTemplate.opsForHash().putAll(key, map);
    }

    /**
     * 获取Hash中指定键的值
     *
     * @param key     Hash的key
     * @param hashKey 键
     * @return 值
     */
    public Object hashGet(String key, String hashKey) {
        return this.redisTemplate.opsForHash().get(key, hashKey);
    }

    /**
     * 获取Hash中所有键值对
     *
     * @param key Hash的key
     * @return 所有键值对
     */
    public Map<Object, Object> hashGetAll(String key) {
        return this.redisTemplate.opsForHash().entries(key);
    }

    /**
     * 删除Hash中的一个或多个键
     *
     * @param key      Hash的key
     * @param hashKeys 一个或多个键
     * @return 删除的键的数量
     */
    public Long hashDelete(String key, Object... hashKeys) {
        return this.redisTemplate.opsForHash().delete(key, hashKeys);
    }

    /**
     * 判断Hash中是否存在指定的键
     *
     * @param key     Hash的key
     * @param hashKey 键
     * @return 是否存在
     */
    public Boolean hashHasKey(String key, String hashKey) {
        return this.redisTemplate.opsForHash().hasKey(key, hashKey);
    }
}
