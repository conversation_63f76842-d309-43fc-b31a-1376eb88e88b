apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "insight-flow-cms.api.fullname" . }}
  labels:
    {{ include "insight-flow-cms.labels" . | nindent 4 }}
    component: api
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.api.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{ include "insight-flow-cms.selectorLabels" . | nindent 6 }}
      component: api
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{ toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{ include "insight-flow-cms.labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{ toYaml . | nindent 8 }}
        {{- end }}
        component: api
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{ toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{ toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: api
          securityContext:
            {{ toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: MODE
              value: api
            - name: APP_ENV
              value: {{ .Release.Name }}
            - name: SPRING_PROFILES_ACTIVE
              value: {{ .Release.Name }}
            - name: download_path
              value: {{ .Values.api.persistence.mountPath }}
          {{- if .Values.api.extraEnv }}
            {{- toYaml .Values.api.extraEnv | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          livenessProbe:
            {{- toYaml .Values.livenessProbe | nindent 12 }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          resources:
            {{- toYaml .Values.api.resources | nindent 12 }}
          volumeMounts:
            - name: config
              mountPath: /app/conf.d
            - name: logs
              mountPath: /app/logs
            - name: storage
              mountPath: {{ .Values.api.persistence.mountPath }}
          {{- with .Values.api.volumeMounts }}
            {{ toYaml . | nindent 12 }}
          {{- end }}
      volumes:
        - name: config
          configMap:
            name: {{ include "insight-flow-cms.api.fullname" . }}-config
            items:
              - key: application-{{ .Release.Name }}.yml
                path: application-{{ .Release.Name }}.yml
        - name: logs
          emptyDir: {}
        {{- if .Values.api.persistence.enabled }}
        - name: storage
          persistentVolumeClaim:
            claimName: {{ include "insight-flow-cms.api.fullname" . }}-storage
        {{- else }}
        - name: storage
          emptyDir: {}
        {{- end }}
      {{- with .Values.api.volumes }}
        {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{ toYaml . | nindent 8 }}
      {{- end }}
