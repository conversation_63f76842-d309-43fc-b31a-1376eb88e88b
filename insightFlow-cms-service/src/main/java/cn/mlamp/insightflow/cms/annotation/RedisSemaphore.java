package cn.mlamp.insightflow.cms.annotation;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RedisSemaphore {

    /**
     * 信号量key
     */
    String key();

    /**
     * 总许可数
     */
    int permits() default 10;

    /**
     * 需要获取的许可数
     */
    int acquires() default 1;

    /**
     * 等待超时时间
     */
    long timeout() default -1;

    /**
     * 时间单位
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

    /**
     * 获取失败时的处理策略
     */
    FailureStrategy failureStrategy() default FailureStrategy.EXCEPTION;

    /**
     * 自定义失败消息
     */
    String failureMessage() default "系统繁忙，请稍后重试";

    // 失败处理策略枚举
    enum FailureStrategy {
        EXCEPTION,  // 抛出异常
        RETURN_NULL, // 返回null
        RETURN_DEFAULT // 返回默认值
    }

}