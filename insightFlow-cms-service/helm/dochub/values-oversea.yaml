image:
  api:
    repository: tsg-miaoacms-tcr1.tencentcloudcr.com/cms/dochub-api
    tag: 73ffd25b531717857b5b904f6e75f5027230ee83
    # pullPolicy: Always
    pullSecrets:
      - tencentcloudcr
  web:
    repository: tsg-miaoacms-tcr1.tencentcloudcr.com/cms/dochub-web
    tag: 73ffd25b531717857b5b904f6e75f5027230ee83
    pullSecrets:
      - tencentcloudcr
  webMlamp:
    repository: tsg-miaoacms-tcr1.tencentcloudcr.com/cms/dochub-web-mlamp
    tag: v0.6.12.12
    pullSecrets:
      - tencentcloudcr
  sandbox:
    repository: tsg-miaoacms-tcr1.tencentcloudcr.com/cms/dochub-sandbox
    tag: v0.2.4
    pullSecrets:
      - tencentcloudcr
  proxy:
    repository: tsg-miaoacms-tcr1.tencentcloudcr.com/cms/nginx
    tag: latest
    pullSecrets:
      - tencentcloudcr
api:
  replicas: 2
  extraEnv:
    - name: TZ
      value: Asia/Shanghai
    - name: ADMIN_API_KEY
      value: 'Y\^spP(O{1C*Dm-b]DF/V+F\\tQg1e'
    - name: LOG_LEVEL
      value: DEBUG
    - name: DOCHUB_PORT
      value: "5001"
    - name: UPLOAD_FILE_SIZE_LIMIT
      value: "300"
    - name: GUNICORN_TIMEOUT
      value: "600"
    - name: SERVER_WORKER_AMOUNT
      value: "3"
    - name: CAN_REPLACE_LOGO
      value: "true"
    - name: UPLOAD_FILE_WORD_COUNT_LIMIT
      value: "600000"
    - name: UPLOAD_IMAGE_FILE_NUMBER_LIMIT
      value: "40"
    - name: ADMIN_API_KEY_ENABLE
      value: "true"
    - name: OPENAI_BASE_URL
      value: https://openrouter.ai/api/v1
    - name: MODEL_LB_ENABLED
      value: "true"
    - name: OCR_PADDLE_API_URL
      value: "http://ocr-server"
    - name: INDEXING_MAX_SEGMENTATION_TOKENS_LENGTH
      value: "5000"
    - name: CODE_EXECUTION_ENDPOINT
      value: http://dochub-sandbox:8194
    - name: CODE_EXECUTION_API_KEY
      value: "dochub-sandbox"
    - name: CODE_MAX_NUMBER
      value: "9223372036854775807"
    - name: CODE_MIN_NUMBER
      value: "-9223372036854775808"
    - name: CODE_MAX_STRING_LENGTH
      value: "80000"
    - name: TEMPLATE_TRANSFORM_MAX_LENGTH
      value: "80000"
    - name: CODE_MAX_STRING_ARRAY_LENGTH
      value: "30"
    - name: CODE_MAX_OBJECT_ARRAY_LENGTH
      value: "30"
    - name: CODE_MAX_NUMBER_ARRAY_LENGTH
      value: "1000"
    - name: JOB_SCHEDULER_ENABLE
      value: "true"
  url:
    console: https://llm-ops-meowoo.mlamp.cn
    api: https://llm-ops-meowoo.mlamp.cn
    app: https://llm-ops-meowoo.mlamp.cn
#  hostAliases:
#    - ip: *************
#      hostnames:
#        - xinference.mlamp.cn
#        - pypi.mlamp.cn
#    - ip: *************
#      hostnames:
#        - llm.mlamp.cn
  externalModels:
    - claude-opus-4.yaml
    - claude-sonnet-4.yaml
    - claude-3-7-sonnet.yaml
    - claude-3-5-sonnet.yaml
    - gemini-2.0-flash-001.yaml
    - gemini-2.5-flash-preview-04-17.yaml
    - gemini-2.5-pro-preview-05-06.yaml
    - o3.yaml
  resources:
    requests:
      cpu: "2"
      memory: 4Gi
    limits:
      cpu: "4"
      memory: 8Gi
  persistence:
    persistentVolumeClaim:
      storageClass: "cms-cfs"
      size: 50Gi
worker:
  replicas: 2
  extraEnv:
    - name: TZ
      value: Asia/Shanghai
    - name: RESEND_API_KEY
      value: re_JHZZmGU4_PFB9zCA7nX5aw2fS7JkhP8oJ
    - name: ADMIN_API_KEY
      value: 'Y\^spP(O{1C*Dm-b]DF/V+F\\tQg1e'
    - name: LOG_LEVEL
      value: DEBUG
    - name: UPLOAD_FILE_SIZE_LIMIT
      value: "300"
    - name: UPLOAD_IMAGE_FILE_NUMBER_LIMIT
      value: "40"
    - name: GUNICORN_TIMEOUT
      value: "600"
    - name: CELERY_WORKER_AMOUNT
      value: "10"
    - name: UPLOAD_FILE_WORD_COUNT_LIMIT
      value: "600000"
    - name: ADMIN_API_KEY_ENABLE
      value: "true"
    - name: SQLALCHEMY_POOL_PRE_PING
      value: "true"
    - name: OPENAI_BASE_URL
      value: https://openrouter.ai/api/v1
    - name: MODEL_LB_ENABLED
      value: "true"
    - name: OCR_PADDLE_API_URL
      value: "http://ocr-server"
    - name: INDEXING_MAX_SEGMENTATION_TOKENS_LENGTH
      value: "5000"
    - name: CODE_EXECUTION_API_KEY
      value: "dochub-sandbox"
#  hostAliases:
#    - ip: *************
#      hostnames:
#        - xinference.mlamp.cn
#        - pypi.mlamp.cn
#    - ip: *************
#      hostnames:
#        - llm.mlamp.cn
  resources:
    requests:
      cpu: "2"
      memory: 4Gi
    limits:
      cpu: "4"
      memory: 8Gi
sandbox:
  enabled: true
  extraEnv:
    - name: DEBUG
      value: "true"
    - name: API_KEY
      value: dochub-sandbox
  resources:
    requests:
      cpu: "500m"
      memory: 1Gi
    limits:
      cpu: "1"
      memory: 2Gi
proxy:
  log:
    persistence:
      enabled: false
proxyMlamp:
  enabled: false
web:
  replicas: 2
  extraEnv:
    - name: EDITION
      value: SELF_HOSTED
    - name: CONSOLE_API_URL
      value: https://llm-ops-meowoo.mlamp.cn
    - name: APP_API_URL
      value: https://llm-ops-meowoo.mlamp.cn
  resources:
    requests:
      cpu: 500m
      memory: 512Mi
    limits:
      cpu: "1"
      memory: 1024Mi
webMlamp:
  enabled: false
  extraEnv:
    - name: EDITION
      value: SELF_HOSTED
    - name: CONSOLE_API_URL
      value: https://llm-ops-social.mlamp.cn
    - name: APP_API_URL
      value: https://llm-ops-social.mlamp.cn
  resources:
    requests:
      cpu: 500m
      memory: 512Mi
    limits:
      cpu: "1"
      memory: 1024Mi
externalPostgres:
  enabled: true
  address: postgresql-primary.database.svc.cluster.local
  port: 5432
  username: dochub
  password: "La3~Il*j;BwSLBN<"
  dbName: dochub
postgresql:
  enabled: false
  global:
    storageClass: "cms-cfs"
  image:
    registry: hub.intra.mlamp.cn
    repository: mz-llm/pgvector
    tag: v0.5.1
    pullSecrets:
      - tencentcloudcr
  architecture: standalone
  primary:
    # extraEnvVars:
    #   - name: TELEMETRY
    #     value: false
    args:
      - "-c"
      - "max_connections=1000"
      - "-c"
      - "log_min_duration_statement=3000"
    resources:
      requests:
        cpu: "500m"
        memory: 1024Mi
      limits:
        cpu: "1"
        memory: 2048Mi
    persistence:
      storageClass: "cms-cfs"
      size: 50Gi
weaviate:
  enabled: false
  resources:
    requests:
      cpu: 500m
      memory: 512Mi
    limits:
      cpu: "1"
      memory: 1024Mi
  storage:
    size: 50Gi
    storageClassName: "cms-cfs"
pgvector:
  enabled: true
redis:
  enabled: true
  image:
    registry: tsg-miaoacms-tcr1.tencentcloudcr.com
    repository: cms/redis
    tag: 7.0.11-debian-11-r12
    pullSecrets:
      - tencentcloudcr
  architecture: standalone
  commonConfiguration: |
    appendonly no
  master:
    persistence:
      enabled: true
      storageClass: "cms-cfs"
      size: 5Gi
    resources:
      requests:
        cpu: 500m
        memory: 512Mi
      limits:
        cpu: "1"
        memory: 1Gi

service:
  type: NodePort

resources:
  requests:
    cpu: 500m
    memory: 512Mi
  limits:
    cpu: "1"
    memory: 1024Mi
