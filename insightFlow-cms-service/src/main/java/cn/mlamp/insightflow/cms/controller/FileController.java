package cn.mlamp.insightflow.cms.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.mlamp.insightflow.cms.annotation.HasMemberCheck;
import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.common.resp.RespCode;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.entity.CmsVideoInfo;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.model.query.*;
import cn.mlamp.insightflow.cms.model.vo.DocTaskUploadVO;
import cn.mlamp.insightflow.cms.model.vo.LinkFileVO;
import cn.mlamp.insightflow.cms.model.vo.TaskUploadVO;
import cn.mlamp.insightflow.cms.model.vo.VideoTaskDetailVO;
import cn.mlamp.insightflow.cms.queue.RedisTaskQueue;
import cn.mlamp.insightflow.cms.service.FileService;
import cn.mlamp.insightflow.cms.service.IVideoInfoService;
import cn.mlamp.insightflow.cms.task.UrlVideoDownloadCheckTask;
import cn.mlamp.insightflow.cms.task.UserVideoAnalysisQueryTask;
import cn.mlamp.insightflow.cms.util.RateLimitUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping("/file")
@Tag(name = "文件相关接口")
@Slf4j
public class FileController {

    @Resource
    private FileService fileService;

    @Resource
    private IVideoInfoService videoInfoService;

    @Resource
    private UrlVideoDownloadCheckTask urlVideoDownloadCheckTask;

    @Resource
    private UserVideoAnalysisQueryTask userVideoAnalysisQueryTask;

    @Autowired
    private RedisTaskQueue redisTaskQueue;

    @Resource
    private RateLimitUtil rateLimitUtil;

    @PostMapping("/download")
    @Operation(summary = "下载文件")
    @HasMemberCheck
    public RespBody<List<String>> downloadFile(@RequestBody DownloadRequest objOssIds) {
        if (objOssIds == null) {
            throw new BusinessException(RespCode.BAD_REQUEST, "请求体为null");
        }
        List<String> ossIds = objOssIds.getOssIds();
        if (CollectionUtil.isEmpty(ossIds)) {
            return RespBody.ok(Collections.emptyList());
        }

        // 获取第一个文件路径进行限流检查
        String ossId = ossIds.get(0);


        // 根据文件路径类型进行限流检查
        rateLimitUtil.checkDownloadRateLimit(ossId, UserContext.getUserId(), UserContext.getTenantId());

        // 由于限流目前仅支持单个下载
        final List<String> downloadSignatureUrl = List.of(fileService.getDownloadSignatureUrl(ossId));
        return RespBody.ok(downloadSignatureUrl);
    }

    @PostMapping("/upload/presign")
    @Operation(summary = "获取上传文件的url(签名)")
    @HasMemberCheck
    public RespBody<TaskUploadVO> uploadFile(@RequestBody TaskUploadRequest request) {
        if (request == null) {
            throw new BusinessException(RespCode.BAD_REQUEST, "请求体为null");
        }
        final TaskUploadVO TaskUploadVO = fileService.getUploadSignatureUrl(request);
        return RespBody.ok(TaskUploadVO);
    }

    @PostMapping("/upload/presign-v2")
    @Operation(summary = "获取上传文件的url(签名)，没有会员资格检查")
    public RespBody<TaskUploadVO> uploadFileV2(@RequestBody TaskUploadRequest request) {
        return this.uploadFile(request);
    }

    /**
     * 上传网页链接-校验并获取标题
     */
    @PostMapping("/upload/link/check")
    @Operation(summary = "上传网页链接-校验并获取标题")
    public RespBody<List<LinkFileVO>> uploadLink(@Valid @RequestBody LinkUploadRequest request) {
        return RespBody.ok(fileService.uploadLinkCheck(request));
    }

    /**
     * 批量上传任务
     */
    @PostMapping("/upload/batch")
    @Operation(summary = "批量上传视频任务")
    public RespBody<List<Integer>> batchUploadVideo(@Valid @RequestBody BatchUploadRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        request.setUserId(userId);
        request.setTenantId(tenantId);
        var videoInfos = fileService.batchUploadVideo(request);
        for (CmsVideoInfo videoInfo : videoInfos) {
            List<CmsVideoInfo> videoInfo2s = new ArrayList<>();
            videoInfo2s.add(videoInfo);
            redisTaskQueue.decodeUploadVideo(videoInfo2s, request.getSourceType());
        }
        return RespBody.ok(videoInfos.stream().map(CmsVideoInfo::getSourceFileId).toList());
    }

    /**
     * 上传任务历史（分页）
     */
    @GetMapping("/upload/history")
    @Operation(summary = "上传任务历史（分页）")
    public RespBody<Page<DocTaskUploadVO>> uploadHistory(
            @RequestParam(value = "current", defaultValue = "1") Integer current,
            @RequestParam(value = "pageSize", defaultValue = "5") Integer pageSize) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        return RespBody.ok(fileService.uploadHistory(current, pageSize, userId, tenantId));
    }

    /**
     * 右上角任务列表（批量查询任务状态）
     */
    @PostMapping("/upload/status")
    @Operation(summary = "右上角任务列表（批量查询任务状态）")
    public RespBody<List<DocTaskUploadVO>> uploadTaskStatus(@Valid @RequestBody UploadTaskQueryRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        request.setUserId(userId);
        request.setTenantId(tenantId);
        return RespBody.ok(fileService.uploadTaskStatus(request));
    }

    /**
     * 取消上传
     */
    @PostMapping("/upload/cancel/{taskId}")
    @Operation(summary = "取消上传")
    public RespBody<Void> uploadCancel(@PathVariable("taskId") Integer taskId) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        fileService.uploadCancel(taskId, userId, tenantId);
        return RespBody.ok();
    }

    /**
     * 上传重试（智能分镜重试）
     */
    @PostMapping("/upload/retry/{taskId}")
    @Operation(summary = "上传重试")
    public RespBody<Void> uploadRetry(@PathVariable("taskId") Integer taskId) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        fileService.uploadRetry(taskId, userId, tenantId);
        return RespBody.ok();
    }

    /**
     * 智能分镜上传任务删除
     */
    @DeleteMapping("/upload/delete/{taskId}")
    @Operation(summary = "删除分镜上传任务")
    @Deprecated // 已废弃，分享的视频删除会有问题
    public RespBody<Void> uploadDelete(@PathVariable("taskId") Integer taskId) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        fileService.uploadTaskDelete(taskId, userId, tenantId);
        return RespBody.ok();
    }

    /**
     * 智能分镜重命名（移动端）
     */
    @PostMapping("/upload/rename")
    @Operation(summary = "智能分镜重命名（移动端）")
    public RespBody<Void> uploadRename(@Valid @RequestBody TaskRenameRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        fileService.renameTask(request, userId, tenantId);
        return RespBody.ok();
    }

    /**
     * 智能分镜列表（移动端）
     */
    @GetMapping("/upload/task/list")
    @Operation(summary = "智能分镜列表（移动端）")
    public RespBody<Page<VideoTaskDetailVO>> getUploadTaskList(
            @RequestParam(value = "current", defaultValue = "1") Integer current,
            @RequestParam(value = "pageSize", defaultValue = "5") Integer pageSize) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        return RespBody.ok(videoInfoService.uploadTaskList(current, pageSize, userId, tenantId));
    }

    /**
     * 智能分镜列表（移动端）
     */
    @GetMapping("/upload/task/template/list")
    @Operation(summary = "智能分镜-示例列表（移动端）")
    public RespBody<Page<VideoTaskDetailVO>> getUploadTaskTemplateList(
            @RequestParam(value = "current", defaultValue = "1") Integer current,
            @RequestParam(value = "pageSize", defaultValue = "5") Integer pageSize) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        return RespBody.ok(videoInfoService.uploadTaskTemplateList(current, pageSize, userId, tenantId));
    }

    /**
     * 查询用户url上传视频的下载结果情况，如果成功就发起解析请求
     */
    @GetMapping("/urlVideoDownloadCheck")
    @Operation(summary = "查询用户url上传视频的下载结果情况，如果成功就发起解析请求）")
    public RespBody<?> urlVideoDownloadCheck() {
        urlVideoDownloadCheckTask.urlVideoDownloadCheck();
        return RespBody.ok();
    }

    /**
     * 查询用户视频分析结果
     */
    @GetMapping("/documentAnalysisJobQuery")
    @Operation(summary = "查询用户视频分析结果")
    public RespBody<?> documentAnalysisJobQuery() {
        userVideoAnalysisQueryTask.documentAnalysisJobQuery();
        return RespBody.ok();
    }

}
