package cn.mlamp.insightflow.cms.enums;

import java.util.Arrays;
import java.util.Optional;

public enum VoiceTypeEnum {
    // 多情感
    BEIJING_XIAOYE("zh_male_beijingxiaoye_emo_v2_mars_bigtts", "北京小爷（多情感）"),
    ROUMEI_NVYOU("zh_female_roumeinvyou_emo_v2_mars_bigtts", "柔美女友（多情感）"),
    YANGGUANG_QINGNIAN("zh_male_yangguangqingnian_emo_v2_mars_bigtts", "阳光青年（多情感）"),
    MEILIN_NVYOUDUOQINGGAN("zh_female_meilinvyou_emo_v2_mars_bigtts", "魅力女友（多情感）"),
    SHUANGKUAI_SISI("zh_female_shuangkuaisisi_emo_v2_mars_bigtts", "爽快思思（多情感）"),

    // 客服场景
    KEFU_NVSHENG("zh_female_kefunvsheng_mars_bigtts", "暖阳女声"),
    TIANMEI_TAOZI("zh_female_tianmeitaozi_mars_bigtts", "甜美桃子"),
    CANCAN("zh_female_cancan_mars_bigtts", "灿灿/Shiny"),
    QINGXIN_NVSHENG("zh_female_qingxinnvsheng_mars_bigtts", "清新女声"),
    SKYE("zh_female_shuangkuaisisi_moon_bigtts", "爽快思思/Skye"),
    ALVIN("zh_male_wennuanahu_moon_bigtts", "温暖阿虎/Alvin"),
    BRAYAN("zh_male_shaonianzixin_moon_bigtts", "少年梓辛/Brayan"),
    ZHIXING_NVSHENG("zh_female_zhixingnvsheng_mars_bigtts", "知性女声"),
    QINGSHUANG_NANDA("zh_male_qingshuangnanda_mars_bigtts", "清爽男大"),
    LINJIA_NVHAI("zh_female_linjianvhai_moon_bigtts", "邻家女孩"),
    YUANBO_XIAOSHU("zh_male_yuanboxiaoshu_moon_bigtts", "渊博小叔"),

    // 通用场景
    WENNAN_AHU("zh_male_wennuanahu_mars_bigtts", "温暖阿虎"),
    SUNWUKONG("zh_male_sunwukong_mars_bigtts", "猴哥"),
    XIONGER("zh_male_xionger_mars_bigtts", "熊二"),
    PEIQI("zh_female_peiqi_mars_bigtts", "佩奇猪"),
    WUZETIAN("zh_female_wuzetian_mars_bigtts", "武则天"),
    GUJIE("zh_female_gujie_mars_bigtts", "顾姐"),
    YINGTAO_Wanzi("zh_female_yingtaowanzi_mars_bigtts", "樱桃丸子"),
    AD_JIESHUO("zh_male_chunhui_mars_bigtts", "广告解说"),
    SHAOER_GUSHI("zh_female_shaoergushi_mars_bigtts", "少儿故事"),
    SILANG("zh_male_silang_mars_bigtts", "四郎"),
    JIESHUO_MALE("zh_male_jieshuonansheng_mars_bigtts", "磁性解说男声/Morgan"),
    JITANG_MEIMEI("zh_female_jitangmeimei_mars_bigtts", "鸡汤妹妹/Hope"),
    TIEXIN_FEMALE("zh_female_tiexinnvsheng_mars_bigtts", "贴心女声/Candy"),
    QIAOPIN_FEMALE("zh_female_qiaopinvsheng_mars_bigtts", "俏皮女声"),
    CUTEY("zh_female_mengyatou_mars_bigtts", "萌丫头/Cutey"),
    LANXIAOYANG("zh_male_lanxiaoyang_mars_bigtts", "懒音绵宝"),
    DONGMAN_HAIMIAN("zh_male_dongmanhaimian_mars_bigtts", "亮嗓萌仔"),

    // 角色扮演
    NANQI_MENGWA("zh_male_naiqimengwa_mars_bigtts", "奶气萌娃"),
    POPPO("zh_female_popo_mars_bigtts", "婆婆"),
    GAOLENG_YUJIE("zh_female_gaolengyujie_moon_bigtts", "高冷御姐"),
    AOJIAO_BAZONG("zh_male_aojiaobazong_moon_bigtts", "傲娇霸总"),
    MEILIN_NVYOU("zh_female_meilinvyou_moon_bigtts", "魅力女友"),
    SHENYE_BOKER("zh_male_shenyeboke_moon_bigtts", "深夜播客"),
    SAJIAO_NVYOU("zh_female_sajiaonvyou_moon_bigtts", "柔美女友"),
    SAJIAO_XUEMEI("zh_female_yuanqinvyou_moon_bigtts", "撒娇学妹"),

    // 趣味口音
    JINGQIANG_KANYE("zh_male_jingqiangkanye_moon_bigtts", "京腔侃爷/Harmony"),
    WANWAN_XIAOHE("zh_female_wanwanxiaohe_moon_bigtts", "湾湾小何"),
    WANQU_DASHU("zh_female_wanqudashu_moon_bigtts", "湾区大叔"),
    DAIMENG_CHUANMEI("zh_female_daimengchuanmei_moon_bigtts", "呆萌川妹"),
    GUOZHOU_DEGE("zh_male_guozhoudege_moon_bigtts", "广州德哥"),
    BEIJING_XIAOYE_MOON("zh_male_beijingxiaoye_moon_bigtts", "北京小爷"),
    HAOYU_XIAOGE("zh_male_haoyuxiaoge_moon_bigtts", "浩宇小哥"),
    GUANGXI_YUANZHOU("zh_male_guangxiyuanzhou_moon_bigtts", "广西远舟"),
    MEITUO_JIEER("zh_female_meituojieer_moon_bigtts", "妹坨洁儿"),
    YUZHOU_ZIXUAN("zh_male_yuzhouzixuan_moon_bigtts", "豫州子轩"),

    // 视频配音
    HEAI_NAINAI("ICL_zh_female_heainainai_tob", "和蔼奶奶"),
    LINJU_AYI("ICL_zh_female_linjuayi_tob", "邻居阿姨"),
    WENROU_XIAOYA("zh_female_wenrouxiaoya_moon_bigtts", "温柔小雅"),
    TIANTAI_TONGSHENG("zh_male_tiancaitongsheng_mars_bigtts", "天才童声"),
    CHUNHUI_JIESHUO("zh_male_chunhui_mars_bigtts", "广告解说"),
    XUANYI_JIESHUO("zh_male_changtianyi_mars_bigtts", "悬疑解说"),
    RUYA_QINGNIAN("zh_male_ruyaqingnian_mars_bigtts", "儒雅青年"),
    BAQI_QINGSHU("zh_male_baqiqingshu_mars_bigtts", "霸气青叔"),
    QINGCANG("zh_male_qingcang_mars_bigtts", "擎苍"),
    YANGGUANG_QINGNIANHUOLI("zh_male_yangguangqingnian_mars_bigtts", "活力小哥"),

    // 古风相关
    GUFENG_SHA_YU("zh_female_gufengshaoyu_mars_bigtts", "古风少御"),
    WENROU_SHUNV("zh_female_wenroushunv_mars_bigtts", "温柔淑女"),

    // 其他
    FANJUAN_QINGNIAN("zh_male_fanjuanqingnian_mars_bigtts", "反卷青年"),

    SUNNY_YOUTH("zh_male_yangguangqingnian_moon_bigtts", "阳光青年"),
    SWEET_XIAOYUAN("zh_female_tianmeixiaoyuan_moon_bigtts", "甜美小源"),
    CLEAR_ZIZI("zh_female_qingchezizi_moon_bigtts", "清澈梓梓"),
    NARRATOR_XIAOMING("zh_male_jieshuoxiaoming_moon_bigtts", "解说小明"),
    CHEERFUL_SISTER("zh_female_kailangjiejie_moon_bigtts", "开朗姐姐"),
    NEIGHBOR_BOY("zh_male_linjiananhai_moon_bigtts", "邻家男孩"),
    SWEET_YUEYUE("zh_female_tianmeiyueyue_moon_bigtts", "甜美悦悦"),
    INSPIRATIONAL_SOUP("zh_female_xinlingjitang_moon_bigtts", "心灵鸡汤"),
    ELEGANT_GRACE("ICL_zh_female_zhixingwenwan_tob", "知性温婉"),
    WARM_ATTENTIVE("ICL_zh_male_nuanxintitie_tob", "暖心体贴"),
    GENTLE_ELEGANCE("ICL_zh_female_wenrouwenya_tob", "温柔文雅"),
    BREEZY_CHEERFUL("ICL_zh_male_kailangqingkuai_tob", "开朗轻快"),
    VIBRANT_BRIGHT("ICL_zh_male_huoposhuanglang_tob", "活泼爽朗"),
    FRANK_YOUNG_MAN("ICL_zh_male_shuaizhenxiaohuo_tob", "率真小伙"),
    GENTLE_BROTHER("zh_male_wenrouxiaoge_mars_bigtts", "温柔小哥");

    private final String voiceType;
    private final String description;

    VoiceTypeEnum(String voiceType, String description) {
        this.voiceType = voiceType;
        this.description = description;
    }

    public String getVoiceType() {
        return voiceType;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据中文描述查找对应的 voice_type
     *
     * @param description 中文描述
     * @return 匹配的 voice_type 或 null
     */
    public static String getVoiceTypeByDescription(String description) {
        return Optional.ofNullable(description)
                .flatMap(desc -> java.util.Arrays.stream(values())
                        .filter(vt -> vt.getDescription().equals(desc))
                        .map(VoiceTypeEnum::getVoiceType)
                        .findFirst())
                .orElse(null);
    }

    /**
     * 根据英文 voice_type 查找对应的中文描述
     *
     * @param voiceType 英文标识符（如 zh_female_kefunvsheng_mars_bigtts）
     * @return 匹配的中文描述 或 null
     */
    public static String getDescriptionByVoiceType(String voiceType) {
        return Optional.ofNullable(voiceType)
                .flatMap(vt -> Arrays.stream(values())
                        .filter(enumItem -> enumItem.getVoiceType().equals(vt))
                        .map(VoiceTypeEnum::getDescription)
                        .findFirst())
                .orElse(null);
    }
}
