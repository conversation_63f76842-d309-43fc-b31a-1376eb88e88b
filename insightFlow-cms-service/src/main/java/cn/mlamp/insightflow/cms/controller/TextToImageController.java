package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.model.dto.TextToImageDTO;
import cn.mlamp.insightflow.cms.model.vo.TextToImageVO;
import cn.mlamp.insightflow.cms.service.TextToImageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 文本生图控制器
 */
@Slf4j
@RestController
@RequestMapping("/ai-image")
@AllArgsConstructor
@Tag(name = "火山引擎文本生图", description = "火山引擎文本生图相关接口")
public class TextToImageController {

    @Autowired
    private TextToImageService textToImageService;

    /**
     * 创建文本生图任务（同步接口）
     */
    @PostMapping("/volcano/create")
    @Operation(summary = "创建文本生图任务", description = "使用火山引擎API根据文本描述生成图片，这是一个同步接口，会直接返回生成结果")
    public RespBody<TextToImageVO.TextToImageCreateResponse> createTextToImageTask(
            @RequestBody @Valid TextToImageDTO.TextToImageCreateRequest request) {

        // 获取用户信息
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        // 创建任务并同步生成图片
        TextToImageVO.TextToImageCreateResponse result = textToImageService.createTextToImageTask(request, userId,
                tenantId);

        return RespBody.ok(result);
    }

    /**
     * 创建图生图任务（同步接口）
     */
    @PostMapping("/volcano/image-to-image")
    @Operation(summary = "创建图生图任务", description = "使用火山引擎通用2.0Pro图生图API根据输入图片和文本描述生成新图片，这是一个同步接口，会直接返回生成结果")
    public RespBody<TextToImageVO.ImageToImageCreateResponse> createImageToImageTask(
            @RequestBody @Valid TextToImageDTO.ImageToImageCreateRequest request) {

        // 获取用户信息
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        // 创建任务并同步生成图片
        TextToImageVO.ImageToImageCreateResponse result = textToImageService.createImageToImageTask(request, userId,
                tenantId);

        return RespBody.ok(result);
    }

}
