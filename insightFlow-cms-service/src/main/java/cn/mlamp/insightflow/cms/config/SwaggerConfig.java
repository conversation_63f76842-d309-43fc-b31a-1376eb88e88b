package cn.mlamp.insightflow.cms.config;


import io.swagger.v3.oas.models.ExternalDocumentation;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;


/***
 * 创建Swagger配置
 * @since:knife4j-springdoc-openapi-demo 1.0
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * 2020/03/15 20:40
 */
@Configuration
@Component
//@EnableSwagger2
public class SwaggerConfig {
    /**
     * 根据@Tag 上的排序，写入x-order
     *
     * @return the global open api customizer
     */
//    @Bean
//    public GlobalOpenApiCustomizer orderGlobalOpenApiCustomizer() {
//        return openApi -> {
//            if (openApi.getTags()!=null){
//                openApi.getTags().forEach(tag -> {
//                    Map<String,Object> map=new HashMap<>();
//                    map.put("x-order", RandomUtil.randomInt(0,100));
//                    tag.setExtensions(map);
//                });
//            }
//            if(openApi.getPaths()!=null){
//                openApi.addExtension("x-test123","333");
//                openApi.getPaths().addExtension("x-abb",RandomUtil.randomInt(1,100));
//            }
//
//        };
//    }

//    @Bean
//    public OpenAPI customOpenAPI() {
//        return new OpenAPI()
//                .info(new Info()
//                        .title("XXX用户系统API")
//                        .version("1.0")
//
//                        .description( "Knife4j集成springdoc-openapi示例")
//                        .termsOfService("http://doc.xiaominfo.com")
//                        .license(new License().name("Apache 2.0")
//                                .url("http://doc.xiaominfo.com")));
//    }
//
//    @Bean
//    public Docket createRestApi() {
//        return new Docket(DocumentationType.OAS_30)  // swagger2版本这里是DocumentationType.SWAGGER_2
//                .apiInfo(apiInfo())
//                .select()
//                .apis(RequestHandlerSelectors.withClassAnnotation(RestController.class))
//                .paths(PathSelectors.any())
//                .build();
//    }
//
//    private ApiInfo apiInfo() {
//        return new ApiInfoBuilder()
//                .title("XXXX管理平台")
//                .description("XXXX管理平台 API接口文档")
//                .license("小魏学习啦") //随便写个某公司都行
//                .licenseUrl("www.xiaowei.com") // 可以写个某某网址
//                .version("1.0")
//                .build();
//    }
    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("接口文档标题")
                        .description("SpringBoot3 集成 Swagger3接口文档")
                        .version("v1"))
                .externalDocs(new ExternalDocumentation()
                        .description("项目API文档")
                        .url("/"));
    }


}
