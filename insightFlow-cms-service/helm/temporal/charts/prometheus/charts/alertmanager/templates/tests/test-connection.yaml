{{- if .Values.testFramework.enabled }}
apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "alertmanager.fullname" . }}-test-connection"
  labels:
    {{- include "alertmanager.labels" . | nindent 4 }}
  {{- with .Values.testFramework.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  namespace: {{ include "alertmanager.namespace" . }}
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "alertmanager.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
{{- end }}
