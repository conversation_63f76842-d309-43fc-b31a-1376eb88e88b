{{- if .Values.webMlamp.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "dify.webMlamp.fullname" . }}
  labels:
{{ include "dify.labels" . | indent 4 }}
{{- if .Values.webMlamp.service.labels }}
{{ toYaml .Values.webMlamp.service.labels | indent 4 }}
{{- end }}
    component: "web-mlamp"
{{- with .Values.webMlamp.service.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
{{- end }}
spec:
  type: ClusterIP
  {{- if .Values.webMlamp.service.clusterIP }}
  clusterIP: {{ .Values.webMlamp.service.clusterIP }}
  {{- end }}
  ports:
    - name: web-mlamp
      port: {{ .Values.webMlamp.service.port }}
      protocol: TCP
      targetPort: web-mlamp
  selector:
{{ include "dify.selectorLabels" . | indent 4 }}
    component: "web-mlamp"
{{- end }}