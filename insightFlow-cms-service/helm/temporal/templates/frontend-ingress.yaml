{{- if .Values.server.frontend.ingress.enabled -}}
  {{- if .Capabilities.APIVersions.Has "networking.k8s.io/v1" }}
apiVersion: networking.k8s.io/v1
  {{- else if .Capabilities.APIVersions.Has "networking.k8s.io/v1beta1" }}
apiVersion: networking.k8s.io/v1beta1
  {{- else if .Capabilities.APIVersions.Has "extensions/v1beta1" }}
apiVersion: extensions/v1beta1
  {{- end }}
kind: Ingress
metadata:
  name: {{ include "temporal.componentname" (list $ "frontend") }}
  labels:
    {{- include "temporal.resourceLabels" (list $ "frontend" "") | nindent 4 }}
{{- with .Values.server.frontend.ingress.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
{{- end }}
spec:
  {{- with .Values.server.frontend.ingress.className }}
  ingressClassName: {{ . | quote }}
  {{- end }}
  {{- if .Values.server.frontend.ingress.tls }}
  tls:
    {{- range .Values.server.frontend.ingress.tls }}
    - hosts:
      {{- range .hosts }}
      - {{ . | quote }}
      {{- end }}
      secretName: {{ .secretName }}
      {{- end }}
      {{- end }}
  rules:
    {{- range .Values.server.frontend.ingress.hosts }}
      {{- $url := splitList "/" . }}
      - host: {{ first $url }}
        http:
          paths:
            - path: /{{ rest $url | join "/" }}
              {{- if $.Capabilities.APIVersions.Has "networking.k8s.io/v1" }}
              pathType: Prefix
              backend:
                service:
                  name: {{ include "temporal.fullname" $ }}-frontend
                  port:
                    number: {{ $.Values.server.frontend.service.port }}
              {{- else if $.Capabilities.APIVersions.Has "networking.k8s.io/v1beta1" }}
              backend:
                serviceName: {{ include "temporal.fullname" $ }}-frontend
                servicePort: {{ $.Values.server.frontend.service.port }}
              {{- end }}
    {{- end}}
{{- end }}
