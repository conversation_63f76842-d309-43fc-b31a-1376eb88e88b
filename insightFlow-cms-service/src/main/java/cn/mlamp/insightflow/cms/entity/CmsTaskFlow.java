package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务流表
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_task_flow")
public class CmsTaskFlow extends BaseEntity {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 流程ID
     */
    private Integer flowId;

    /**
     * 父节点ID
     */
    private Integer parentId;

    /**
     * 任务类型
     */
    private Integer taskType;

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 状态：1-进行中，2-成功，3-失败
     */
    private Integer status;

    /**
     * 创建人id
     */
    private Integer userId;

    /**
     * 租户Id
     */
    private Integer tenantId;
}
