package cn.mlamp.insightflow.cms.entity;

import cn.mlamp.insightflow.cms.enums.MemberRightEnum;
import cn.mlamp.insightflow.cms.enums.MemberRightUsageRecordStatusEnum;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("cms_tenant_member_right_usage_record")
public class CmsTenantMemberRightUsageRecord {
    @TableId(type = IdType.AUTO)
    private Integer id ;

    private Integer tenantId;
    private Integer userId;
    /**
     * {@link MemberRightEnum#getRightName()}
     */
    private String rightName;
    private String outUseId;
    private Long rightUsageAmount;
    /**
     * {@link MemberRightUsageRecordStatusEnum#getStatus()}
     */
    private String status;

    // 数据逻辑删除标记 0:未删除 1:已删除
    @TableField(value = "is_deleted")
    private Integer deleted;
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime updateTime;
}