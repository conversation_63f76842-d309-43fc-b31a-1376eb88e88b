package cn.mlamp.insightflow.cms.graph.helper;

import cn.hutool.core.util.StrUtil;
import cn.mlamp.insightflow.cms.graph.config.LangfuseProperties;
import com.langfuse.client.LangfuseClient;
import com.langfuse.client.resources.prompts.PromptsClient;
import com.langfuse.client.resources.prompts.types.Prompt;
import com.langfuse.client.resources.prompts.types.TextPrompt;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@ConditionalOnBean(LangfuseProperties.class)
@Component(PromptHelper.BEAN_NAME)
public class PromptHelper {

    public static final String BEAN_NAME = "promptHelper";
    public static final String GENERATE_CONVERSATION_NAME_PROMPT = "generate_conversation_name_prompt";
    public static final String SUPERVISOR_SYSTEM_PROMPT = "supervisor_system_prompt";
    public static final String SUPERVISOR_DISPATCH_TASK_DESCRIPTION_PROMPT = "supervisor_dispatch_task_description_prompt";
    public static final String SUPERVISOR_DISPATCH_TASK_NEXT_PROMPT = "supervisor_dispatch_task_next_prompt";
    public static final String SUPERVISOR_DISPATCH_TASK_GOAL_PROMPT = "supervisor_dispatch_task_goal_prompt";
    public static final String FEATURED_VIDEOS_SYSTEM_PROMPT = "featured_videos_system_prompt";
    public static final String AI_SCRIPT_SYSTEM_PROMPT = "ai_script_system_prompt";
    // 参数补全相关提示词
    public static final String PARAMETER_EXTRACTOR_SYSTEM_PROMPT = "parameter_extractor_system_prompt";
    public static final String PARAMETER_EXTRACTION_PROMPT = "parameter_extraction_prompt";

    private static final Map<String, String> PROMPT_CACHE = new ConcurrentHashMap<>();
    private static final String[] PROMPT_KEYS = {
            GENERATE_CONVERSATION_NAME_PROMPT,
            SUPERVISOR_SYSTEM_PROMPT,
            SUPERVISOR_DISPATCH_TASK_DESCRIPTION_PROMPT,
            SUPERVISOR_DISPATCH_TASK_NEXT_PROMPT,
            SUPERVISOR_DISPATCH_TASK_GOAL_PROMPT,
            FEATURED_VIDEOS_SYSTEM_PROMPT,
            AI_SCRIPT_SYSTEM_PROMPT,
            PARAMETER_EXTRACTOR_SYSTEM_PROMPT,
            PARAMETER_EXTRACTION_PROMPT,
    };

    @Autowired
    private LangfuseClient langfuseClient;

    /**
     * 获取 prompt 内容
     *
     * @param promptKey prompt 键
     * @return prompt 内容，如果不存在返回空字符串
     */
    public static String getPrompt(String promptKey) {
        final String prompt = PROMPT_CACHE.get(promptKey);
        if (prompt == null) {
            throw new IllegalArgumentException("Prompt not found: " + promptKey);
        }
        return prompt;
    }

    @PostConstruct
    private void init() {
        // 初始化加载所有 prompt
        loadAllPrompts();
    }

    @Scheduled(initialDelay = 2 * 60 * 1000, fixedDelay = 2 * 60 * 1000) // 每5分钟刷新一次
    private void refresh() {
        try {
            log.debug("刷新Prompt配置");
            loadAllPrompts();
            log.debug("刷新Prompt配置完成");
        } catch (Exception e) {
            log.error("刷新Prompt配置失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 初始化加载所有 prompt
     */
    private void loadAllPrompts() {
        for (String promptKey : PROMPT_KEYS) {
            loadPrompt(promptKey);
        }
    }

    /**
     * 加载单个 prompt
     *
     * @param promptKey prompt 键
     */
    private void loadPrompt(String promptKey) {
        final PromptsClient promptsClient = langfuseClient.prompts();
        final Prompt prompt = promptsClient.get(promptKey);

        final String existedPrompt = PROMPT_CACHE.get(promptKey);
        if (prompt.isText()) {
            final TextPrompt textPrompt = prompt.getText().orElseThrow(RuntimeException::new);
            final String newPrompt = textPrompt.getPrompt();
            if (StrUtil.isBlank(newPrompt)) {
                throw new IllegalArgumentException("Prompt内容不能为空: " + promptKey);
            }

            PROMPT_CACHE.put(promptKey, newPrompt);

            if (existedPrompt == null) {
                log.debug("已加载{}, Prompt: {}", promptKey, newPrompt);
            } else if (!existedPrompt.equals(newPrompt)) {
                log.info("Prompt {} 内容已更新，旧内容: {}, 新内容: {}", promptKey, existedPrompt, newPrompt);
            }
        } else {
            throw new UnsupportedOperationException("暂不支持Chat Prompt");
        }
    }

}
