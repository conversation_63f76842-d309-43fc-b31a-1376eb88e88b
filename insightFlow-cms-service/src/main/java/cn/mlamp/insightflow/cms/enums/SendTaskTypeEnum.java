package cn.mlamp.insightflow.cms.enums;

/**
 * @Author: husuper
 * @CreateTime: 2025-06-05
 */
public enum SendTaskTypeEnum {

    //    分镜拆解任务
    SPLIT_TASK("1", "分镜拆解任务"),
    //    脚本生成任务
    SCRIPT_TASK("2", "脚本生成任务"),
    //    视频合成任务
    SYNTHESIS_TASK("3", "视频合成任务"),
    //    素材生成任务
    MATERIAL_TASK("4", "素材生成任务"),
    //    素材上传任务
    MATERIAL_UPLOAD_TASK("5", "素材上传任务"),
    //    视频价值任务
    VALUE_TASK("6", "视频价值任务");

    private final String code;
    private final String msg;

    SendTaskTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }


}
