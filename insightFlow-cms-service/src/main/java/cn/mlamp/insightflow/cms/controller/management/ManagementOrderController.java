package cn.mlamp.insightflow.cms.controller.management;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.model.query.OrderCreationRequest;
import cn.mlamp.insightflow.cms.model.vo.ManagementOrderDetailVO;
import cn.mlamp.insightflow.cms.model.vo.ManagementOrderPageVO;
import cn.mlamp.insightflow.cms.service.management.ManagementOrderService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 管理中心-订单接口
 */
@RestController
@RequestMapping("/management/orders")
@RequiredArgsConstructor
public class ManagementOrderController {

    private final ManagementOrderService managementOrderService;

    @PostMapping
    public RespBody<Void> createOrder(@RequestBody OrderCreationRequest request) {
        managementOrderService.createOrder(request);
        return RespBody.ok();
    }

    @GetMapping("/page")
    public RespBody<IPage<ManagementOrderPageVO>> pageOrders(@RequestParam(required = false) String condition,
                                                             @RequestParam int current, @RequestParam int pageSize) {
        IPage<ManagementOrderPageVO> vos = managementOrderService.pageOrders(condition, current, pageSize);
        return RespBody.ok(vos);
    }

    /**
     * 无效订单
     *
     * @param id
     * @return
     */
    @PutMapping("/invalidation")
    public RespBody<Void> invalidateOrder(@RequestParam int id) {
        managementOrderService.invalidateOrder(id);
        return RespBody.ok();
    }

    /**
     * 订单详情
     *
     * @param id
     * @return
     */
    @GetMapping
    public RespBody<ManagementOrderDetailVO> getInvoiceDetail(@RequestParam int id) {
        ManagementOrderDetailVO vo = managementOrderService.getInvoiceDetail(id);
        return RespBody.ok(vo);
    }
}
