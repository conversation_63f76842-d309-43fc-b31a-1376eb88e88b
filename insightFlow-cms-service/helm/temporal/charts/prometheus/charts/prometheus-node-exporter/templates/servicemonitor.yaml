{{- if .Values.prometheus.monitor.enabled }}
apiVersion: {{ .Values.prometheus.monitor.apiVersion | default "monitoring.coreos.com/v1" }}
kind: ServiceMonitor
metadata:
  name: {{ include "prometheus-node-exporter.fullname" . }}
  namespace: {{ include "prometheus-node-exporter.monitor-namespace" . }}
  labels:
    {{- include "prometheus-node-exporter.labels" . | nindent 4 }}
    {{- with .Values.prometheus.monitor.additionalLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  jobLabel: {{ default "app.kubernetes.io/name" .Values.prometheus.monitor.jobLabel }}
  {{- include "servicemonitor.scrapeLimits" .Values.prometheus.monitor | nindent 2 }}
  {{- with .Values.prometheus.monitor.podTargetLabels }}
  podTargetLabels:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  selector:
    matchLabels:
    {{- with .Values.prometheus.monitor.selectorOverride }}
      {{- toYaml . | nindent 6 }}
    {{- else }}
      {{- include "prometheus-node-exporter.selectorLabels" . | nindent 6 }}
    {{- end }}
  {{- with .Values.prometheus.monitor.attachMetadata }}
  attachMetadata:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  endpoints:
    - port: {{ .Values.service.portName }}
      scheme: {{ .Values.prometheus.monitor.scheme }}
      {{- with .Values.prometheus.monitor.basicAuth }}
      basicAuth:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.prometheus.monitor.bearerTokenFile }}
      bearerTokenFile: {{ . }}
      {{- end }}
      {{- with .Values.prometheus.monitor.tlsConfig }}
      tlsConfig:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.prometheus.monitor.proxyUrl }}
      proxyUrl: {{ . }}
      {{- end }}
      {{- with .Values.prometheus.monitor.interval }}
      interval: {{ . }}
      {{- end }}
      {{- with .Values.prometheus.monitor.scrapeTimeout }}
      scrapeTimeout: {{ . }}
      {{- end }}
      {{- with .Values.prometheus.monitor.relabelings }}
      relabelings:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.prometheus.monitor.metricRelabelings }}
      metricRelabelings:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
