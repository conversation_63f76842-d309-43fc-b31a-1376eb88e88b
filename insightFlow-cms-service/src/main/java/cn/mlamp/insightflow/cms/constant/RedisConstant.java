package cn.mlamp.insightflow.cms.constant;

public class RedisConstant {

    public static final String VIDEO_ASYNC_COMMON_RESULT_QUEUE = "video:async:common:result:queue";

    public static final String VIDEO_ASYNC_USER_RESULT_QUEUE = "video:async:user:result:queue";

    // ai脚本仿写相关
    public static final String AI_SCRIPT_REDIS_KEY_PREFIX = "ai-script:";
    public static final String AI_SCRIPT_SCENE = AI_SCRIPT_REDIS_KEY_PREFIX + "scene:";
    public static final String AI_SCRIPT_IMAGE = AI_SCRIPT_REDIS_KEY_PREFIX + "image:";

    // 会员权益阈值提醒key，第一个占位符填入用户id，第二个占位符填入租户id，第三个占位符填入日期 yyyyMMdd
    public static final String MEMBER_RIGHT_THRESHOLD_ALERT_KEY_FORMAT = "member:right:alert:%d_%d_%s";

    // 订单支付状态检查锁的key
    public static final String ORDER_PAY_STATUS_CHECK_LOCK_KEY = "order_pay_check";

    // 订单支付状态检查锁的timeout，单位秒
    public static final int ORDER_PAY_STATUS_CHECK_LOCK_TIMEOUT = 30;
}