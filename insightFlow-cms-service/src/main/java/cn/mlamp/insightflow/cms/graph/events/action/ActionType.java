package cn.mlamp.insightflow.cms.graph.events.action;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

public enum ActionType {

    DROPDOWN("dropdown"),

    REGENERATE("regenerate"),

    SCENES("scenes"),

    SELLING_POINT("sellingPoint"),

    USER_STORY("userStory"),

    REFRESH("refresh"),

    SET_SELLING_POINTS("setSellingPoints"),

    SELLING_POINTS("sellingPoints"),

    FORM("form"),

    ADVANCE("advance"),

    GOTO("goto"),

    ;

    @JsonValue
    @Getter
    private final String value;

    ActionType(String value) {
        this.value = value;
    }

}
