apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-cli
  labels:
    app: redis-cli
spec:
  selector:
    matchLabels:
      app: redis-cli
  template:
    metadata:
      name: redis-cli
      labels:
        app: redis-cli
    spec:
      imagePullSecrets:
        - name: ai-pc-ai-pc-robot
      containers:
        - name: redis-cli
          image: hub.intra.mlamp.cn/ai-pc/dbcliorg/iredis:1.12.0
          command: ['sh', '-c', 'echo "Redis Cli pod is running" && sleep infinity']
          volumeMounts:
            - name: config
              mountPath: /root/.iredisrc
              subPath: .iredisrc
          resources:
            requests:
              cpu: "250m"
              memory: "256Mi"
            limits:
              cpu: "500m"
              memory: "512Mi"
      volumes:
        - name: config
          configMap:
            name: redis-cli-config
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-cli-config
  labels:
    app: redis-cli
data:
  .iredisrc: |
    [alias_dsn]
    test = redis://:gpqylk3p6l@10.10.100.150:8001/5
    prod = redis://:gpqylk3p6l@10.10.100.150:8001/6