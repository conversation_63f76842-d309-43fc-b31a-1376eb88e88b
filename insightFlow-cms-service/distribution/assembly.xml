<?xml version="1.0" encoding="UTF-8"?>
<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.2.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.2.0 https://maven.apache.org/xsd/assembly-2.2.0.xsd">
    <id>bin</id>
    <formats>
        <format>tar.gz</format>
    </formats>
    <includeBaseDirectory>true</includeBaseDirectory>

    <!-- 复制编译后的classes和resources到boot目录 -->
    <fileSets>
        <!-- 应用类文件和资源 -->
        <fileSet>
            <directory>${project.build.directory}</directory>
            <outputDirectory>boot</outputDirectory>
            <includes>
                <include>${project.artifactId}-${project.version}.jar</include>
            </includes>
        </fileSet>

        <!-- 启动脚本 -->
        <fileSet>
            <directory>distribution/bin</directory>
            <outputDirectory>bin</outputDirectory>
            <fileMode>0755</fileMode>
            <includes>
                <include>run.sh</include>
            </includes>
        </fileSet>

        <!-- 配置文件 -->
        <fileSet>
            <directory>src/main/resources</directory>
            <outputDirectory>conf</outputDirectory>
            <includes>
                <include>**/*.yaml</include>
                <include>**/*.yml</include>
                <include>**/*.json</include>
                <include>**/*.sql</include>
                <include>logback*.xml</include>
                <include>template/**/*</include>
                <include>db/**/*</include>
            </includes>
        </fileSet>

        <!-- README文档 -->
        <fileSet>
            <directory>distribution</directory>
            <outputDirectory>.</outputDirectory>
            <includes>
                <include>README.txt</include>
            </includes>
        </fileSet>

        <!-- 创建logs目录 -->
        <fileSet>
            <directory>distribution/logs</directory>
            <outputDirectory>logs</outputDirectory>
            <excludes>
                <exclude>**/*</exclude>
            </excludes>
        </fileSet>
    </fileSets>

    <!-- 依赖管理 -->
    <dependencySets>
        <!-- 第三方依赖到lib目录 -->
        <dependencySet>
            <outputDirectory>lib</outputDirectory>
            <useProjectArtifact>false</useProjectArtifact>
            <useTransitiveFiltering>false</useTransitiveFiltering>
            <excludes>
                <exclude>${project.groupId}:${project.artifactId}</exclude>
            </excludes>
        </dependencySet>
    </dependencySets>
</assembly> 