package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.model.dto.TextToVideoDTO;
import cn.mlamp.insightflow.cms.model.vo.TextToVideoVO;
import cn.mlamp.insightflow.cms.service.TextToVideoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 文生视频控制器
 */
@Slf4j
@RestController
@RequestMapping("/text")
@AllArgsConstructor
@Tag(name = "文生视频", description = "文生视频相关接口")
public class TextToVideoController {

    @Autowired
    private TextToVideoService textToVideoService;

    /**
     * 创建文生视频任务
     */
    @PostMapping("/create")
    @Operation(summary = "创建文生视频任务", description = "根据文本提示词生成视频")
    public RespBody<TextToVideoVO.TextToVideoCreateResponse> createTextToVideoTask(
            @Valid @RequestBody TextToVideoDTO.TextToVideoCreateRequest request) {

        log.info("创建文生视频任务，用户ID={}，租户ID={}，请求参数={}", UserContext.getUserId(), UserContext.getTenantId(), request);

        TextToVideoVO.TextToVideoCreateResponse response = textToVideoService.createTextToVideoTask(request,
                UserContext.getUserId(), UserContext.getTenantId());

        // 异步执行视频生成任务
        textToVideoService.generateTextToVideoAsync(request, response.getTaskId(), UserContext.getUserId(),
                UserContext.getTenantId());

        return RespBody.ok(response);
    }
}
