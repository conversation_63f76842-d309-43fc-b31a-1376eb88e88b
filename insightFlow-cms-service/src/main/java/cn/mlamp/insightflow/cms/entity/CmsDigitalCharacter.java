package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数字人角色表
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_digital_character")
public class CmsDigitalCharacter extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 角色名称
     */
    @TableField("name")
    private String name;

    /**
     * 角色图像OSS路径
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 基础属性（JSON格式存储：性别、年龄段、皮肤颜色）
     */
    @TableField("basic_attributes")
    private String basicAttributes;

    /**
     * 角色描述
     */
    @TableField("description")
    private String description;

    /**
     * 创建人ID
     */
    @TableField("creator_id")
    private Integer creatorId;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Integer tenantId;

}
