package cn.mlamp.insightflow.cms.controller;

import cn.hutool.core.date.DateUtil;
import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.entity.SystemMetrics;
import cn.mlamp.insightflow.cms.model.dto.*;
import cn.mlamp.insightflow.cms.service.ISystemMetricsService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Date;

/**
 * 后台系统指标统计表 控制层
 */
@RestController
@Tag(name = "后台系统指标统计")
@RequestMapping("system/metrics")
public class SystemMetricsController {

    @Autowired
    private ISystemMetricsService systemMetricsService;

    /**
     * 开始统计任务接口
     *
     * @param dateStr
     * @return
     */
    @GetMapping("/handle/{dateStr}")
    @Operation(summary = "开始统计任务接口")
    public RespBody<SystemMetrics> handleSystemMetrics(
            @Parameter(description = "日期yyyy-MM-dd", required = true) @PathVariable String dateStr) {
        //日期yyyy-mm-dd转换成date
        Date date = DateUtil.parse(dateStr, "yyyy-MM-dd");
        return RespBody.ok(systemMetricsService.handleSystemMetrics(date));
    }

    /**
     * 获取系统指标接口
     *
     * @param dateStr
     * @return
     */
    @GetMapping("/get/{dateStr}")
    @Operation(summary = "获取每天系统指标接口")
    public RespBody<SystemMetricsDTO> getSystemMetrics(
            @Parameter(description = "日期yyyy-MM-dd", required = true) @PathVariable String dateStr) {
        //日期yyyy-mm-dd转换成date
        Date date = DateUtil.parse(dateStr, "yyyy-MM-dd");
        return RespBody.ok(systemMetricsService.getSystemMetrics(date));
    }


    /**
     * 推送每周系统指标接口-每周
     *
     * @param dateStr
     * @return
     */
    @GetMapping("/get/week/{dateStr}")
    @Operation(summary = "推送每周系统指标接口")
    public RespBody<SystemWeeklyReportDTO> getSystemMetricsWeekReport(
            @Parameter(description = "日期yyyy-MM-dd", required = true) @PathVariable String dateStr) throws IOException {
        //日期yyyy-mm-dd转换成date
        Date date = DateUtil.parse(dateStr, "yyyy-MM-dd");
        SystemWeeklyReportDTO systemWeeklyReportDTO = systemMetricsService.getSystemMetricsWeekReport(date);
        systemMetricsService.sendWeekReport(date);
        return RespBody.ok(systemWeeklyReportDTO);
    }


    /**
     * 推送每天系统指标接口-每天
     *
     * @param dateStr
     * @return
     */
    @GetMapping("/get/day/{dateStr}")
    @Operation(summary = "推送每天系统指标接口")
    public RespBody<SystemMetricsDTO> getSystemMetricsDayReport(
            @Parameter(description = "日期yyyy-MM-dd", required = true) @PathVariable String dateStr) throws IOException {
        //日期yyyy-mm-dd转换成date
        Date date = DateUtil.parse(dateStr, "yyyy-MM-dd");
        SystemMetricsDTO systemMetricsDTO = systemMetricsService.getSystemMetrics(DateUtil.offsetDay(date, -1));
        systemMetricsService.sendDayReport(date);
        return RespBody.ok(systemMetricsDTO);
    }

}