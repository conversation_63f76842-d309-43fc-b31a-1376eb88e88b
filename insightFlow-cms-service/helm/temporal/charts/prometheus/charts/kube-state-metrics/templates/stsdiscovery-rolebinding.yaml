{{- if and .Values.autosharding.enabled .Values.rbac.create -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: stsdiscovery-{{ template "kube-state-metrics.fullname" . }}
  namespace: {{ template "kube-state-metrics.namespace" . }}
  labels:
    {{- include "kube-state-metrics.labels" . | indent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: stsdiscovery-{{ template "kube-state-metrics.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ template "kube-state-metrics.serviceAccountName" . }}
    namespace: {{ template "kube-state-metrics.namespace" . }}
{{- end }}
