apiVersion: apps/v1
kind: Deployment
metadata:
  name: busybox
  labels:
    app: busybox
spec:
  replicas: 1
  selector:
    matchLabels:
      app: busybox
  template:
    metadata:
      labels:
        app: busybox
    spec:
      containers:
        - name: busybox
          image: busybox:latest
          command: ["sh", "-c", "while true; do sleep 10; done"]
          resources:
            requests:
              cpu: "256m"
              memory: "256Mi"
            limits:
              cpu: "500m"
              memory: "512Mi"