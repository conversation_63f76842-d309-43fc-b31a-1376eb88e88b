{{- if and .Values.proxyMlamp.enabled}}
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
{{ include "dify.ud.annotations" . | indent 4 }}
    descriptions: nginx mlamp proxy
  labels:
{{- include "dify.labels" . | nindent 4 }}
    component: proxy-mlamp
    # app: {{ template "dify.nginx.fullname" . }}
{{ include "dify.ud.labels" . | indent 4 }}
  name: {{ template "dify.nginxMlamp.fullname" . }}
spec:
  replicas: {{ .Values.proxyMlamp.replicas }}
  selector:
    matchLabels:
{{- include "dify.selectorLabels" . | nindent 6 }}
      component: proxy-mlamp
      {{/*
      # Required labels for istio
      # app: {{ template "dify.nginxMlamp.fullname" . }}
      # version: {{ (print "v" .Values.serviceMesh.version) | quote }}
      */}}
  template:
    metadata:
      annotations:
{{ include "dify.ud.annotations" . | indent 8 }}
      labels:
{{- include "dify.selectorLabels" . | nindent 8 }}
        component: proxy-mlamp
        {{/*
        # Required labels for istio
        # app: {{ template "dify.nginxMlamp.fullname" . }}
        # version: {{ (print "v" .Values.serviceMesh.version) | quote }}
        */}}
{{ include "dify.ud.labels" . | indent 8 }}
    spec:
      {{- if .Values.image.proxyMlamp.pullSecrets }}
      imagePullSecrets:
      {{- range .Values.image.proxyMlamp.pullSecrets }}
        - name: {{ . }}
      {{- end }}
      {{- end }}
      containers:
      - image: "{{ .Values.image.proxyMlamp.repository }}:{{ .Values.image.proxyMlamp.tag }}"
        imagePullPolicy: "{{ .Values.image.proxyMlamp.pullPolicy }}"
        name: nginx
        env:
        {{- if .Values.proxyMlamp.extraEnv }}
          {{- toYaml .Values.proxyMlamp.extraEnv | nindent 8 }}
        {{- end }}
        ports:
          - name: dochub-mlamp
            containerPort: 80
            protocol: TCP
        readinessProbe:
          tcpSocket:
            port: 80
          initialDelaySeconds: 3
          periodSeconds: 30
        livenessProbe:
          tcpSocket:
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 30
        resources:
          {{- toYaml .Values.proxyMlamp.resources | nindent 12 }}
        volumeMounts:
        - name: nginx
          mountPath: /etc/nginx/nginx.conf
          readOnly: true
          subPath: nginx.conf
        - name: nginx
          mountPath: /etc/nginx/proxy.conf
          readOnly: true
          subPath: proxy.conf
        - name: nginx
          mountPath: /etc/nginx/conf.d/default.conf
          readOnly: true
          subPath: default.conf
        {{- if .Values.proxyMlamp.log.persistence.enabled }}
        - name: nginx-logs-disk
          mountPath: {{ .Values.proxyMlamp.log.persistence.mountPath | quote }}
          subPath: {{ .Values.proxyMlamp.log.persistence.persistentVolumeClaim.subPath | default "" }}
        {{- end }}
    {{- if and (.Values.nodeSelector) (not .Values.proxyMlamp.nodeSelector) }}
      nodeSelector:
{{ toYaml .Values.nodeSelector | indent 8 }}
    {{- end }}
    {{- if .Values.proxyMlamp.nodeSelector }}
      nodeSelector:
{{ toYaml .Values.proxyMlamp.nodeSelector | indent 8 }}
    {{- end }}
    {{- if and (.Values.affinity) (not .Values.proxyMlamp.affinity) }}
      affinity:
{{ toYaml .Values.affinity | indent 8 }}
    {{- end }}
    {{- if .Values.proxyMlamp.affinity }}
      affinity:
{{ toYaml .Values.proxyMlamp.affinity | indent 8 }}
    {{- end }}
    {{- if and (.Values.tolerations) (not .Values.proxyMlamp.tolerations) }}
      tolerations:
{{ toYaml .Values.tolerations | indent 8 }}
    {{- end }}
    {{- if .Values.proxyMlamp.tolerations }}
      tolerations:
{{ toYaml .Values.proxyMlamp.tolerations | indent 8 }}
    {{- end }}
      volumes:
      - name: nginx
        configMap:
          defaultMode: 420
          name: {{ template "dify.nginxMlamp.fullname" . }}
      {{- if .Values.proxyMlamp.log.persistence.enabled }}
      - name: nginx-logs-disk
        persistentVolumeClaim:
          claimName: {{ .Values.proxyMlamp.log.persistence.persistentVolumeClaim.existingClaim | default (printf "%s-logs" (include "dify.nginx.fullname" . | trunc 58)) }}
      {{- end }}
{{- end }}