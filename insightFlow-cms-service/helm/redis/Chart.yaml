annotations:
  category: Database
  images: |
    - name: kubectl
      image: docker.io/bitnami/kubectl:1.33.1-debian-12-r6
    - name: os-shell
      image: docker.io/bitnami/os-shell:12-debian-12-r46
    - name: redis
      image: docker.io/bitnami/redis:8.0.2-debian-12-r3
    - name: redis-exporter
      image: docker.io/bitnami/redis-exporter:1.74.0-debian-12-r1
    - name: redis-sentinel
      image: docker.io/bitnami/redis-sentinel:8.0.2-debian-12-r2
  licenses: Apache-2.0
  tanzuCategory: service
apiVersion: v2
appVersion: 8.0.2
dependencies:
- name: common
  repository: oci://registry-1.docker.io/bitnamicharts
  tags:
  - bitnami-common
  version: 2.x.x
description: Redis(R) is an open source, advanced key-value store. It is often referred
  to as a data structure server since keys can contain strings, hashes, lists, sets
  and sorted sets.
home: https://bitnami.com
icon: https://dyltqmyl993wv.cloudfront.net/assets/stacks/redis/img/redis-stack-220x234.png
keywords:
- redis
- keyvalue
- database
maintainers:
- name: Broadcom, Inc. All Rights Reserved.
  url: https://github.com/bitnami/charts
name: redis
sources:
- https://github.com/bitnami/charts/tree/main/bitnami/redis
version: 21.2.4
