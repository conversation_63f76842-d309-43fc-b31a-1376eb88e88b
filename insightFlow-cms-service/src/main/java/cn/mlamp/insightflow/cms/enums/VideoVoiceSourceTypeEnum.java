package cn.mlamp.insightflow.cms.enums;

import lombok.Getter;

@Getter
public enum VideoVoiceSourceTypeEnum {

    SYSTEM(0, "系统"),
    ALIYUN(1, "阿里云生成");

    private final Integer code;
    private final String description;

    VideoVoiceSourceTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }


    /**
     * 根据 code 获取枚举实例
     */
    public static VideoVoiceSourceTypeEnum fromCode(Integer code) {
        for (VideoVoiceSourceTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的 SourceType: " + code);
    }
}
