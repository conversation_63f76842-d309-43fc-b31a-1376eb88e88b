package cn.mlamp.insightflow.cms.enums.dam;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * @Author: husuper
 * @CreateTime: 2025-07-21
 */
@Getter
public enum DamLinkUploadTagTypeEnum implements IEnum<Integer> {

    HAND_MOVEMENT(0, "手动切分"),
    AI_MARK(1, "AI打标");


    @JsonValue
    private final Integer value;
    private final String desc;

    DamLinkUploadTagTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

}
