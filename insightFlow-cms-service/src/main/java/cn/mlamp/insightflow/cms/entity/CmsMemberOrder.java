package cn.mlamp.insightflow.cms.entity;

import cn.mlamp.insightflow.cms.enums.OrderSourceEnum;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("cms_member_order")
public class CmsMemberOrder {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer tenantId;
    private Integer userId;
    private Integer invoiceId;

    private Integer memberId;
    private String memberName;
    private String memberSourceType;
    private Integer memberLevel;
    private String memberLevelName;
    private Integer duration;
    private BigDecimal price;

    private Integer hotStoryboard;
    private Integer gold3sSummary;
    private Integer videoAnalysis;
    private Integer script;
    private Long damCapacity;
    private Integer damAiMark;
    private Long videoDuration;
    private Integer videoSynthesis;
    private Integer videoBgm;
    private Integer tts;

    /**
     * {@link cn.mlamp.insightflow.cms.enums.MemberOrderStatusEnum}
     */
    private String status;
    private String channel;
    private String outTradeNo;
    private LocalDateTime payExpireTime;

    private String seller;
    private LocalDateTime effectTime;
    /**
     * {@link OrderSourceEnum#getSource()}
     */
    private String source;

    // 数据逻辑删除标记 0:未删除 1:已删除
    private Integer isDeleted;
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime updateTime;
}