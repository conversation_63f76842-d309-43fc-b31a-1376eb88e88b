package cn.mlamp.insightflow.cms.controller.analysis;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.model.dto.analysis.SearchRecommendQueryDTO;
import cn.mlamp.insightflow.cms.model.dto.analysis.VideoAnalysisListQueryDTO;
import cn.mlamp.insightflow.cms.model.vo.QianchuanMaterialVideoVO;
import cn.mlamp.insightflow.cms.model.vo.analysis.SearchRecommendVO;
import cn.mlamp.insightflow.cms.service.CmsPullTaskDedupedDataService;
import cn.mlamp.insightflow.cms.service.analysis.IVideoAnalysisService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 视频分析 api
 *
 * <AUTHOR>
 */
@Tag(name = "视频分析接口")
@RestController
@RequestMapping("/analysis")
public class VideoAnalysisController {

    private final IVideoAnalysisService videoAnalysisService;

    private final CmsPullTaskDedupedDataService pullTaskDataService;

    @Autowired
    public VideoAnalysisController(IVideoAnalysisService videoAnalysisService,
                                   CmsPullTaskDedupedDataService pullTaskDataService) {
        this.videoAnalysisService = videoAnalysisService;
        this.pullTaskDataService = pullTaskDataService;
    }

    @Operation(summary = "获取品牌/产品推荐 - 我的爆款")
    @PostMapping("/search-recommendations")
    public RespBody<List<SearchRecommendVO>> getSearchRecommendations(@RequestBody SearchRecommendQueryDTO queryDTO) {
        Integer tenantId = UserContext.getTenantId();
        List<SearchRecommendVO> recommendVOList = pullTaskDataService.getSearchRecommendations(queryDTO, tenantId);
        return RespBody.ok(recommendVOList);
    }

    /**
     * 获取上传拆解结果列表，作为我的爆款展示，展示形式与千川热点视频类似
     *
     * @see cn.mlamp.insightflow.cms.controller.QianchuanVideoHotspotController#list(cn.mlamp.insightflow.cms.model.dto.QianchuanVideoHotspotQueryDTO)
     */
    @Operation(summary = "获取视频拆解列表 - 我的爆款")
    @PostMapping("/list")
    public RespBody<IPage<QianchuanMaterialVideoVO>> list(@RequestBody VideoAnalysisListQueryDTO queryDTO) {
        Integer tenantId = UserContext.getTenantId();
        return RespBody.ok(videoAnalysisService.listVideos(queryDTO, tenantId));
    }

}