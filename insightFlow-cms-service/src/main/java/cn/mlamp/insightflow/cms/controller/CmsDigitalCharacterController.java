package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.common.resp.RespCode;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.model.dto.CmsDigitalCharacterCreateDTO;
import cn.mlamp.insightflow.cms.model.query.CmsDigitalCharacterQueryParam;
import cn.mlamp.insightflow.cms.model.vo.CmsDigitalCharacterPageVO;
import cn.mlamp.insightflow.cms.service.ICmsDigitalCharacterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 数字人角色 Controller
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@RestController
@RequestMapping("/digital-character")
@Tag(name = "数字人角色管理接口")
public class CmsDigitalCharacterController {

    @Autowired
    private ICmsDigitalCharacterService digitalCharacterService;

    /**
     * 创建数字人角色
     */
    @PostMapping
    @Operation(summary = "创建数字人角色", description = "创建一个新的数字人角色，包含图像、基础属性、描述等信息")
    public RespBody<Integer> createCharacter(@RequestBody @Valid CmsDigitalCharacterCreateDTO createDTO,
            HttpServletRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        Integer characterId = digitalCharacterService.createCharacter(createDTO, userId, tenantId);
        return RespBody.ok(characterId);
    }

    /**
     * 批量删除数字人角色（支持单个和多个）
     */
    @PostMapping("/delete")
    @Operation(summary = "批量删除数字人角色", description = "批量删除数字人角色，支持单个和多个角色删除")
    public RespBody<Void> deleteCharacters(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        @SuppressWarnings("unchecked")
        List<Integer> characterIds = (List<Integer>) params.get("characterIds");
        if (CollectionUtils.isEmpty(characterIds)) {
            throw new BusinessException(RespCode.BAD_REQUEST, "角色ID列表不能为空");
        }

        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        boolean result = digitalCharacterService.deleteCharacters(characterIds, userId, tenantId);
        return result ? RespBody.ok() : RespBody.fail("删除数字人角色失败");
    }

    /**
     * 分页获取数字人角色列表
     */
    @PostMapping("/list")
    @Operation(summary = "分页获取数字人角色列表", description = "分页获取数字人角色列表，支持按名称、性别、年龄段、皮肤颜色等条件筛选")
    public RespBody<CmsDigitalCharacterPageVO> getCharacterList(
            @RequestBody @Valid CmsDigitalCharacterQueryParam queryParam, HttpServletRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        CmsDigitalCharacterPageVO result = digitalCharacterService.getCharacterList(queryParam, userId, tenantId);
        return RespBody.ok(result);
    }

}
