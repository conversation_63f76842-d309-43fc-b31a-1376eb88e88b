package cn.mlamp.insightflow.cms.entity.dam;

import cn.mlamp.insightflow.cms.enums.dam.DamTaskStatusEnum;
import cn.mlamp.insightflow.cms.model.vo.dam.DamScene;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * DAM素材上传任务表-记录细节
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Getter
@Setter
@TableName(value = "cms_asset_upload_task_detail", autoResultMap = true)
@Schema(name = "CmsAssetUploadTaskDetail", description = "DAM素材上传任务表-记录细节")
public class DamAssetUploadTaskDetail implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "ai 打标任务 id")
    @TableField("task_id")
    private Integer taskId;

    @Schema(description = "关联素材 id")
    @TableField("asset_id")
    private Integer assetId;

    @Schema(description = "状态：1-待处理，2-处理中，3-完成，4-失败, 5-裁剪中, 6-裁剪完成")
    @TableField("status")
    private DamTaskStatusEnum status;

    @Schema(description = "推荐分镜脚本")
    @TableField(value = "suggestion_scenes", typeHandler = JacksonTypeHandler.class)
    private List<DamScene> suggestionScenes;

    @Schema(description = "素材打标结果")
    @TableField("result")
    private String result;

    @Schema(description = "失败原因")
    @TableField("error")
    private String error;

    @Schema(description = "指标")
    @TableField("metrics")
    private String metrics;

    @Schema(description = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @Schema(description = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @Schema(description = "逻辑删除：0-未删除，1-已删除")
    @TableField("is_deleted")
    private Boolean isDeleted = false;

}
