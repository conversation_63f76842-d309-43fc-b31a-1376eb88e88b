apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "insight-flow-cms.worker.customer.fullname" . }}-config
data:
  application-prod.yml: |-
    spring:
      temporal:
        namespace: cms-prod
        connection:
          target: temporal-cms-frontend.ai-pc:7233
        workers:
          - name: common-worker
            task-queue: common
            capacity:
              max-concurrent-workflow-task-pollers: 1
              max-concurrent-workflow-task-executors: 5
              max-concurrent-activity-task-pollers: 5
              max-concurrent-activity-executors: 15
          - name: customer-worker
            task-queue: customer
            capacity:
              max-concurrent-workflow-task-pollers: 5
              max-concurrent-workflow-task-executors: 5
              max-concurrent-activity-task-pollers: 5
              max-concurrent-activity-executors: 30
              max-concurrent-local-activity-executors: 30
        workflow:
          task-timeout: PT1200S
          run-timeout: PT1200S
          execution-timeout: PT1200S
        workersAutoDiscovery:
          packages:
            - cn.mlamp.insightflow.cms.worker.workflows.customer
    cms:
      task:
        common: true
        scheduler:
          daily:
            batch-size: 5
            max-running-workflow-size: 15
          customer:
            batch-size: 5
            max-running-workflow-size: 10
        semaphore:
          asset-upload-task-asset-upload-sub-workflow:
            permits: 10
  application-oversea.yml: |-
    spring:
      temporal:
        namespace: cms-oversea
        connection:
          target: temporal-frontend.temporal.svc.cluster.local:7233
        workers:
          - name: common-worker
            task-queue: common
            capacity:
              max-concurrent-workflow-task-pollers: 1
              max-concurrent-workflow-task-executors: 5
              max-concurrent-activity-task-pollers: 5
              max-concurrent-activity-executors: 15
          - name: customer-worker
            task-queue: customer
            capacity:
              max-concurrent-workflow-task-pollers: 5
              max-concurrent-workflow-task-executors: 10
              max-concurrent-activity-task-pollers: 5
              max-concurrent-activity-executors: 10
        workflow:
          task-timeout: PT1200S
          run-timeout: PT1200S
          execution-timeout: PT1200S
        workersAutoDiscovery:
          packages:
            - cn.mlamp.insightflow.cms.worker.workflows.customer
    cms:
      task:
        common: true
        scheduler:
          daily:
            batch-size: 5
            max-running-workflow-size: 25
          customer:
            batch-size: 5
            max-running-workflow-size: 10
  application-preview.yml: |-
    spring:
      temporal:
        namespace: cms-test
        connection:
          target: temporal-cms-frontend.ai-pc:7233
        workers:
          - name: common-worker
            task-queue: common
            capacity:
              max-concurrent-workflow-task-pollers: 1
              max-concurrent-workflow-task-executors: 5
              max-concurrent-activity-task-pollers: 5
              max-concurrent-activity-executors: 15
          - name: customer-worker
            task-queue: customer
            capacity:
              max-concurrent-workflow-task-pollers: 5
              max-concurrent-workflow-task-executors: 5
              max-concurrent-activity-task-pollers: 5
              max-concurrent-activity-executors: 20
        workflow:
          task-timeout: PT1200S
          run-timeout: PT1200S
          execution-timeout: PT1200S
        workersAutoDiscovery:
          packages:
            - cn.mlamp.insightflow.cms.worker.workflows.customer
    cms:
      task:
        common: true
        scheduler:
          daily:
            batch-size: 5
            max-running-workflow-size: 25
          customer:
            batch-size: 5
            max-running-workflow-size: 10
  application-test.yml: |-
    spring:
      temporal:
        namespace: cms-test
        connection:
          target: temporal-cms-frontend.ai-pc:7233
        workers:
          - name: common-worker
            task-queue: common
            capacity:
              max-concurrent-workflow-task-pollers: 1
              max-concurrent-workflow-task-executors: 5
              max-concurrent-activity-task-pollers: 5
              max-concurrent-activity-executors: 15
          - name: customer-worker
            task-queue: customer
            capacity:
              max-concurrent-workflow-task-pollers: 5
              max-concurrent-workflow-task-executors: 5
              max-concurrent-activity-task-pollers: 5
              max-concurrent-activity-executors: 30
        workflow:
          task-timeout: PT1200S
          run-timeout: PT1200S
          execution-timeout: PT1200S
        workersAutoDiscovery:
          packages:
            - cn.mlamp.insightflow.cms.worker.workflows.customer
    cms:
      task:
        common: true
        scheduler:
          daily:
            enabled: false
            batch-size: 1
            max-running-workflow-size: 1
          customer:
            enabled: true
            batch-size: 3
            max-running-workflow-size: 10