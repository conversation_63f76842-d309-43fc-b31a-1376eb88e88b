podAnnotations:
  fluentd.active: "true"
  fluentd.index: insight-flow-cms-log-test
  fluentd.regexp: '^(?<count>[0-9]*): (?<logtime>.*$)'
  fluentd.timeformat: '%Y_%m_%d %H:%M:%S'

image:
  tag: 455145

api:
  replicaCount: 1
  persistence:
    enabled: true
    mountPath: "/local/downloads"
    storageClassName: "new-gfs"
    size: 10Gi
  resources:
    requests:
      cpu: 4
      memory: 8G
    limits:
      cpu: 8
      memory: 16G
  config:
    logLevel: debug
  volumes: []
  volumeMounts: []
  extraEnv: []

worker:
  persistence:
    pvc:
      enabled: true
      mountPath: "/local/downloads"
      storageClassName: "new-gfs"
      size: 30Gi
    s3fs:
      enabled: false
      subPath: "/tmp/shared/test"
  daily:
    replicaCount: 0
    resources:
      requests:
        cpu: 4
        memory: 8G
      limits:
        cpu: 8
        memory: 16G
    config:
      logLevel: debug
      processorsCount: 2
    volumes: []
    volumeMounts: []
    extraEnv: []
  customer:
    replicaCount: 0
    resources:
      requests:
        cpu: 4
        memory: 8G
      limits:
        cpu: 8
        memory: 16G
    config:
      logLevel: debug
      processorsCount: 2
    volumes: []
    volumeMounts: []
    extraEnv: []
  network:
    replicaCount: 0
    resources:
      requests:
        cpu: 1
        memory: 2G
      limits:
        cpu: 2
        memory: 4G
    config:
      logLevel: debug
      processorsCount: 2
    volumes: []
    volumeMounts: []
    extraEnv: []
    affinity:
      nodeAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
          nodeSelectorTerms:
            - matchExpressions:
                - key: kubernetes.io/hostname
                  operator: In
                  values:
                    - nb-paas-k8s48
                    - nb-paas-k8s49
    tolerations:
      - effect: NoSchedule
        key: egress
        operator: Equal
        value: preferred