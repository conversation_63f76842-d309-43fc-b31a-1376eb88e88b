{{- if .Values.api.persistence.enabled }}
{{- if .Capabilities.APIVersions.Has "batch/v1/CronJob" }}
apiVersion: batch/v1
{{- else }}
apiVersion: batch/v1beta1
{{- end }}
kind: CronJob
metadata:
  name: {{ include "insight-flow-cms.api.fullname" . }}-cleaner
  labels:
    app: {{ include "insight-flow-cms.api.fullname" . }}-cleaner
spec:
  schedule: "0/30 * * * *" # Every 30 minutes
  jobTemplate:
    metadata:
      name: {{ include "insight-flow-cms.api.fullname" . }}-cleaner
      labels:
        app: {{ include "insight-flow-cms.api.fullname" . }}-cleaner
    spec:
      template:
        metadata:
          name: {{ include "insight-flow-cms.api.fullname" . }}-cleaner
          labels:
            app: {{ include "insight-flow-cms.api.fullname" . }}-cleaner
        spec:
          {{- with .Values.imagePullSecrets }}
          imagePullSecrets:
            {{ toYaml . | nindent 12 }}
          {{- end }}
          restartPolicy: Never
          containers:
            - name: cleaner
              image: "{{ .Values.image.cleaner.repository }}:{{ .Values.image.cleaner.tag }}"
              imagePullPolicy: {{ .Values.image.pullPolicy }}
              command:
                - bash
                - -c
                - |
                  echo "Cleaning up old data..."

                  TARGET_DIR={{- .Values.api.persistence.mountPath }}

                  # Find directories older than 600 minutes
                  OLD_DIRS=$(find "${TARGET_DIR}" -type d -mmin +600 2>/dev/null | sort)
                  
                  # Find directories newer than 600 minutes (to be kept)
                  KEPT_DIRS=$(find "${TARGET_DIR}" -type d -mmin -600 2>/dev/null | sort)
                  
                  echo "Directories to be cleaned (older than 600 minutes):"
                  if [ -n "$OLD_DIRS" ]; then
                    echo "$OLD_DIRS"
                    echo "$OLD_DIRS" | while read -r dir; do
                      if [ -d "$dir" ] && [ "$dir" != "${TARGET_DIR}" ]; then
                        echo "Removing: $dir"
                        rm -rf "$dir" 2>/dev/null || true
                      fi
                    done
                  else
                    echo "No directories to clean"
                  fi
                  
                  echo -e "\nDirectories kept (newer than 600 minutes):"
                  if [ -n "$KEPT_DIRS" ]; then
                    echo "$KEPT_DIRS"
                  else
                    echo "No directories kept"
                  fi

                  echo "Cleanup completed"
              volumeMounts:
                - name: storage
                  mountPath: {{ .Values.api.persistence.mountPath }}
          volumes:
            - name: storage
              persistentVolumeClaim:
                claimName: {{ include "insight-flow-cms.api.fullname" . }}-storage
{{- end }}
---
{{- if .Values.worker.persistence.pvc.enabled }}
{{- if .Capabilities.APIVersions.Has "batch/v1/CronJob" }}
apiVersion: batch/v1
{{- else }}
apiVersion: batch/v1beta1
{{- end }}
kind: CronJob
metadata:
  name: {{ include "insight-flow-cms.worker.fullname" . }}-cleaner
  labels:
    app: {{ include "insight-flow-cms.worker.fullname" . }}-cleaner
spec:
  schedule: "0/30 * * * *" # Every 30 minutes
  jobTemplate:
    metadata:
      name: {{ include "insight-flow-cms.worker.fullname" . }}-cleaner
      labels:
        app: {{ include "insight-flow-cms.worker.fullname" . }}-cleaner
    spec:
      template:
        metadata:
          name: {{ include "insight-flow-cms.worker.fullname" . }}-cleaner
          labels:
            app: {{ include "insight-flow-cms.worker.fullname" . }}-cleaner
        spec:
          {{- with .Values.imagePullSecrets }}
          imagePullSecrets:
            {{ toYaml . | nindent 12 }}
          {{- end }}
          restartPolicy: Never
          containers:
            - name: cleaner
              image: "{{ .Values.image.cleaner.repository }}:{{ .Values.image.cleaner.tag }}"
              imagePullPolicy: {{ .Values.image.pullPolicy }}
              command:
                - bash
                - -c
                - |
                  echo "Cleaning up old data..."

                  TARGET_DIR={{- .Values.worker.persistence.pvc.mountPath }}

                  # Find directories older than 600 minutes
                  OLD_DIRS=$(find "${TARGET_DIR}" -type d -mmin +600 2>/dev/null | sort)
                  
                  # Find directories newer than 600 minutes (to be kept)
                  KEPT_DIRS=$(find "${TARGET_DIR}" -type d -mmin -600 2>/dev/null | sort)
                  
                  echo "Directories to be cleaned (older than 600 minutes):"
                  if [ -n "$OLD_DIRS" ]; then
                    echo "$OLD_DIRS"
                    echo "$OLD_DIRS" | while read -r dir; do
                      if [ -d "$dir" ] && [ "$dir" != "${TARGET_DIR}" ]; then
                        echo "Removing: $dir"
                        rm -rf "$dir" 2>/dev/null || true
                      fi
                    done
                  else
                    echo "No directories to clean"
                  fi
                  
                  echo -e "\nDirectories kept (newer than 600 minutes):"
                  if [ -n "$KEPT_DIRS" ]; then
                    echo "$KEPT_DIRS"
                  else
                    echo "No directories kept"
                  fi

                  echo "Cleanup completed"
              volumeMounts:
                - name: storage
                  mountPath: {{ .Values.worker.persistence.pvc.mountPath }}
          volumes:
            - name: storage
              persistentVolumeClaim:
                claimName: {{ include "insight-flow-cms.worker.fullname" . }}-storage
{{- end }}