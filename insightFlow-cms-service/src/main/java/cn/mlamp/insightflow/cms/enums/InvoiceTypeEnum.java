package cn.mlamp.insightflow.cms.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 发票类型枚举
 */
@RequiredArgsConstructor
public enum InvoiceTypeEnum {
    VET("vet", "增值税专用发票"),
    COMMON("common", "电子普通发票"),

    ;

    @Getter
    private final String type;
    @Getter
    private final String name;

    @Getter
    private static final Map<String, InvoiceTypeEnum> TYPE_TO_ENUM_MAP = Arrays.stream(InvoiceTypeEnum.values())
            .collect(Collectors.toUnmodifiableMap(InvoiceTypeEnum::getType, v -> v));
}
