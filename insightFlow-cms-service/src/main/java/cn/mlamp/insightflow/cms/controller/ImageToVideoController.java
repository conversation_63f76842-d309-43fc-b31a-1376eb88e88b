package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.model.dto.ImageToVideoDTO;
import cn.mlamp.insightflow.cms.model.vo.ImageToVideoVO;
import cn.mlamp.insightflow.cms.service.ImageToVideoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 首图生视频控制器
 */
@Slf4j
@RestController
@RequestMapping("/ai-video")
@AllArgsConstructor
@Tag(name = "火山生视频", description = "火山首图生视频相关接口")
public class ImageToVideoController {

    @Autowired
    private ImageToVideoService imageToVideoService;

    /**
     * 创建首图生视频任务
     */
    // @PostMapping("/huoshan/create")
    @Operation(summary = "创建首图生视频任务")
    public RespBody<ImageToVideoVO.ImageToVideoCreateResponse> createImageToVideoTask(
            @RequestBody @Valid ImageToVideoDTO.ImageToVideoCreateRequest request) {

        // 获取用户信息
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        // 创建任务并获取任务ID
        ImageToVideoVO.ImageToVideoCreateResponse result = imageToVideoService.createImageToVideoTask(request, userId,
                tenantId);

        // 异步执行首图生视频任务
        imageToVideoService.generateVideoAsync(request, result.getId(), userId, tenantId);

        return RespBody.ok(result);
    }

    /**
     * 查询首图生视频任务状态
     */
    @GetMapping("/query")
    @Operation(summary = "查询首图生视频任务状态")
    public RespBody<ImageToVideoVO.ImageToVideoQueryResponse> queryImageToVideoTask(@RequestParam String id) {
        ImageToVideoVO.ImageToVideoQueryResponse response = imageToVideoService.queryImageToVideoTask(id);
        return RespBody.ok(response);
    }

    /**
     * 创建即梦AI图生视频任务
     */
    // @PostMapping("/jimeng/create")
    @Operation(summary = "创建即梦AI图生视频任务")
    public RespBody<ImageToVideoVO.JimengImageToVideoCreateResponse> createJimengImageToVideoTask(
            @RequestBody @Valid ImageToVideoDTO.JimengImageToVideoCreateRequest request) {

        // 获取用户信息
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        // 创建任务并获取任务ID
        ImageToVideoVO.JimengImageToVideoCreateResponse result = imageToVideoService
                .createJimengImageToVideoTask(request, userId, tenantId);

        // 异步执行即梦AI图生视频任务
        imageToVideoService.generateJimengVideoAsync(request, result.getId(), userId, tenantId);

        return RespBody.ok(result);
    }


}
