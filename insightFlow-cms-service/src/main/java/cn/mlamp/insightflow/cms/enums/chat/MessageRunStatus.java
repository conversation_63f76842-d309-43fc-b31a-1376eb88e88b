package cn.mlamp.insightflow.cms.enums.chat;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import javax.annotation.Nullable;

public enum MessageRunStatus implements IEnum<Integer> {

    RUNNING(1),

    COMPLETED(2),

    FAILED(3);

    private final Integer value;

    MessageRunStatus(Integer value) {
        this.value = value;
    }

    @JsonValue
    @Override
    public Integer getValue() {
        return this.value;
    }

    @Nullable
    @JsonCreator
    public static MessageRunStatus fromValue(@Nullable Integer value) {
        if (value == null) {
            return null; // 返回null表示未定义状态
        }
        for (MessageRunStatus status : MessageRunStatus.values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown value: " + value);
    }

}
