{{ if index .Values "modules" "qna-transformers" "enabled" }}
apiVersion: v1
kind: Service
metadata:
  name: {{ index .Values "modules" "qna-transformers" "fullnameOverride" }}
  labels:
    app.kubernetes.io/name: weaviate
    app.kubernetes.io/managed-by: helm
spec:
  type: ClusterIP
  selector:
    app: {{ index .Values "modules" "qna-transformers" "fullnameOverride" }}
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
{{ end }}
