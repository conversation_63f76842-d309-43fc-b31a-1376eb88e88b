{{- if .Values.dashboards }}
{{ $files := .Files }}
{{- range $provider, $dashboards := .Values.dashboards }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "grafana.fullname" $ }}-dashboards-{{ $provider }}
  namespace: {{ include "grafana.namespace" $ }}
  labels:
    {{- include "grafana.labels" $ | nindent 4 }}
    dashboard-provider: {{ $provider }}
    {{- if $.Values.sidecar.dashboards.enabled }}
    {{ $.Values.sidecar.dashboards.label }}: {{ $.Values.sidecar.dashboards.labelValue | quote }}
    {{- end }}
{{- if $dashboards }}
data:
{{- $dashboardFound := false }}
{{- range $key, $value := $dashboards }}
{{- if (or (hasKey $value "json") (hasKey $value "file")) }}
{{- $dashboardFound = true }}
  {{- print $key | nindent 2 }}.json:
    {{- if hasKey $value "json" }}
    |-
      {{- $value.json | nindent 6 }}
    {{- end }}
    {{- if hasKey $value "file" }}
    {{- toYaml ( $files.Get $value.file ) | nindent 4}}
    {{- end }}
{{- end }}
{{- end }}
{{- if not $dashboardFound }}
  {}
{{- end }}
{{- end }}
---
{{- end }}

{{- end }}
