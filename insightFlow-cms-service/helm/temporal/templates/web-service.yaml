{{- if .Values.web.enabled -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "temporal.componentname" (list $ "web") }}
  labels:
    {{- include "temporal.resourceLabels" (list $ "web" "") | nindent 4 }}
  {{- if .Values.web.service.annotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.web.service.annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  {{- with .Values.web.service.loadBalancerIP }}
  loadBalancerIP: {{.}}
    {{- end }}
  type: {{ .Values.web.service.type }}
  {{- with .Values.web.service.clusterIP }}
  clusterIP: {{.}}
  {{- end }}
  ports:
    - port: {{ .Values.web.service.port }}
      targetPort: http
      protocol: TCP
      name: http
      {{- if hasKey .Values.web.service "nodePort" }}
      nodePort: {{ .Values.web.service.nodePort }}
      {{- end }}
  selector:
    app.kubernetes.io/name: {{ include "temporal.name" $ }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: web
  {{- end }}
