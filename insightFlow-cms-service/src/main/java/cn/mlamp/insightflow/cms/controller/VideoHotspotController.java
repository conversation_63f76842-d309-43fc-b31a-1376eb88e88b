package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.service.CmsPullTaskDedupedDataService;
import cn.mlamp.insightflow.cms.service.IVideoAsrService;
import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.entity.CmsPullTaskDedupedData;
import cn.mlamp.insightflow.cms.entity.CmsVideoAsr;
import cn.mlamp.insightflow.cms.model.query.RecommenListRequest;
import cn.mlamp.insightflow.cms.model.query.RecommendationRequest;
import cn.mlamp.insightflow.cms.model.query.VideoHotspotQueryRequest;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.baomidou.mybatisplus.core.metadata.IPage;


@RestController
@RequestMapping("/video-hotspot")
public class VideoHotspotController {

    @Autowired
    private CmsPullTaskDedupedDataService cmsPullTaskDedupedDataService;

    @Autowired
    private IVideoAsrService iVideoAsrService;

    /**
     * 获取视频热点列表
     *
     * @param request 查询参数对象
     * @return 视频热点列表
     */
    @PostMapping("/list")
    public RespBody<IPage<CmsPullTaskDedupedData>> getVideoHotspotList(
            @RequestBody VideoHotspotQueryRequest request) {

        // 调用 service 层处理业务逻辑
        IPage<CmsPullTaskDedupedData> videoHotspots = cmsPullTaskDedupedDataService.getVideoHotspotList(request);

        return RespBody.ok(videoHotspots);
    }

    /**
     * 获取今天最高互动数的推荐数据
     *
     * @param type 类型（达人、标题、语音识别）
     * @return 返回推荐的字符串
     */
    @PostMapping("/recommend")
    public RespBody<String> getRecommendation(@RequestBody RecommendationRequest request) {

        String recommendation = "";

        // 获取今天的最高互动数数据
        CmsPullTaskDedupedData topData = cmsPullTaskDedupedDataService.getTopInteractiveDataOfToday();
        if (topData == null) {
            return RespBody.ok(recommendation);
        }
        switch (request.getType()) {
            case "1":
                recommendation = topData.getTextNickName(); // 返回“用户昵称”
                break;
            case "2":
                recommendation = topData.getTextContent(); // 返回“标题”
                break;
            case "3":
                CmsVideoAsr asrResult = iVideoAsrService.getByEsIdWithMinStart(topData.getEsId());
                recommendation = (asrResult != null) ? asrResult.getText() : "";
                break;
            default:
                return RespBody.ok(recommendation);
        }

        return RespBody.ok(recommendation);
    }

    /**
     * 获取推荐列表数据（达人、标题、语音识别）
     *
     * @param keyword 类型（达人、标题、语音识别）
     * @return 返回推荐的字符串
     */
    @PostMapping("/recommend/list")
    public RespBody<List<CmsPullTaskDedupedData>> getRecommendationList(@RequestBody RecommenListRequest request) {

        // 调用 service 层处理业务逻辑
        List<CmsPullTaskDedupedData> videoHotspots = cmsPullTaskDedupedDataService.getRecommenListRequest(request);

        return RespBody.ok(videoHotspots);
    }

    @PostMapping("/tribe/list")
    public RespBody<List<Map<String, Object>>> getTribeStatistics() {
        List<Map<String, Object>> tribeStatistics = cmsPullTaskDedupedDataService.getTribeStatisticsList();
        return RespBody.ok(tribeStatistics);
    }

}
