{{- if $.Values.admintools.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "temporal.componentname" (list $ "admintools") }}
  annotations:
    {{- include "temporal.resourceAnnotations" (list $ "admintools" "deployment") | nindent 4 }}
  labels:
    {{- include "temporal.resourceLabels" (list $ "admintools" "deployment") | nindent 4 }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "temporal.name" $ }}
      app.kubernetes.io/instance: {{ .Release.Name }}
      app.kubernetes.io/component: admintools
  template:
    metadata:
      annotations:
        {{- include "temporal.resourceAnnotations" (list $ "admintools" "pod") | nindent 8 }}
      labels:
        {{- include "temporal.resourceLabels" (list $ "admintools" "pod") | nindent 8 }}
    spec:
      {{ include "temporal.serviceAccount" $ }}
      {{- with $.Values.admintools.additionalInitContainers }}
      initContainers:
        {{- toYaml .| nindent 8 }}
      {{- end }}
      containers:
        - name: admin-tools
          image: "{{ .Values.admintools.image.repository }}:{{ .Values.admintools.image.tag }}"
          imagePullPolicy: {{ .Values.admintools.image.pullPolicy }}
          env:
            # TEMPORAL_CLI_ADDRESS is deprecated, use TEMPORAL_ADDRESS instead
            - name: TEMPORAL_CLI_ADDRESS
              {{- if and (hasKey .Values.server "internalFrontend") .Values.server.internalFrontend.enabled }}
              value: {{ include "temporal.fullname" $ }}-internal-frontend:{{ .Values.server.internalFrontend.service.port }}
              {{- else }}
              value: {{ include "temporal.fullname" $ }}-frontend:{{ .Values.server.frontend.service.port }}
              {{- end }}
            - name: TEMPORAL_ADDRESS
              {{- if and (hasKey .Values.server "internalFrontend") .Values.server.internalFrontend.enabled }}
              value: {{ include "temporal.fullname" $ }}-internal-frontend:{{ .Values.server.internalFrontend.service.port }}
              {{- else }}
              value: {{ include "temporal.fullname" $ }}-frontend:{{ .Values.server.frontend.service.port }}
              {{- end }}
          {{- with .Values.admintools.additionalEnv }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.admintools.additionalEnvSecretName }}
          envFrom:
            - secretRef:
                name: {{ . }}
          {{- end }}
          livenessProbe:
              exec:
                command:
                - ls
                - /
              initialDelaySeconds: 5
              periodSeconds: 5
          {{- with $.Values.admintools.additionalVolumeMounts }}
          volumeMounts:
            {{- toYaml . | nindent 12}}
          {{- end }}
          {{- with .Values.admintools.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
      {{- with $.Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $.Values.admintools.additionalVolumes }}
      volumes:
        {{- toYaml . | nindent 8}}
      {{- end }}
      {{- with .Values.admintools.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.admintools.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.admintools.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
{{- end }}
