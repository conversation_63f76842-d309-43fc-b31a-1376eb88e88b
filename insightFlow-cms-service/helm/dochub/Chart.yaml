apiVersion: v2
appVersion: 0.3.8
dependencies:
- condition: postgresql.enabled
  name: postgresql
  # repository: https://charts.bitnami.com/bitnami
  # tags:
  # - postgres
  # version: 12.5.6
- condition: redis.enabled
  name: redis
  repository: https://charts.bitnami.com/bitnami
  tags:
  - redis
  version: 16.13.2
- condition: weaviate.enabled
  name: weaviate
  repository: https://weaviate.github.io/weaviate-helm
  tags:
  - vector
  - search
  version: 16.1.0
description: Instant cloud deployment for https://github.com/langgenius/dify.
name: dochub
type: application
version: 0.16.1
