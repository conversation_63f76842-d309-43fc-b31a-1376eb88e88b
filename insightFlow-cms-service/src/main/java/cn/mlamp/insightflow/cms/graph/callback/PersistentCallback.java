package cn.mlamp.insightflow.cms.graph.callback;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.json.JSONUtil;
import cn.mlamp.insightflow.cms.entity.chat.Message;
import cn.mlamp.insightflow.cms.entity.chat.MessageRun;
import cn.mlamp.insightflow.cms.enums.chat.MessageRunStatus;
import cn.mlamp.insightflow.cms.enums.chat.MessageRunType;
import cn.mlamp.insightflow.cms.enums.chat.MessageStatus;
import cn.mlamp.insightflow.cms.graph.entities.EnvInfo;
import cn.mlamp.insightflow.cms.graph.events.action.ActionType;
import cn.mlamp.insightflow.cms.graph.helper.EnvHelper;
import cn.mlamp.insightflow.cms.model.vo.chat.ResourceVO;
import cn.mlamp.insightflow.cms.service.chat.IMessageRunService;
import cn.mlamp.insightflow.cms.service.chat.IMessageService;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class PersistentCallback implements Callback {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private final Integer tenantId;

    private final Integer userId;

    private final Integer conversationId;

    private final Integer messageId;

    private final IMessageService messageService;

    private final IMessageRunService messageRunService;

    public PersistentCallback(
            Integer tenantId,
            Integer userId,
            Integer conversationId,
            Integer messageId,
            IMessageService messageService,
            IMessageRunService messageRunService
    ) {
        this.tenantId = tenantId;
        this.userId = userId;
        this.messageService = messageService;
        this.messageRunService = messageRunService;
        this.conversationId = conversationId;
        this.messageId = messageId;
    }

    @Override
    public void onWorkflowStarted(String query, EnvInfo env) {

    }

    @Override
    public void onWorkflowFinished(String answer) {
        messageService.update(new LambdaUpdateWrapper<Message>()
                .eq(Message::getId, messageId)
                .set(Message::getAnswer, answer)
                .set(Message::getStatus, MessageStatus.COMPLETED)
        );
    }

    @Override
    public void onWorkflowError(Throwable t) {
        messageService.update(
                new LambdaUpdateWrapper<Message>()
                        .eq(Message::getId, messageId)
                        .set(Message::getError, t.getMessage())
                        .set(Message::getStatus, MessageStatus.FAILED)
        );
    }

    @Override
    public void onMessage(String runId, String message) {

    }

    @Override
    public void onMessageEnd(String runId, String content) {
        final EnvInfo env = EnvHelper.getEnv();
        final MessageRun messageRun = new MessageRun();
        messageRun.setMessageRunId(runId);
        messageRun.setConversationId(conversationId);
        messageRun.setMessageId(messageId);
        messageRun.setType(MessageRunType.MESSAGE);
        messageRun.setEnv(env);
        messageRun.setName("message");
        messageRun.setOutput(content);
        messageRun.setStatus(MessageRunStatus.COMPLETED);

        messageRunService.save(messageRun);
    }

    @Override
    public void onNodeStarted(String runId, String nodeName, String goal) {
        final EnvInfo env = EnvHelper.getEnv();

        final MessageRun messageRun = new MessageRun();
        messageRun.setMessageRunId(runId);
        messageRun.setConversationId(conversationId);
        messageRun.setMessageId(messageId);
        messageRun.setType(MessageRunType.NODE);
        messageRun.setEnv(env);
        messageRun.setName(nodeName);
        messageRun.setInput(goal);
        messageRun.setStatus(MessageRunStatus.RUNNING);

        messageRunService.save(messageRun);
    }

    @Override
    public void onNodeFinished(String runId, String nodeName, String response) {
        messageRunService.update(
                new LambdaUpdateWrapper<MessageRun>()
                        .eq(MessageRun::getMessageRunId, runId)
                        .set(MessageRun::getOutput, response)
                        .set(MessageRun::getStatus, MessageRunStatus.COMPLETED)
        );
    }

    @Override
    public void onNodeError(String runId, String nodeName, Throwable t) {
        messageRunService.update(
                new LambdaUpdateWrapper<MessageRun>()
                        .eq(MessageRun::getMessageRunId, runId)
                        .set(MessageRun::getError, t.getMessage())
                        .set(MessageRun::getStatus, MessageRunStatus.FAILED)
        );
    }

    @Override
    public void onToolStarted(String runId, String toolName, String label, String input) {
        final EnvInfo env = EnvHelper.getEnv();

        final MessageRun messageRun = new MessageRun();
        messageRun.setMessageRunId(runId);
        messageRun.setConversationId(conversationId);
        messageRun.setMessageId(messageId);
        messageRun.setType(MessageRunType.TOOL);
        messageRun.setEnv(env);
        messageRun.setName(toolName);
        messageRun.setLabel(label);
        messageRun.setInput(input);
        messageRun.setStatus(MessageRunStatus.RUNNING);

        messageRunService.save(messageRun);
    }

    @Override
    public void onToolFinished(String runId, String toolName, String label, String input, String output, ResourceVO... resources) {
        final List<ResourceVO> resourceVOs = ArrayUtil.isNotEmpty(resources) ? Arrays.asList(resources) : List.of();

        try {
            final String resourcesJson = OBJECT_MAPPER.writeValueAsString(resourceVOs);
            messageRunService.update(
                    new LambdaUpdateWrapper<MessageRun>()
                            .eq(MessageRun::getMessageRunId, runId)
                            .set(MessageRun::getOutput, output)
                            .set(ArrayUtil.isNotEmpty(resources), MessageRun::getResources, resourcesJson)
                            .set(MessageRun::getStatus, MessageRunStatus.COMPLETED)
            );
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void onToolError(String runId, String toolName, String label, String input, Throwable t) {
        messageRunService.update(
                new LambdaUpdateWrapper<MessageRun>()
                        .eq(MessageRun::getMessageRunId, runId)
                        .set(MessageRun::getError, t.getMessage())
                        .set(MessageRun::getStatus, MessageRunStatus.FAILED)
        );
    }

    @Override
    public void onAction(String runId, ActionType actionType, Object parameters) {
//        final EnvInfo env = EnvHelper.getEnv();
//
//        final MessageRun messageRun = new MessageRun();
//        messageRun.setMessageRunId(runId);
//        messageRun.setConversationId(conversationId);
//        messageRun.setMessageId(messageId);
//        messageRun.setType(MessageRunType.TOOL);
//        messageRun.setEnv(env);
//        messageRun.setName(actionType.getValue());
//        messageRun.setOutput(JSONUtil.toJsonStr(Map.of(
//                "type", actionType,
//                "parameters", parameters
//        )));
//        messageRun.setStatus(MessageRunStatus.COMPLETED);
//
//        messageRunService.save(messageRun);
    }

}
