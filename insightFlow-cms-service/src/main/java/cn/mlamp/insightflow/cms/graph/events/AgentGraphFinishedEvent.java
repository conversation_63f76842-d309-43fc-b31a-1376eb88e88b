package cn.mlamp.insightflow.cms.graph.events;

import lombok.Data;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * Agent Graph消息事件 用于传递节点执行过程中的消息信息
 */
@Getter
public class AgentGraphFinishedEvent extends AbstractAgentGraphEvent<AgentGraphFinishedEvent.EventData> {

    private final EventType event = EventType.WORKFLOW_FINISHED;

    private final Map<String, Object> metadata;

    public AgentGraphFinishedEvent(
            String conversationId,
            String messageId
    ) {
        this(conversationId, messageId, new HashMap<>());
    }

    public AgentGraphFinishedEvent(
            String conversationId,
            String messageId,
            Map<String, Object> metadata
    ) {
        super(conversationId, messageId);
        this.metadata = new HashMap<>(metadata);
    }

    @Override
    protected EventData getEventData() {
        final EventData eventData = new EventData();
        eventData.setElapsedTime(System.currentTimeMillis() - getCreateTime());
        eventData.setCreateTime(getCreateTime());
        eventData.setFinishTime(System.currentTimeMillis());
        return eventData;
    }

    @Data
    public static class EventData {
        private long elapsedTime;
        private long createTime;
        private long finishTime;
    }

}
