FROM hub.intra.mlamp.cn/public/alpine:3.21

ENV KUBECTL_VERSION="1.33.1"
ENV HELM_VERSION="3.15.3"

RUN apk add --no-cache curl bash git openssh ca-certificates openssl yq \
    && apk add --no-cache --virtual .build-deps \
    && update-ca-certificates

RUN curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl" \
    && chmod +x kubectl \
    && mv kubectl /usr/local/bin/ \
    && kubectl version --client

RUN curl -fsSL -o get_helm.sh https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 \
    && chmod +x get_helm.sh \
    && ./get_helm.sh --version v${HELM_VERSION} \
    && rm get_helm.sh

#    && helm version



WORKDIR /app

ENTRYPOINT ["/bin/bash", "-c"]

CMD ["echo 'Kubernetes CLI tools ready. kubectl version:' && kubectl version --client && echo 'Helm version:' && helm version"]