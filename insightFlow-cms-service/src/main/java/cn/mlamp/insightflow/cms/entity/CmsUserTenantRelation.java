package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 用户租户关系表
 */
@Data
@TableName("cms_user_tenant_relation")
public class CmsUserTenantRelation extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer userId;

    private Integer tenantId;

    private Timestamp createTime;

    private Timestamp updateTime;

    // 数据逻辑删除标记 0:未删除 1:已删除
    private Integer isDeleted;

}

