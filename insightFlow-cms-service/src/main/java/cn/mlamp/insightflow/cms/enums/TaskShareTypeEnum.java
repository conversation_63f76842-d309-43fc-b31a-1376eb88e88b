package cn.mlamp.insightflow.cms.enums;

import lombok.Getter;

/**
 * 任务分享类型枚举
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Getter
public enum TaskShareTypeEnum {

    /**
     * 视频合成任务
     */
    VIDEO_SYNTHESIS(1, "视频合成任务"),

    /**
     * 视频价值任务
     */
    VIDEO_VALUE(2, "视频价值任务"),

    /**
     * 智能分镜任务
     */
    SMART_CUT(3, "智能分镜任务"),
    ;

    private final Integer code;
    private final String msg;

    TaskShareTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static TaskShareTypeEnum getByCode(Integer code) {
        for (TaskShareTypeEnum typeEnum : TaskShareTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
