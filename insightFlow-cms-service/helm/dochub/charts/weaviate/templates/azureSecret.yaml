{{ if and (index .Values "backups" "azure" "enabled") (index .Values "backups" "azure" "secrets") }}
{{- if or (index .Values "backups" "azure" "secrets" "AZURE_STORAGE_ACCOUNT") (index .Values "backups" "azure" "secrets" "AZURE_STORAGE_KEY") (index .Values "backups" "azure" "secrets" "AZURE_STORAGE_CONNECTION_STRING") }}
apiVersion: v1
kind: Secret
metadata:
  name: backup-azure
  labels:
    app.kubernetes.io/name: weaviate
    app.kubernetes.io/managed-by: helm
type: Opaque
data:
  {{- if index .Values "backups" "azure" "secrets" "AZURE_STORAGE_ACCOUNT" }}
  AZURE_STORAGE_ACCOUNT: {{ index .Values "backups" "azure" "secrets" "AZURE_STORAGE_ACCOUNT" | b64enc }}
  {{- end }}
  {{- if index .Values "backups" "azure" "secrets" "AZURE_STORAGE_KEY" }}
  AZURE_STORAGE_KEY: {{ index .Values "backups" "azure" "secrets" "AZURE_STORAGE_KEY" | b64enc }}
  {{- end }}
  {{- if index .Values "backups" "azure" "secrets" "AZURE_STORAGE_CONNECTION_STRING" }}
  AZURE_STORAGE_CONNECTION_STRING: {{ index .Values "backups" "azure" "secrets" "AZURE_STORAGE_CONNECTION_STRING" | b64enc }}
  {{- end }}
{{- end }}
{{ end }}
