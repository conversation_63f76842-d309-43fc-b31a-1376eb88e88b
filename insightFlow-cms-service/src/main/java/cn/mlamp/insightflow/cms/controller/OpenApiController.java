package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.model.vo.GetVideoValueTaskVO;
import cn.mlamp.insightflow.cms.model.vo.TaskShareDetailVO;
import cn.mlamp.insightflow.cms.model.vo.VideoTaskDetailVO;
import cn.mlamp.insightflow.cms.service.ITaskShareReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 开放API接口
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/open-api")
@Tag(name = "开放API接口")
public class OpenApiController {

    private final ITaskShareReportService taskShareReportService;

    @GetMapping("/share")
    @Operation(summary = "获取分享详情")
    public RespBody<TaskShareDetailVO> getShareDetail(@RequestParam("code") String authorizeCode) {
        TaskShareDetailVO result = taskShareReportService.getShareDetailByCode(authorizeCode);
        return RespBody.ok(result);
    }

    @GetMapping("/share/video/task/{code}")
    @Operation(summary = "获取分享分镜任务详情")
    public RespBody<VideoTaskDetailVO> getShareVideoTask(@PathVariable("code") String code) {
        VideoTaskDetailVO result = taskShareReportService.getSmartCutDetailByCode(code);
        return RespBody.ok(result);
    }

    @GetMapping("/share/video-value/{code}")
    @Operation(summary = "获取视频价值任务分享详情")
    public RespBody<GetVideoValueTaskVO> getVideoValueDetailByCode(@PathVariable("code") String code) {
        GetVideoValueTaskVO result = taskShareReportService.getVideoValueDetailByCode(code);
        return RespBody.ok(result);
    }
}
