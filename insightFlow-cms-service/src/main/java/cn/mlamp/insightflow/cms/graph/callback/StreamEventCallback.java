package cn.mlamp.insightflow.cms.graph.callback;

import cn.mlamp.insightflow.cms.graph.entities.EnvInfo;
import cn.mlamp.insightflow.cms.graph.events.*;
import cn.mlamp.insightflow.cms.graph.events.action.ActionType;
import cn.mlamp.insightflow.cms.graph.helper.EnvHelper;
import cn.mlamp.insightflow.cms.model.vo.chat.ResourceVO;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Sinks;

import java.util.Map;

@Slf4j
public class StreamEventCallback implements Callback {

    private final String conversationId;

    private final String messageId;

    private final Sinks.Many<AgentGraphEvent<?>> sink;

    public StreamEventCallback(
            String conversationId,
            String messageId,
            Sinks.Many<AgentGraphEvent<?>> sink
    ) {
        this.conversationId = conversationId;
        this.messageId = messageId;
        this.sink = sink;
    }

    @Override
    public void onWorkflowStarted(String query, EnvInfo env) {
        sink.emitNext(new AgentGraphStartedEvent(conversationId, messageId), Sinks.EmitFailureHandler.FAIL_FAST);
    }

    @Override
    public void onWorkflowFinished(String answer) {
        sink.emitNext(new AgentGraphFinishedEvent(conversationId, messageId), Sinks.EmitFailureHandler.FAIL_FAST);
        sink.tryEmitComplete();
    }

    @Override
    public void onWorkflowError(Throwable t) {
        log.error("工作流执行错误, ", t);
        sink.emitNext(new AgentGraphErrorEvent(conversationId, messageId, "工作流执行错误"), Sinks.EmitFailureHandler.FAIL_FAST);
        sink.emitError(t, Sinks.EmitFailureHandler.FAIL_FAST);
    }

    @Override
    public void onMessage(String runId, String message) {
        sink.emitNext(new AgentGraphMessageEvent(conversationId, messageId, runId, message), Sinks.EmitFailureHandler.FAIL_FAST);
    }

    @Override
    public void onMessageEnd(String runId, String content) {
        sink.emitNext(new AgentGraphMessageEndEvent(conversationId, messageId, runId, content), Sinks.EmitFailureHandler.FAIL_FAST);
    }

    @Override
    public void onNodeStarted(String runId, String nodeName, String goal) {
    }

    @Override
    public void onNodeFinished(String runId, String nodeName, String response) {
    }

    @Override
    public void onNodeError(String runId, String nodeName, Throwable t) {

    }

    @Override
    public void onToolStarted(String runId, String toolName, String label, String input) {
        final EnvInfo env = EnvHelper.getEnv();
        sink.emitNext(new AgentGraphToolStartedEvent(
                conversationId,
                messageId,
                runId,
                toolName,
                label,
                env,
                input
        ), Sinks.EmitFailureHandler.FAIL_FAST);
    }

    @Override
    public void onToolFinished(String runId, String toolName, String label, String input, String output, ResourceVO... resources) {
        sink.emitNext(new AgentGraphToolFinishedEvent(
                conversationId,
                messageId,
                runId,
                toolName,
                label,
                EnvHelper.getEnv(),
                input,
                output
        ), Sinks.EmitFailureHandler.FAIL_FAST);
    }

    @Override
    public void onToolError(String runId, String toolName, String label, String input, Throwable t) {
        log.error("工具调用错误, ", t);
        sink.emitNext(new AgentGraphToolErrorEvent(
                conversationId,
                messageId,
                runId,
                toolName,
                label,
                input,
                t.getMessage()
        ), Sinks.EmitFailureHandler.FAIL_FAST);
    }

    @Override
    public void onAction(String runId, ActionType actionType, Object parameters) {
        sink.emitNext(new AgentGraphActionEvent(
                conversationId,
                messageId,
                runId,
                actionType,
                parameters
        ), Sinks.EmitFailureHandler.FAIL_FAST);
    }

}
