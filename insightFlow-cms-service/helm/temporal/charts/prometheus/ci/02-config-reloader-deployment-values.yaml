---
## Test case: test config-reloader in deployment
configmapReload:
  env:
    - name: APPNAME
      value: "prometheus-config-reloader"

  prometheus:
    containerSecurityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
          - ALL
      runAsNonRoot: true
      readOnlyRootFilesystem: true
      seccompProfile:
        type: RuntimeDefault

    extraArgs:
      log-level: debug
      watch-interval: 1m

server:
  statefulSet:
    enabled: false
