{{- if .Values.ingress.enabled }}
{{- $serviceName := include "prometheus-pushgateway.fullname" . }}
{{- $servicePort := .Values.service.port }}
{{- $ingressPath := .Values.ingress.path }}
{{- $ingressClassName := .Values.ingress.className }}
{{- $ingressPathType := .Values.ingress.pathType }}
{{- $extraPaths := .Values.ingress.extraPaths }}
apiVersion: {{ include "prometheus-pushgateway.ingress.apiVersion" . }}
kind: Ingress
metadata:
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    {{- include "prometheus-pushgateway.defaultLabels" . | nindent 4 }}
  name: {{ include "prometheus-pushgateway.fullname" . }}
  namespace: {{ template "prometheus-pushgateway.namespace" . }}
spec:
  {{- if semverCompare ">=1.19-0" .Capabilities.KubeVersion.GitVersion }}
  ingressClassName: {{ $ingressClassName }}
  {{- end }}
  rules:
    {{- range $host := .Values.ingress.hosts }}
    - host: {{ $host }}
      http:
        paths:
          {{- with $extraPaths }}
          {{- toYaml . | nindent 10 }}
          {{- end }}
          - path: {{ $ingressPath }}
            {{- if semverCompare ">=1.19-0" $.Capabilities.KubeVersion.GitVersion }}
            pathType: {{ $ingressPathType }}
            {{- end }}
            backend:
              {{- if semverCompare ">=1.19-0" $.Capabilities.KubeVersion.GitVersion }}
              service:
                name: {{ $serviceName }}
                port:
                  number: {{ $servicePort }}
              {{- else }}
              serviceName: {{ $serviceName }}
              servicePort: {{ $servicePort }}
              {{- end }}
    {{- end -}}
  {{- with .Values.ingress.tls }}
  tls:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
