package cn.mlamp.insightflow.cms.aspect;

import cn.mlamp.insightflow.cms.annotation.RedisSemaphore;
import cn.mlamp.insightflow.cms.service.concurrent.RedisSemaphoreService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RSemaphore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.Collection;
import java.util.Collections;

@Slf4j
@Aspect
@Component
public class RedisSemaphoreAspect {

    @Autowired
    private RedisSemaphoreService redisSemaphoreService;

    private final SpelExpressionParser parser = new SpelExpressionParser();

    private final DefaultParameterNameDiscoverer nameDiscoverer = new DefaultParameterNameDiscoverer();

    @Around("@annotation(redisSemaphore)")
    public Object around(ProceedingJoinPoint joinPoint, RedisSemaphore redisSemaphore) throws Throwable {
        final String semaphoreKey = parseSemaphoreKey(joinPoint, redisSemaphore);
        final RSemaphore semaphore = redisSemaphoreService.getSemaphore(semaphoreKey, redisSemaphore.permits());

        int acquires = redisSemaphore.acquires();
        boolean acquired = false;

        try {
            if (redisSemaphore.timeout() <= 0) {
                semaphore.acquire(redisSemaphore.permits());
                acquired = true;
            } else {
                // 尝试获取许可
                acquired = semaphore.tryAcquire(acquires, redisSemaphore.timeout(), redisSemaphore.timeUnit());
            }

            if (acquired) {
                log.info("成功获取信号量许可: key={}, acquires={}, available={}",
                        semaphoreKey, acquires, semaphore.availablePermits());

                // 执行目标方法
                return joinPoint.proceed();
            } else {
                log.warn("获取信号量许可失败: key={}, acquires={}, available={}",
                        semaphoreKey, acquires, semaphore.availablePermits());

                return handleAcquireFailure(joinPoint, redisSemaphore);
            }
        } finally {
            if (acquired) {
                semaphore.release(acquires);
                log.info("释放信号量许可: key={}, released={}, available={}",
                        semaphoreKey, acquires, semaphore.availablePermits());
            }
        }
    }

    private String parseSemaphoreKey(ProceedingJoinPoint joinPoint, RedisSemaphore redisSemaphore) {
        String key = redisSemaphore.key();

        if (StringUtils.hasText(key)) {
            // 支持SpEL表达式
            if (key.contains("#")) {
                return parseSpelExpression(joinPoint, key);
            }
            return key;
        }

        // 默认key生成策略
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        return "semaphore:" + className + ":" + methodName;
    }

    private String parseSpelExpression(ProceedingJoinPoint joinPoint, String expression) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            Object[] args = joinPoint.getArgs();

            EvaluationContext context = new StandardEvaluationContext();
            String[] paramNames = nameDiscoverer.getParameterNames(method);

            if (paramNames != null) {
                for (int i = 0; i < paramNames.length; i++) {
                    context.setVariable(paramNames[i], args[i]);
                }
            }

            Expression exp = parser.parseExpression(expression);
            return exp.getValue(context, String.class);
        } catch (Exception e) {
            log.error("解析SpEL表达式失败: {}", expression, e);
            return expression;
        }
    }

    private Object handleAcquireFailure(ProceedingJoinPoint joinPoint, RedisSemaphore redisSemaphore) {
        RedisSemaphore.FailureStrategy strategy = redisSemaphore.failureStrategy();
        String message = redisSemaphore.failureMessage();

        return switch (strategy) {
            case RETURN_NULL -> null;
            case RETURN_DEFAULT -> getDefaultValue(joinPoint);
            default -> throw new SemaphoreAcquireException(message);
        };
    }

    private Object getDefaultValue(ProceedingJoinPoint joinPoint) {
        Class<?> returnType = ((MethodSignature) joinPoint.getSignature()).getReturnType();

        if (returnType == void.class || returnType == Void.class) {
            return null;
        } else if (returnType == boolean.class || returnType == Boolean.class) {
            return false;
        } else if (returnType.isPrimitive()) {
            return 0;
        } else if (Collection.class.isAssignableFrom(returnType)) {
            return Collections.emptyList();
        } else {
            return null;
        }
    }

    public static class SemaphoreAcquireException extends RuntimeException {
        public SemaphoreAcquireException(String message) {
            super(message);
        }
    }

}
