package cn.mlamp.insightflow.cms.graph.events;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Data;

import javax.annotation.Nullable;
import java.util.Map;

/**
 * Agent Graph事件基础接口 定义了图执行过程中的各种事件类型
 */
public interface AgentGraphEvent<T> {

    String getConversationId();

    String getMessageId();

    String getRunId();

    /**
     * 获取事件类型
     */
    EventType getEvent();

    /**
     * 获取事件时间戳
     */
    long getCreateTime();

    /**
     * 获取事件元数据
     */
    Map<String, Object> getMetadata();

    Chunk<T> toChunk();

    /**
     * 事件类型枚举
     */
    enum EventType {
        WORKFLOW_STARTED("started"), // 工作流开始
        WORKFLOW_FINISHED("finished"), // 工作流完成
        WORKFLOW_ERROR("error"), // 工作流错误
        NODE_STARTED("node_started"), // 节点开始执行
        NODE_FINISHED("node_finished"), // 节点执行完成
        NODE_ERROR("node_error"), // 节点执行错误
        MESSAGE("message"), // 消息事件
        MESSAGE_END("message_end"), // 消息事件
        TOOL_STARTED("tool_started"), // 工具调用开始
        TOOL_FINISHED("tool_finished"), // 工具调用完成
        TOOL_ERROR("tool_error"),
        ACTION("action"); // 工具调用错误

        private final String value;

        EventType(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

    }

    @Data
    class Chunk<T> {

        private EventType event;

        private String conversationId;

        private String messageId;

        @Nullable
        private String runId;

        private long createTime;

        private T data;

    }
}
