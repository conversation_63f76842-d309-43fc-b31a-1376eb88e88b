package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.model.query.CreateOrderRequest;
import cn.mlamp.insightflow.cms.model.vo.CreateOrderVO;
import cn.mlamp.insightflow.cms.model.vo.ListMemberOrdersVO;
import cn.mlamp.insightflow.cms.model.vo.OrderDetailVO;
import cn.mlamp.insightflow.cms.service.OrderService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/orders")
@RequiredArgsConstructor
public class OrderController {

    private final OrderService orderService;

    /**
     * 创建订单
     *
     * @param request
     * @return
     */
    @PostMapping
    public RespBody<CreateOrderVO> createOrder(@RequestBody CreateOrderRequest request) {
        CreateOrderVO vo = orderService.createOrder(request);
        return RespBody.ok(vo);
    }

    /**
     * 查询订单状态
     *
     * @param orderId
     * @return
     */
    @GetMapping("/status")
    public RespBody<String> queryOrderStatus(@RequestParam(name = "orderId", required = true) Integer orderId) {
        String status = orderService.queryOrderStatus(orderId);
        return RespBody.ok(status);
    }

    /**
     * 订单列表
     *
     * @param condition
     * @param current
     * @param pageSize
     * @return
     */
    @GetMapping("/list")
    public RespBody<IPage<ListMemberOrdersVO>> listOrders(@RequestParam(required = false) String condition,
                                                          @RequestParam int current, @RequestParam int pageSize) {
        IPage<ListMemberOrdersVO> result = orderService.listOrders(condition, current, pageSize);
        return RespBody.ok(result);
    }

    /**
     * 订单详情
     *
     * @param id
     * @return
     */
    @GetMapping
    public RespBody<OrderDetailVO> getOrderDetail(@RequestParam("id") int id) {
        OrderDetailVO vo = orderService.getOrderDetail(id);
        return RespBody.ok(vo);
    }
}