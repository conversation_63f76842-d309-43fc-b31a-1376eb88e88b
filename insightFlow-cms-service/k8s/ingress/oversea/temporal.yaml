apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: temporal-ingress
  namespace: temporal
  annotations:
    kubernetes.io/ingress.internetChargeType: TRAFFIC_POSTPAID_BY_HOUR
    kubernetes.io/ingress.internetMaxBandwidthOut: "100"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  ingressClassName: qcloud
  tls:
    - hosts:
        - temporal-meowoo.mlamp.cn
      secretName: mlamp-cn-oknob5t4
  rules:
    - host: temporal-meowoo.mlamp.cn
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: temporal-web
                port:
                  number: 8080
#          - path: /
#            pathType: Prefix
#            backend:
#              service:
#                name: temporal-frontend
#                port:
#                  number: 7243