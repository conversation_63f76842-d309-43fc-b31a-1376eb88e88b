package cn.mlamp.insightflow.cms.config.properties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 豆包API配置属性
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Configuration
@ConfigurationProperties(prefix = "doubao")
public class DoubaoApiProperties {

    /**
     * 豆包API基础URL
     */
    private String baseUrl;

    /**
     * 豆包API密钥
     */
    private String apiKey;
}
