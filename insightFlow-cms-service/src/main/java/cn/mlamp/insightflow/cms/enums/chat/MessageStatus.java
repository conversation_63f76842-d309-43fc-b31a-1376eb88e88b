package cn.mlamp.insightflow.cms.enums.chat;

import javax.annotation.Nullable;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 消息状态
 */
public enum MessageStatus implements IEnum<Integer> {
    /**
     * 运行中
     */
    RUNNING(1),

    /**
     * 完成
     */
    COMPLETED(2),

    /**
     * 失败
     */
    FAILED(3);

    private final Integer value;

    MessageStatus(Integer value) {
        this.value = value;
    }

    @JsonValue
    @Override
    public Integer getValue() {
        return this.value;
    }

    @Nullable
    @JsonCreator
    public static MessageStatus from(@Nullable Integer value) {
        if (value == null) {
            return null;
        }
        for (MessageStatus status : MessageStatus.values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("MessageStatus Unknown value: " + value);
    }

}
