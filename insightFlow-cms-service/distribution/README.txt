===========================================
InsightFlow CMS 服务部署说明
===========================================

版本: 1.0-SNAPSHOT
构建时间: ${maven.build.timestamp}

目录结构:
├── bin/           # 启动脚本目录
│   └── run.sh     # 统一启动脚本
├── conf/          # 配置文件目录
│   ├── application.yml
│   ├── application-*.yml
│   └── ...
├── logs/          # 日志目录（运行时创建）
├── lib/           # 第三方依赖JAR目录
├── boot/          # 应用类文件目录
└── README.txt     # 本文档

启动方式:
=========

1. API服务模式:
   MODE=api ./bin/run.sh

2. Worker服务模式:
   MODE=worker ./bin/run.sh

3. 指定环境配置:
   MODE=api APP_ENV=prod ./bin/run.sh
   MODE=worker APP_ENV=test ./bin/run.sh

环境变量:
=========
- MODE: 必需，指定启动模式（api/worker）
- APP_ENV: 可选，指定Spring环境配置（prod/test/dev）
- JVM_OPS: 可选，追加JVM参数
- APP_OPTS: 可选，追加应用参数
- JAVA_HOME: 可选，指定Java安装路径

示例:
====
# 生产环境API服务
MODE=api APP_ENV=prod JVM_OPS="-XX:+PrintGC" ./bin/run.sh

# 测试环境Worker服务
MODE=worker APP_ENV=test ./bin/run.sh

# Docker部署
docker run -e MODE=api -e APP_ENV=prod your-image

注意事项:
========
1. 确保Java 17+ 环境
2. 配置文件位于conf/目录，可根据需要修改
3. 日志输出到logs/目录
4. 支持容器化部署
5. 进程为同步运行，适合Docker entrypoint 