package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 用户表;
 *
 * <AUTHOR> husper
 * @date : 2025-3-19
 */
@Data
@TableName("cms_user")
public class CmsUser extends BaseEntity {
    /**
     * 主键 自增id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 用户头像url
     */
    private String pic;
    /**
     * TCC的用户Id
     */
    private Integer userId;
    /**
     * TCC的用户名称
     */
    private String userName;

    /**
     * 用户统计
     */
    private Integer statistics;

    private String email;

    private String telephone;


}