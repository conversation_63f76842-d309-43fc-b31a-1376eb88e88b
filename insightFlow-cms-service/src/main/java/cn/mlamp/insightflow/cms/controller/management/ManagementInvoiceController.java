package cn.mlamp.insightflow.cms.controller.management;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.model.query.ManagementInvoiceExportRequest;
import cn.mlamp.insightflow.cms.model.vo.ManagementInvoiceDetailVO;
import cn.mlamp.insightflow.cms.model.vo.ManagementInvoicePageVO;
import cn.mlamp.insightflow.cms.service.management.ManagementInvoiceService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/management/invoices")
@RequiredArgsConstructor
public class ManagementInvoiceController {
    private final ManagementInvoiceService managementInvoiceService;


    @GetMapping("/page")
    public RespBody<IPage<ManagementInvoicePageVO>> pageInvoices(@RequestParam(required = false) String condition,
                                                                 @RequestParam int current, @RequestParam int pageSize) {
        IPage<ManagementInvoicePageVO> vos = managementInvoiceService.pageInvoices(condition, current, pageSize);
        return RespBody.ok(vos);
    }

    @GetMapping
    public RespBody<ManagementInvoiceDetailVO> getInvoiceDetail(@RequestParam int id) {
        ManagementInvoiceDetailVO vo = managementInvoiceService.getInvoiceDetail(id);
        return RespBody.ok(vo);
    }

    @PutMapping("/status")
    public RespBody<Void> updateStatus(@RequestParam int id, @RequestParam String status) {
        managementInvoiceService.updateStatus(id, status);
        return RespBody.ok();
    }

    /**
     * 导出发票信息
     *
     * @param request
     * @param response
     */
    @PostMapping("/export")
    public void exportInvoices(@RequestBody ManagementInvoiceExportRequest request, HttpServletResponse response) {
        managementInvoiceService.exportInvoices(request, response);
    }
}
