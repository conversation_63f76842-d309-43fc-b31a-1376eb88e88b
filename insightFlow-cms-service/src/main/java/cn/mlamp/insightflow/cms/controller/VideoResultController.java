package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.annotation.HasMemberCheck;
import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.entity.CmsVideoResult;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.model.query.VideoResultRequest;
import cn.mlamp.insightflow.cms.service.IVideoResultService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: husuper
 * @CreateTime: 2025-04-21
 */
@Slf4j
@RequestMapping("/video/result/")
@RestController
@RequiredArgsConstructor
@Tag(name = "视频分析结果相关接口")
public class VideoResultController {

    @Autowired
    private IVideoResultService videoResultService;

    @PostMapping("/update")
    @Operation(summary = "编辑视频分镜分析结果接口")
    public RespBody<Boolean> update(@RequestBody List<VideoResultRequest> request) {
        return RespBody.ok(videoResultService.update(request));
    }


    @PostMapping("/back")
    @Operation(summary = "视频分析结果回退接口")
    public RespBody<Boolean> back(@RequestParam(value = "videoId", required = false) Integer videoId) {
        return RespBody.ok(videoResultService.back(videoId));
    }


    @GetMapping("reuslt/detail/export")
    @Operation(summary = "导出脚本接口")
    @HasMemberCheck
    public void exportReusltDetail(@RequestParam(value = "videoId", required = false) Integer videoId, HttpServletResponse response) {
        if (videoId == null) {
            throw new BusinessException("视频分析结果Id不能为空");
        }
        videoResultService.exportReusltDetail(videoId, response);
    }

    @PostMapping("/getSceneSplitResultByVideoId")
    @Operation(summary = "获取分镜信息")
    public RespBody<List<CmsVideoResult>> getSceneSplitResultByVideoId(@RequestParam(value = "videoId", required = false) Integer videoId) {
        return RespBody.ok(videoResultService.getSceneSplitResultByVideoId(videoId));
    }

}
