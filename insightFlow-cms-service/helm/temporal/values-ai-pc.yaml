imagePullSecrets:
  - name: "ai-pc-ai-pc-robot"

server:
  image:
    repository: hub.intra.mlamp.cn/ai-pc/temporalio-server
  frontend:
    resources:
      limits:
        cpu: "2"
        memory: 8Gi
      requests:
        cpu: "1"
        memory: 4Gi
  history:
    resources:
      limits:
        cpu: "4"
        memory: 16Gi
      requests:
        cpu: "2"
        memory: 8Gi
  matching:
    resources:
      limits:
        cpu: "4"
        memory: 16Gi
      requests:
        cpu: "2"
        memory: 8Gi
  worker:
    resources:
      limits:
        cpu: "4"
        memory: 16Gi
      requests:
        cpu: "2"
        memory: 8Gi
  dynamicConfig:
    # 历史大小限制（字节数，默认50MB）
    limit.historySize.error:
      - value: 262144000  # 250MB
        constraints: {}
    # 历史大小警告阈值（字节数，默认10MB）
    limit.historySize.warn:
      - value: 20971520   # 20MB
        constraints: {}
    # 历史事件数量限制（默认51200）
    limit.historyCount.error:
      - value: 100000
        constraints: {}
    # 历史事件数量警告阈值（默认10240）
    limit.historyCount.warn:
      - value: 20000
        constraints: {}
  config:
    persistence:
      default:
        driver: "sql"
        sql:
          driver: "mysql8"
          host: **********
          port: 3311
          database: temporal
          user: temporal
          password: RUEApvtjOk
          maxConns: 200
          maxIdleConns: 200
          maxConnLifetime: "1h"
      visibility:
        driver: "sql"
        sql:
          driver: "mysql8"
          host: **********
          port: 3311
          database: temporal_visibility
          user: temporal
          password: RUEApvtjOk
          maxConns: 200
          maxIdleConns: 200
          maxConnLifetime: "1h"

admintools:
  image:
    repository: hub.intra.mlamp.cn/ai-pc/temporalio-admin-tools
  resources:
    limits:
      cpu: "500m"
      memory: 1Gi
    requests:
      cpu: "250m"
      memory: 512Mi

web:
  image:
    repository: hub.intra.mlamp.cn/ai-pc/temporalio-ui
  service:
    type: ClusterIP
  resources:
    limits:
      cpu: 500m
      memory: 1Gi
    requests:
      cpu: 250m
      memory: 512Mi

elasticsearch:
  enabled: false

cassandra:
  enabled: false

schema:
  createDatabase:
    enabled: true
  setup:
    enabled: true
  update:
    enabled: true

mysql:
  enabled: true