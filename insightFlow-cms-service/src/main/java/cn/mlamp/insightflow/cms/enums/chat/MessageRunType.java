package cn.mlamp.insightflow.cms.enums.chat;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import javax.annotation.Nullable;

/**
 * 消息状态
 */
public enum MessageRunType implements IEnum<String> {

    MESSAGE("message"), // 消息

    TOOL("tool"), // 工具

    NODE("node"),

    ACTION("action"); // 节点

    @Getter
    private final String label;

    MessageRunType(String label) {
        this.label = label;
    }

    @JsonValue
    @Override
    public String getValue() {
        return this.label;
    }

    @Nullable
    @JsonCreator
    public static MessageRunType from(@Nullable String value) {
        if (value == null) {
            return null;
        }
        for (MessageRunType type : MessageRunType.values()) {
            if (type.label.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("MessageRunType Unknown label: " + value);
    }

}
