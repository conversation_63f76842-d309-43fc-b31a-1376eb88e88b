package cn.mlamp.insightflow.cms.entity.chat;

import java.util.Date;

import cn.mlamp.insightflow.cms.graph.entities.EnvInfo;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import cn.mlamp.insightflow.cms.enums.chat.MessageStatus;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

@TableName(value = "message", autoResultMap = true)
@Data
public class Message {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "message_id")
    private String messageId;

    @TableField(value = "conversation_id")
    private Integer conversationId;

    @TableField(value = "tenant_id")
    private Integer tenantId;

    @TableField(value = "user_id")
    private Integer userId;

    @TableField(value = "env", typeHandler = JacksonTypeHandler.class)
    private EnvInfo env;

    @TableField(value = "query")
    private String query;

    @TableField(value = "answer")
    private String answer;

    @TableField(value = "error")
    private String error;

    @TableField(value = "status")
    private MessageStatus status;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "update_time")
    private Date updateTime;

}
