apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "insight-flow-cms.worker.network.fullname" . }}
  labels:
    {{ include "insight-flow-cms.labels" . | nindent 4 }}
    component: worker-network
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.worker.customer.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{ include "insight-flow-cms.selectorLabels" . | nindent 6 }}
      component: worker-network
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{ toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{ include "insight-flow-cms.labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{ toYaml . | nindent 8 }}
        {{- end }}
        component: worker-network
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{ toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{ toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        {{ include "insight-flow-cms.s3fs-sidercar" . | nindent 8 }}
        - name: worker
          securityContext:
            {{ toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            {{ include "insight-flow-cms.worker.env" . | nindent 12 }}
          {{- if .Values.worker.network.extraEnv }}
          {{ toYaml .Values.worker.network.extraEnv | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          resources:
            {{ toYaml .Values.worker.network.resources | nindent 12 }}
          volumeMounts:
            {{ include "insight-flow-cms.worker.volumeMounts" . | nindent 12 }}
          {{- with .Values.worker.network.volumeMounts }}
            {{ toYaml . | nindent 12 }}
          {{- end }}
      volumes:
        - name: config
          configMap:
            name: {{ include "insight-flow-cms.worker.network.fullname" . }}-config
            items:
              - key: application-{{ .Release.Name }}.yml
                path: application-{{ .Release.Name }}.yml
        {{ include "insight-flow-cms.worker.volumes" . | nindent 8 }}
      {{- with .Values.worker.network.volumes }}
        {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.worker.network.nodeSelector }}
      nodeSelector:
        {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.worker.network.affinity }}
      affinity:
        {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.worker.network.tolerations }}
      tolerations:
        {{ toYaml . | nindent 8 }}
      {{- end }}
