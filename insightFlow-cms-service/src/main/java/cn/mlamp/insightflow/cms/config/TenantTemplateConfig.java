package cn.mlamp.insightflow.cms.config;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.mlamp.insightflow.cms.auth.cms.bo.TenantTemplateBO;
import cn.mlamp.insightflow.cms.enums.ErrorCode;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.util.ValidatorUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 租户模板 Config
 *
 * <AUTHOR>
 * @since 2022-09-16 15:49:51
 */
@Slf4j
@Component
public class TenantTemplateConfig {

    /**
     * Ttc自助注册、自助激活模板
     */
    @Autowired
    private TenantTemplateConfigProperties tenantTemplateConfigProperties;

    /**
     * 给具有继承关系的包装类增加租户模版参数
     *
     * @param t   具有继承关系的包装类
     * @param <T> TenantTemplateBO
     * @return 添加好租户模板参数之后的包装类
     */
    public <T extends TenantTemplateBO> T addTenantTemplateArgs(T t) {

        populateTenantTemplateBO(t, tenantTemplateConfigProperties);
        return t;
    }

    private <T extends TenantTemplateBO> void populateTenantTemplateBO(T t,
                                                                       TenantTemplateConfigProperties tenantTemplate) {
        t.setIsForceCreateTenant(tenantTemplate.getIsForceCreateTenant());
        DateTime dateTime = DateUtil.offsetDay(new Date(), tenantTemplate.getExpiredDay());
        t.setExpiredDay(tenantTemplate.getExpiredDay());
        t.setExpiredDate(dateTime.toJdkDate());
        t.setResources(tenantTemplate.getResources());
        t.setPermissions(tenantTemplate.getPermissions());
        t.setQuotas(tenantTemplate.getQuotas());
        t.setUserCountLimit(tenantTemplate.getUserCountLimit());
        t.setTenantTagList(tenantTemplate.getTenantTagList());
        t.setRemark(tenantTemplate.getRemark());
    }

}
