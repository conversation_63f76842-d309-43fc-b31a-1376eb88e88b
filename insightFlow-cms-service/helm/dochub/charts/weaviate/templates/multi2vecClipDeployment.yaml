{{ if index .Values "modules" "multi2vec-clip" "enabled" }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ index .Values "modules" "multi2vec-clip" "fullnameOverride" }}
  labels:
    name: {{ index .Values "modules" "multi2vec-clip" "fullnameOverride" }}
    app: {{ index .Values "modules" "multi2vec-clip" "fullnameOverride" }}
    app.kubernetes.io/name: weaviate
    app.kubernetes.io/managed-by: helm
spec:
  replicas: {{ index .Values "modules" "multi2vec-clip" "replicas" }}
  selector:
    matchLabels:
      app: {{ index .Values "modules" "multi2vec-clip" "fullnameOverride" }}
  template:
    metadata:
      labels:
        app: {{ index .Values "modules" "multi2vec-clip" "fullnameOverride" }}
    spec:
      {{- if (index .Values "modules" "multi2vec-clip" "serviceAccountName") }}
      serviceAccountName: {{ index .Values "modules" "multi2vec-clip" "serviceAccountName" }}
      {{- else if (index .Values "serviceAccountName") }}
      serviceAccountName: {{ index .Values "serviceAccountName" }}
      {{- end }}
      containers:
      - name: {{ index .Values "modules" "multi2vec-clip" "fullnameOverride" }}
        image: {{ index .Values "modules" "multi2vec-clip" "registry" }}/{{ index .Values "modules" "multi2vec-clip" "repo" }}:{{ index .Values "modules" "multi2vec-clip" "tag" }}
        env:
          - name: ENABLE_CUDA
            value: "{{ if index .Values "modules" "multi2vec-clip" "envconfig" "enable_cuda" }}1{{ else }}0{{ end }}"
          {{ if index .Values "modules" "multi2vec-clip" "envconfig" "enable_cuda" }}
          - name: NVIDIA_VISIBLE_DEVICES
            value: {{ index .Values "modules" "multi2vec-clip" "envconfig" "nvidia_visible_devices"}}
          - name: NVIDIA_DRIVER_CAPABILITIES
            value: {{ index .Values "modules" "multi2vec-clip" "envconfig" "nvidia_driver_capabilities"}}
          - name: LD_LIBRARY_PATH
            value: {{ index .Values "modules" "multi2vec-clip" "envconfig" "ld_library_path"}}
          {{ end }}
        resources:
{{ index .Values "modules" "multi2vec-clip" "resources" | toYaml | indent 10 }}
        livenessProbe:
          httpGet:
            path: /.well-known/live
            port: 8080
          initialDelaySeconds: {{ index .Values "modules" "multi2vec-clip" "livenessProbe" "initialDelaySeconds" }}
          periodSeconds: {{ index .Values "modules" "multi2vec-clip" "livenessProbe" "periodSeconds" }}
          timeoutSeconds: {{ index .Values "modules" "multi2vec-clip" "livenessProbe" "timeoutSeconds" }}
        readinessProbe:
          httpGet:
            path: /.well-known/ready
            port: 8080
          initialDelaySeconds: {{ index .Values "modules" "multi2vec-clip" "readinessProbe" "initialDelaySeconds" }}
          periodSeconds: {{ index .Values "modules" "multi2vec-clip" "readinessProbe" "periodSeconds" }}
      {{- with index .Values "modules" "multi2vec-clip" "nodeSelector" | default .Values.nodeSelector }}
      nodeSelector:
        {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with index .Values "modules" "multi2vec-clip" "affinity" | default .Values.affinity }}
      affinity:
        {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with index .Values "modules" "multi2vec-clip" "tolerations" | default .Values.tolerations }}
      tolerations:
        {{ toYaml . | nindent 8 }}
      {{- end }}
{{ end }}
