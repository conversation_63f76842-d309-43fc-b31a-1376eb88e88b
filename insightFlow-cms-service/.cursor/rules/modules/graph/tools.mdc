---
description:
globs: src/main/java/cn/mlamp/insightflow/cms/graph/tools/**/*.java
alwaysApply: false
---
# Tools 工具调用与参数补全机制详细说明

## 概述
Graph系统中的Tools是Agent节点执行具体业务逻辑的工具集合。本文档说明如何在工具调用中集成参数缺失检测和自动参数补全机制，确保工具能够智能地识别缺失参数并触发用户友好的参数收集流程。

## 核心设计理念

### 1. 工具与Agent协作
- Tools作为Agent节点的核心执行组件
- 支持参数缺失时的智能检测和处理
- 与 [SupervisorNode](mdc:.cursor/rules/modules/graph/supervisor.mdc) 和 [ParameterExtractorNode](mdc:.cursor/rules/modules/graph/parameter_extractor.mdc) 协作
- 提供用户友好的参数补全体验

### 2. 参数补全集成方式
- **响应字段集成**：在Agent Response中添加 `ParameterMissingRequest` 字段
- **状态驱动**：通过CMSGraphState管理参数补全流程
- **类型安全**：使用强类型参数规格定义
- **智能检测**：工具内部逻辑自动识别参数缺失

## Agent响应增强设计

### 1. Agent响应类增强

Agent的Response类需要支持参数缺失请求：

```java
@Data
public static class AIScriptResponse {

    @Description("生成的AI脚本ID")
    private String scriptId;

    @Description("如果无法生成AI脚本, 提示需要用户补全的参数或错误信息")
    private String error;
    
    @Description("参数缺失请求，当工具调用发现缺失必需参数时使用")
    private ParameterMissingRequest parameterMissingRequest;

    public String toPrettyString() {
        return JSONUtil.toJsonStr(this);
    }

    public boolean isError() {
        return StrUtil.isNotBlank(error);
    }
    
    public boolean hasParameterMissingRequest() {
        return parameterMissingRequest != null;
    }
}
```

### 2. Agent节点处理逻辑

Agent节点需要检查响应中的参数缺失请求：

```java
@Override
protected Map<String, Object> run(String runId, String goal, CMSGraphState state, CallbackManager callbackManager) throws Exception {
    
    // 1. 检查是否有完整参数（来自参数补全流程）
    Optional<Map<String, Object>> completeParams = getCompleteParams(state);
    if (completeParams.isPresent()) {
        // 将完整参数传递给Agent处理
        return executeWithCompleteParams(goal, completeParams.get(), state, callbackManager);
    }
    
    // 2. 正常执行Agent
    final Result<AIScriptResponse> agentResult = chat(goal, context.toString(), callbackManager);
    final AIScriptResponse response = agentResult.content();
    
    // 3. 检查是否有参数缺失请求
    if (response.hasParameterMissingRequest()) {
        return handleParameterMissingRequest(response.getParameterMissingRequest(), state);
    }
    
    // 4. 检查是否有错误
    if (response.isError()) {
        return Map.of(
            CMSGraphState.RESPONSE_KEY, response.getError(),
            CMSGraphState.AI_SCRIPT_RESULT, response,
            CMSGraphState.CONTEXT_KEY, context,
            CMSGraphState.IS_DIRECT_RETURN_KEY, true
        );
    }
    
    // 5. 正常返回结果
    return Map.of(
        CMSGraphState.RESPONSE_KEY, response.toPrettyString(),
        CMSGraphState.AI_SCRIPT_RESULT, response,
        CMSGraphState.CONTEXT_KEY, context
    );
}

/**
 * 处理Agent响应中的参数缺失请求
 */
private Map<String, Object> handleParameterMissingRequest(
    ParameterMissingRequest parameterMissingRequest, 
    CMSGraphState state) {
    
    // 提交参数缺失请求到状态中
    Map<String, Object> result = new HashMap<>();
    result.put(CMSGraphState.PARAMETER_MISSING_REQUEST_KEY, parameterMissingRequest);
    result.put(CMSGraphState.NEXT_KEY, SupervisorNode.NAME); // 返回supervisor处理
    
    return result;
}
```

## 工具实现最佳实践

### 1. 参数检测模式

#### 基础参数检测
```java
@Tool("仿照现有视频创建仿写视频脚本任务, 如果参数信息不完全, 则告知用户需要补充信息")
public String createAiScriptTask(
        @P("视频ID") Integer videoId,
        @P("仿写品牌") String brand,
        @P("仿写产品") String product,
        @P("仿写产品卖点") String sellingPoint,
        @P("仿写的开始的黄金3s悬念") String startGold3s
) {
    // 1. 检测参数缺失
    List<ParameterMissingRequest.ParameterSpec> missingParams = detectMissingParams(
        videoId, brand, product, sellingPoint, startGold3s);
    
    if (!missingParams.isEmpty()) {
        // 2. 创建参数缺失请求
        ParameterMissingRequest request = ParameterMissingRequest.builder()
            .targetNode("aiScript")  // 当前节点名称
            .originalGoal("创建AI脚本任务")
            .missingParams(missingParams)
            .existingParams(buildExistingParams(videoId, brand, product, sellingPoint, startGold3s))
            .build();
        
        // 3. 通过特殊标记返回参数缺失请求
        return "PARAMETER_MISSING_REQUEST:" + JSONUtil.toJsonStr(request);
    }
    
    // 4. 正常执行工具逻辑
    return executeAiScriptTask(videoId, brand, product, sellingPoint, startGold3s);
}

private List<ParameterMissingRequest.ParameterSpec> detectMissingParams(
    Integer videoId, String brand, String product, String sellingPoint, String startGold3s) {
    
    List<ParameterMissingRequest.ParameterSpec> missing = new ArrayList<>();
    
    if (videoId == null) {
        missing.add(ParameterMissingRequest.ParameterSpec.builder()
            .name("videoId")
            .displayName("视频ID")
            .description("要仿写的原视频ID")
            .type(ParameterMissingRequest.ParameterType.INTEGER)
            .required(true)
            .example("12345")
            .hint("请提供要仿写的视频ID")
            .build());
    }
    
    if (StrUtil.isBlank(brand)) {
        missing.add(ParameterMissingRequest.ParameterSpec.builder()
            .name("brand")
            .displayName("仿写品牌")
            .description("仿写脚本中的品牌名称")
            .type(ParameterMissingRequest.ParameterType.STRING)
            .required(true)
            .example("完美日记")
            .hint("请输入您要仿写的品牌名称")
            .build());
    }
    
    if (StrUtil.isBlank(product)) {
        missing.add(ParameterMissingRequest.ParameterSpec.builder()
            .name("product")
            .displayName("仿写产品")
            .description("仿写脚本中的产品名称")
            .type(ParameterMissingRequest.ParameterType.STRING)
            .required(true)
            .example("口红")
            .hint("请输入您要仿写的产品名称")
            .build());
    }
    
    if (StrUtil.isBlank(sellingPoint)) {
        missing.add(ParameterMissingRequest.ParameterSpec.builder()
            .name("sellingPoint")
            .displayName("产品卖点")
            .description("仿写脚本中要突出的产品卖点")
            .type(ParameterMissingRequest.ParameterType.STRING)
            .required(true)
            .example("持久不脱色")
            .hint("请输入产品的主要卖点")
            .build());
    }
    
    if (StrUtil.isBlank(startGold3s)) {
        missing.add(ParameterMissingRequest.ParameterSpec.builder()
            .name("startGold3s")
            .displayName("黄金3秒开头")
            .description("视频开头的黄金3秒悬念内容")
            .type(ParameterMissingRequest.ParameterType.STRING)
            .required(true)
            .example("你还在为口红不持久而烦恼吗？")
            .hint("请输入吸引人的开头悬念")
            .build());
    }
    
    return missing;
}
```

### 2. Agent增强实现

#### Agent接口增强
```java
interface AIScriptAgent {

    @SystemMessage("""
            {{systemMessage}}
            
            当前环境:
            {{environmentInfo}}
            
            当前上下文信息:
            {{context}}
            
            # 参数补全指南
            当工具调用发现参数缺失时，你需要在响应中设置 parameterMissingRequest 字段，
            而不是直接询问用户。系统会自动处理参数补全流程。
            
            如果工具返回 "PARAMETER_MISSING_REQUEST:" 开头的字符串，
            请解析其中的JSON内容并设置到 parameterMissingRequest 字段中。
            """)
    Result<AIScriptResponse> run(
            @V("systemMessage") String systemMessage,
            @V("environmentInfo") String environmentInfo,
            @V("context") String context,
            @dev.langchain4j.service.UserMessage String message
    );
}
```

#### Agent响应处理逻辑
```java
@Override
protected Map<String, Object> run(String runId, String goal, CMSGraphState state, CallbackManager callbackManager) throws Exception {
    final CMSGraphContext context = state.getContext();
    
    // 1. 检查是否有完整参数
    Optional<Map<String, Object>> completeParams = getCompleteParams(state);
    if (completeParams.isPresent()) {
        // 将完整参数注入到工具调用环境中
        return executeWithCompleteParams(goal, completeParams.get(), context, callbackManager);
    }
    
    // 2. 正常执行Agent
    final Result<AIScriptResponse> agentResult = chat(goal, context.toString(), callbackManager);
    final AIScriptResponse response = agentResult.content();
    
    // 3. 检查Agent是否通过工具发现了参数缺失
    if (response.hasParameterMissingRequest()) {
        log.info("Agent发现参数缺失，触发参数补全流程");
        return handleParameterMissingFromAgent(response.getParameterMissingRequest(), state);
    }
    
    // 4. 处理正常响应和错误
    final AIScriptContext aiScriptContext = context.getAiScriptContext();
    aiScriptContext.setAiScript(response);
    
    if (response.isError()) {
        return Map.of(
                CMSGraphState.RESPONSE_KEY, response.getError(),
                CMSGraphState.AI_SCRIPT_RESULT, response,
                CMSGraphState.CONTEXT_KEY, context,
                CMSGraphState.IS_DIRECT_RETURN_KEY, true
        );
    }

    return Map.of(
            CMSGraphState.RESPONSE_KEY, response.toPrettyString(),
            CMSGraphState.AI_SCRIPT_RESULT, response,
            CMSGraphState.CONTEXT_KEY, context
    );
}

/**
 * 处理Agent通过工具发现的参数缺失
 */
private Map<String, Object> handleParameterMissingFromAgent(
    ParameterMissingRequest parameterMissingRequest, 
    CMSGraphState state) {
    
    // 提交参数缺失请求
    Map<String, Object> result = new HashMap<>();
    result.put(CMSGraphState.PARAMETER_MISSING_REQUEST_KEY, parameterMissingRequest);
    result.put(CMSGraphState.NEXT_KEY, SupervisorNode.NAME);
    
    return result;
}

/**
 * 使用完整参数执行任务
 */
private Map<String, Object> executeWithCompleteParams(
    String goal, 
    Map<String, Object> completeParams, 
    CMSGraphContext context, 
    CallbackManager callbackManager) throws Exception {
    
    // 将完整参数注入到Agent执行环境中
    String enhancedGoal = goal + "\n\n完整参数: " + JSONUtil.toJsonStr(completeParams);
    
    final Result<AIScriptResponse> agentResult = chat(enhancedGoal, context.toString(), callbackManager);
    final AIScriptResponse response = agentResult.content();
    
    // 处理响应
    final AIScriptContext aiScriptContext = context.getAiScriptContext();
    aiScriptContext.setAiScript(response);
    
    return Map.of(
            CMSGraphState.RESPONSE_KEY, response.toPrettyString(),
            CMSGraphState.AI_SCRIPT_RESULT, response,
            CMSGraphState.CONTEXT_KEY, context
    );
}
```

## 工具参数传递机制

### 1. 完整参数注入

当参数补全完成后，工具需要接收完整的参数：

```java
@Tool("使用完整参数创建AI脚本任务")
public String createAiScriptTaskWithParams(
        @P("完整参数JSON字符串") String completeParamsJson
) {
    try {
        // 解析完整参数
        Map<String, Object> params = JSONUtil.toBean(completeParamsJson, Map.class);
        
        Integer videoId = (Integer) params.get("videoId");
        String brand = (String) params.get("brand");
        String product = (String) params.get("product");
        String sellingPoint = (String) params.get("sellingPoint");
        String startGold3s = (String) params.get("startGold3s");
        
        // 执行实际的业务逻辑
        return executeAiScriptTask(videoId, brand, product, sellingPoint, startGold3s);
        
    } catch (Exception e) {
        log.error("解析完整参数失败", e);
        return "参数解析失败: " + e.getMessage();
    }
}
```

### 2. 参数验证增强

```java
private List<ParameterMissingRequest.ParameterSpec> validateAndDetectMissing(
    Integer videoId, String brand, String product, String sellingPoint, String startGold3s) {
    
    List<ParameterMissingRequest.ParameterSpec> missing = new ArrayList<>();
    
    // 必填参数检查
    if (videoId == null) {
        missing.add(createRequiredParam("videoId", "视频ID", "要仿写的原视频ID", 
            ParameterMissingRequest.ParameterType.INTEGER, "12345"));
    } else {
        // 业务逻辑验证
        if (!videoExists(videoId)) {
            missing.add(createValidationParam("videoId", "视频ID", "视频ID不存在，请提供有效的视频ID", 
                ParameterMissingRequest.ParameterType.INTEGER, "12345"));
        }
    }
    
    if (StrUtil.isBlank(brand)) {
        missing.add(createRequiredParam("brand", "仿写品牌", "仿写脚本中的品牌名称", 
            ParameterMissingRequest.ParameterType.STRING, "完美日记"));
    }
    
    if (StrUtil.isBlank(product)) {
        missing.add(createRequiredParam("product", "仿写产品", "仿写脚本中的产品名称", 
            ParameterMissingRequest.ParameterType.STRING, "口红"));
    }
    
    // 可选参数的智能建议
    if (StrUtil.isBlank(sellingPoint)) {
        missing.add(createOptionalParam("sellingPoint", "产品卖点", "建议提供产品卖点以提高脚本质量", 
            ParameterMissingRequest.ParameterType.STRING, "持久不脱色"));
    }
    
    return missing;
}

private ParameterMissingRequest.ParameterSpec createRequiredParam(
    String name, String displayName, String description, 
    ParameterMissingRequest.ParameterType type, String example) {
    
    return ParameterMissingRequest.ParameterSpec.builder()
        .name(name)
        .displayName(displayName)
        .description(description)
        .type(type)
        .required(true)
        .example(example)
        .hint("此参数为必填项")
        .build();
}
```

## 高级特性支持

### 1. 智能参数推荐

```java
private void addSmartRecommendations(List<ParameterMissingRequest.ParameterSpec> specs, Integer videoId) {
    if (videoId != null) {
        // 基于视频内容智能推荐参数
        VideoInfo videoInfo = getVideoInfo(videoId);
        if (videoInfo != null) {
            // 推荐品牌
            specs.stream()
                .filter(spec -> "brand".equals(spec.getName()))
                .forEach(spec -> {
                    if (videoInfo.getBrand() != null) {
                        spec.setExample(videoInfo.getBrand());
                        spec.setHint("建议使用原视频品牌: " + videoInfo.getBrand());
                    }
                });
            
            // 推荐产品
            specs.stream()
                .filter(spec -> "product".equals(spec.getName()))
                .forEach(spec -> {
                    if (videoInfo.getProduct() != null) {
                        spec.setExample(videoInfo.getProduct());
                        spec.setHint("建议使用原视频产品: " + videoInfo.getProduct());
                    }
                });
        }
    }
}
```

### 2. 参数依赖关系

```java
private List<ParameterMissingRequest.ParameterSpec> handleParameterDependencies(
    Map<String, Object> existingParams) {
    
    List<ParameterMissingRequest.ParameterSpec> specs = new ArrayList<>();
    
    String brand = (String) existingParams.get("brand");
    if (StrUtil.isNotBlank(brand)) {
        // 根据品牌推荐产品选项
        List<String> products = getProductsByBrand(brand);
        if (!products.isEmpty()) {
            specs.add(ParameterMissingRequest.ParameterSpec.builder()
                .name("product")
                .displayName("产品类型")
                .description("选择" + brand + "品牌下的产品")
                .type(ParameterMissingRequest.ParameterType.ENUM)
                .enumValues(products)
                .required(true)
                .hint("请从" + brand + "的产品中选择")
                .build());
        }
    }
    
    return specs;
}
```

### 3. 批量参数处理

```java
@Tool("批量处理多个脚本生成任务")
public String batchCreateAiScriptTasks(
        @P("批量任务配置JSON数组") String batchConfigJson
) {
    try {
        List<Map<String, Object>> configs = JSONUtil.toList(batchConfigJson, Map.class);
        List<ParameterMissingRequest.ParameterSpec> allMissingParams = new ArrayList<>();
        
        for (int i = 0; i < configs.size(); i++) {
            Map<String, Object> config = configs.get(i);
            List<ParameterMissingRequest.ParameterSpec> missing = validateBatchConfig(config, i);
            allMissingParams.addAll(missing);
        }
        
        if (!allMissingParams.isEmpty()) {
            ParameterMissingRequest request = ParameterMissingRequest.builder()
                .targetNode("aiScript")
                .originalGoal("批量创建AI脚本任务")
                .missingParams(allMissingParams)
                .existingParams(Map.of("batchConfigs", configs))
                .build();
            
            return "PARAMETER_MISSING_REQUEST:" + JSONUtil.toJsonStr(request);
        }
        
        // 执行批量任务
        return executeBatchAiScriptTasks(configs);
        
    } catch (Exception e) {
        return "批量任务处理失败: " + e.getMessage();
    }
}
```

## 错误处理与监控

### 1. 参数补全异常处理

```java
@Tool("容错的AI脚本创建工具")
public String createAiScriptTaskWithFallback(
        @P("视频ID") Integer videoId,
        @P("仿写品牌") String brand,
        @P("仿写产品") String product
) {
    try {
        // 尝试自动补全部分参数
        Map<String, Object> autoFilledParams = autoFillParameters(videoId, brand, product);
        
        // 检查剩余缺失参数
        List<ParameterMissingRequest.ParameterSpec> missingParams = 
            detectMissingParamsWithAutoFill(autoFilledParams);
        
        if (!missingParams.isEmpty()) {
            // 设置降级提示
            missingParams.forEach(spec -> {
                if (!spec.isRequired()) {
                    spec.setHint(spec.getHint() + "（可选，系统将使用默认值）");
                }
            });
            
            ParameterMissingRequest request = ParameterMissingRequest.builder()
                .targetNode("aiScript")
                .originalGoal("创建AI脚本任务（支持自动补全）")
                .missingParams(missingParams)
                .existingParams(autoFilledParams)
                .errorMessage("部分参数已自动补全，请确认或补充其他参数")
                .build();
            
            return "PARAMETER_MISSING_REQUEST:" + JSONUtil.toJsonStr(request);
        }
        
        return executeAiScriptTaskWithDefaults(autoFilledParams);
        
    } catch (Exception e) {
        log.error("AI脚本创建失败", e);
        return "任务创建失败，请检查参数后重试: " + e.getMessage();
    }
}
```

### 2. 性能监控

```java
@Tool("带性能监控的AI脚本创建")
public String createAiScriptTaskWithMonitoring(
        @P("视频ID") Integer videoId,
        @P("仿写品牌") String brand
) {
    long startTime = System.currentTimeMillis();
    String runId = UUID.randomUUID().toString();
    
    try {
        // 参数验证阶段
        long validateStart = System.currentTimeMillis();
        List<ParameterMissingRequest.ParameterSpec> missingParams = 
            detectMissingParams(videoId, brand, null, null, null);
        long validateTime = System.currentTimeMillis() - validateStart;
        
        log.info("参数验证耗时: {}ms, runId: {}", validateTime, runId);
        
        if (!missingParams.isEmpty()) {
            ParameterMissingRequest request = ParameterMissingRequest.builder()
                .targetNode("aiScript")
                .originalGoal("创建AI脚本任务")
                .missingParams(missingParams)
                .existingParams(Map.of(
                    "videoId", videoId, 
                    "brand", brand,
                    "performance", Map.of(
                        "runId", runId,
                        "validateTime", validateTime
                    )
                ))
                .build();
            
            return "PARAMETER_MISSING_REQUEST:" + JSONUtil.toJsonStr(request);
        }
        
        // 执行任务
        String result = executeAiScriptTask(videoId, brand, null, null, null);
        long totalTime = System.currentTimeMillis() - startTime;
        
        log.info("任务执行完成，总耗时: {}ms, runId: {}", totalTime, runId);
        return result;
        
    } catch (Exception e) {
        long errorTime = System.currentTimeMillis() - startTime;
        log.error("任务执行异常，耗时: {}ms, runId: {}", errorTime, runId, e);
        throw e;
    }
}
```

## 最佳实践总结

### 1. 工具设计原则
- **参数检测优先**：工具调用前优先检测参数完整性
- **用户体验友好**：提供清晰的参数描述和示例
- **智能推荐**：基于上下文和历史数据智能推荐参数值
- **优雅降级**：支持部分参数缺失时的降级处理

### 2. Agent集成要点
- **响应增强**：在Agent Response中添加 `ParameterMissingRequest` 字段
- **状态检查**：优先检查是否有来自参数补全流程的完整参数
- **错误处理**：区分参数缺失、业务错误和系统异常
- **性能优化**：缓存验证结果，减少重复计算

### 3. 参数补全流程
- **自动触发**：工具内部逻辑自动检测并触发参数补全
- **智能引导**：提供上下文相关的参数提示和示例
- **批量支持**：支持一次性收集多个缺失参数
- **取消机制**：支持用户随时跳出参数补全流程

### 4. 错误处理策略
- **分层验证**：参数格式、业务逻辑、系统资源多层验证
- **友好提示**：提供具体、可操作的错误信息
- **自动恢复**：支持从参数错误中自动恢复
- **监控告警**：关键错误的实时监控和告警

这种设计确保了工具与参数补全机制的深度集成，为用户提供了智能、友好的参数收集体验，同时保持了系统的健壮性和可扩展性。
