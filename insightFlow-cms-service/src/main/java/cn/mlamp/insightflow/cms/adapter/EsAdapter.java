package cn.mlamp.insightflow.cms.adapter;

import cn.mlamp.insightflow.cms.config.Es7Config;
import cn.mlamp.insightflow.cms.constant.Constants;
import cn.mlamp.insightflow.cms.util.JsonUtil;
import cn.mlamp.insightflow.cms.util.RestHighLevelClientBuilder;

import com.google.common.collect.Lists;
import io.micrometer.core.annotation.Timed;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.search.MultiSearchRequest;
import org.elasticsearch.action.search.MultiSearchResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;


import java.io.IOException;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-06-28 16:49:06
 */

@Component
@EnableRetry
@Slf4j
public class EsAdapter {
    private static final int RETRY_LIMIT = 3;
    private static final int RETRY_SLEEP_MS = 30000;

    private final Es7Config es7Config;

    public static RestHighLevelClient es7Client;

    public EsAdapter(Es7Config es7Config) {
        this.es7Config = es7Config;
    }

    @PostConstruct
    public void init() {
        try {
            es7Client = RestHighLevelClientBuilder.build(es7Config);
        } catch (Exception e) {
            log.error("build restHighLevelClient exception", e);
            throw new RuntimeException(e);
        }
    }

    @PreDestroy
    public void preDestroy() {
        try {
            es7Client.close();
        } catch (IOException e) {
            log.error("close es 7 client error.", e);
        }
    }

    @Timed(value = "es_bulk", percentiles = {0.99D, 0.999D})
    @Retryable(value = {Exception.class}, maxAttempts = RETRY_LIMIT, backoff = @Backoff(delay = RETRY_SLEEP_MS))
    public BulkResponse bulk(BulkRequest request) throws Exception {
        request.timeout(new TimeValue(es7Config.getBulkTimeout()));
        return es7Client.bulk(request, RequestOptions.DEFAULT);
    }

    @Timed(value = "es_msearch", percentiles = {0.99D, 0.999D})
    @Retryable(value = {Exception.class}, maxAttempts = RETRY_LIMIT, backoff = @Backoff(delay = RETRY_SLEEP_MS))
    public SearchHit[] searchIds(Map<String, List<String>> indexIds) throws Exception {
        MultiSearchRequest request = new MultiSearchRequest();
        for (String index : indexIds.keySet()) {
            List<String> ids = indexIds.get(index);
            List<List<String>> idsPartitions = Lists.partition(ids, Constants.SEARCH_SIZE);
            for (List<String> idsPartition : idsPartitions) {
                SearchRequest searchRequest = new SearchRequest(index);
                SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
                searchSourceBuilder.size(10000);
                searchSourceBuilder.query(QueryBuilders.termsQuery("_id", idsPartition));
                searchRequest.source(searchSourceBuilder);
                request.add(searchRequest);
            }
        }
        MultiSearchResponse.Item[] responses = es7Client.msearch(request, RequestOptions.DEFAULT).getResponses();
        LinkedList<SearchHit> searchHits = Lists.newLinkedList();
        for (MultiSearchResponse.Item response : responses) {
            try {
                if (response.isFailure()) {
                    log.error("es search error response:{}, indexIds:{}", response.getFailureMessage(),
                            JsonUtil.encode(indexIds));
                    continue;
                }
                searchHits.addAll(Arrays.asList(response.getResponse().getHits().getHits()));
            } catch (Exception e) {
                log.error("es search error response:{}, indexIds:{}", response, JsonUtil.encode(indexIds), e);
            }
        }
        return searchHits.toArray(new SearchHit[0]);
    }

}
