package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 视频检查任务表
 *
 * <AUTHOR> system
 * @date : 2025-6-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_video_check_task")
public class CmsVideoCheckTask extends BaseEntity {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 数美请求唯一标识
     */
    private String requestId;

    /**
     * 客户侧请求唯一标识
     */
    private String btId;

    /**
     * 视频URL
     */
    private String videoUrl;

    /**
     * 视频ID（用于查询关联）
     */
    private String videoId;

    /**
     * 任务类型（用于查询分类，具体含义待定义）
     */
    private Integer taskType;

    /**
     * 任务状态（1:提交中 2:分析中 3:成功 4:失败）
     */
    private Integer taskStatus;

    /**
     * 请求参数（JSON格式）
     */
    private String requestParams;

    /**
     * 回调结果（JSON格式）
     */
    private String callbackResult;

    /**
     * 风险等级（PASS:正常 REVIEW:可疑 REJECT:违规）
     */
    private String riskLevel;

    /**
     * 风险描述
     */
    private String riskDescription;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 创建人id
     */
    private Integer userId;

    /**
     * 租户Id
     */
    private Integer tenantId;
}
