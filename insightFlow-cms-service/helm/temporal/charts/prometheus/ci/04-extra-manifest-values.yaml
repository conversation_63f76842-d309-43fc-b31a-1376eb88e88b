---
## Test case: set extra manifests to deploy
extraManifests:
  - |
    apiVersion: v1
    kind: ConfigMap
    metadata:
      labels:
        ci: "true"
      name: prometheus-extra-cm-first
    data:
      GREETING: "hello"
  - |
    apiVersion: v1
    kind: ConfigMap
    metadata:
      labels:
        ci: "true"
      name: prometheus-extra-cm-second
    data:
      prometheus.txt: "{{ include "prometheus.server.fullname" . }}"
    immutable: true
