package cn.mlamp.insightflow.cms.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 会员权益枚举
 */
@RequiredArgsConstructor
public enum MemberRightEnum {
    // 会员到期时间
    EXPIRE_TIME("expireTime", "last_member_expire_time"),

    // 视频解析次数
    VIDEO_ANALYSIS("videoAnalysis", "video_analysis"),

    // 脚本生成次数
    SCRIPT("script", "script"),

    // 素材库专有存储空间，单位byte
    DAM_CAPACITY("damCapacity", "dam_capacity"),

    // 素材ai自动打标次数
    DAM_AI_MARK("damAiMark", "dam_ai_mark"),

    // 视频生成时长，单位秒
    VIDEO_DURATION("videoDuration", "video_duration"),

    // 视频背景音乐合成次数
    @Deprecated
    VIDEO_BGM("videoBgm", "video_bgm"),

    // 文本台词转语音次数
    @Deprecated
    TTS("tts", "tts"),

    // 爆款营销素材分镜拆解浏览，无限次/无次数
    HOT_STORYBOARD("hotStoryboard", "hot_storyboard"),

    // 黄金3s套路总结浏览，无限次/无次数
    GOLD3S_SUMMARY("gold3sSummary", "gold3s_summary"),

    // 营销视频合成，无限次/无次数
    VIDEO_SYNTHESIS("videoSynthesis", "video_synthesis")
    ;

    /**
     * 权益名称
     */
    @Getter
    private final String rightName;

    /**
     * cms_member, cms_member_order, cms_tenant_member, cms_tenant_member_right_statistic 中，权益对应的列名称
     */
    @Getter
    private final String columnName;
}