package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.model.query.BatchDeleteCollectionRequest;
import cn.mlamp.insightflow.cms.model.query.CmsShareCollectionRequest;
import cn.mlamp.insightflow.cms.model.query.CmsTaskCollectionRequest;
import cn.mlamp.insightflow.cms.model.vo.CmsTaskCollectionVO;
import cn.mlamp.insightflow.cms.service.CmsCollectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 收藏相关接口
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/collect")
@Tag(name = "收藏相关接口")
public class CmsCollectController {

    private final CmsCollectService cmsCollectService;

    /**
     * 加入任务到我的收藏
     *
     * @param request 包含任务ID和任务类型的请求参数
     * @return 收藏记录VO包装在RespBody中
     */
    @PostMapping("/task/add")
    @Operation(summary = "加入任务到我的收藏")
    public RespBody<CmsTaskCollectionVO> addTaskToCollection(@Valid @RequestBody CmsTaskCollectionRequest request) {
        // 设置用户ID和租户ID
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        // 创建收藏记录并获取VO
        CmsTaskCollectionVO result = cmsCollectService.addTaskToCollection(request.getTaskId(), request.getTaskType(), userId, tenantId);
        return RespBody.ok(result);
    }

    /**
     * 加入分享任务到我的收藏
     *
     * @param request 包含分享码的请求参数
     * @return 收藏记录VO包装在RespBody中
     */
    @PostMapping("/share/add")
    @Operation(summary = "加入分享任务到我的收藏")
    public RespBody<CmsTaskCollectionVO> addSharedTaskToCollection(@Valid @RequestBody CmsShareCollectionRequest request) {
        // 设置用户ID和租户ID
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        // 通过分享码添加分享任务到收藏
        CmsTaskCollectionVO result = cmsCollectService.addSharedTaskToCollection(request.getAuthorizeCode(), userId, tenantId);
        return RespBody.ok(result);
    }

    /**
     * 批量删除收藏记录及对应任务记录
     *
     * @param request 包含收藏ID列表的请求参数
     * @return 删除成功的记录数
     */
    @DeleteMapping("/batch/delete")
    @Operation(summary = "批量删除收藏记录")
    public RespBody<Integer> batchDeleteCollections(@Valid @RequestBody BatchDeleteCollectionRequest request) {
        // 设置用户ID和租户ID
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        // 批量删除收藏记录及对应任务记录
        Integer result = cmsCollectService.batchDeleteCollections(request.getCollectIds(), userId, tenantId);
        return RespBody.ok(result);
    }
}
