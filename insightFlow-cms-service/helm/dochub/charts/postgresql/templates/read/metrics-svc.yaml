{{- if and .Values.metrics.enabled (eq .Values.architecture "replication") }}
apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-metrics" (include "postgresql.readReplica.fullname" .) }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: metrics-read
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if or .Values.commonAnnotations .Values.metrics.service.annotations }}
  annotations:
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.metrics.service.annotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.metrics.service.annotations "context" $ ) | nindent 4 }}
    {{- end }}
  {{- end }}
spec:
  type: ClusterIP
  sessionAffinity: {{ .Values.metrics.service.sessionAffinity }}
  {{- if .Values.metrics.service.clusterIP }}
  clusterIP: {{ .Values.metrics.service.clusterIP }}
  {{- end }}
  ports:
    - name: http-metrics
      port: {{ .Values.metrics.service.ports.metrics }}
      targetPort: http-metrics
  selector: {{- include "common.labels.matchLabels" . | nindent 4 }}
    app.kubernetes.io/component: read
{{- end }}
