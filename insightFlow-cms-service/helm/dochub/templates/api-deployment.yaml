{{- if and .Values.api.enabled}}
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
{{ include "dify.ud.annotations" . | indent 4 }}
    descriptions: api
  labels:
{{- include "dify.labels" . | nindent 4 }}
    component: api
    # app: {{ template "dify.api.fullname" . }}
{{ include "dify.ud.labels" . | indent 4 }}
  name: {{ template "dify.api.fullname" . }}
spec:
  replicas: {{ .Values.api.replicas }}
  selector:
    matchLabels:
{{- include "dify.selectorLabels" . | nindent 6 }}
      component: api
      {{/*
      # Required labels for istio
      # app: {{ template "dify.api.fullname" . }}
      # version: {{ (print "v" .Values.serviceMesh.version) | quote }}
      */}}
  template:
    metadata:
      annotations:
{{ include "dify.ud.annotations" . | indent 8 }}
      labels:
{{- include "dify.selectorLabels" . | nindent 8 }}
        component: api
        {{/*
        # Required labels for istio
        # app: {{ template "dify.api.fullname" . }}
        # version: {{ (print "v" .Values.serviceMesh.version) | quote }}
        */}}
{{ include "dify.ud.labels" . | indent 8 }}
    spec:
      {{- if eq .Release.Name "dify"}}
      {{/*
      Disable service environment variables,
      otherwise they will clash with `DIFY_PORT` which is needed in entrypoint.sh
      */}}
      enableServiceLinks: false
      {{- end }}
      {{- if .Values.image.api.pullSecrets }}
      imagePullSecrets:
      {{- range .Values.image.api.pullSecrets }}
        - name: {{ . }}
      {{- end }}
      {{- end }}
{{- if .Values.api.hostAliases }}
      hostAliases:
{{- range $.Values.api.hostAliases }}
      - ip: {{ .ip }}
        hostnames:
        {{- range .hostnames }}
        - {{ . }}
        {{- end }}
{{- end }}
{{- end }}
      containers:
      - image: "{{ .Values.image.api.repository }}:{{ .Values.image.api.tag }}"
        imagePullPolicy: "{{ .Values.image.api.pullPolicy }}"
        name: api
        env:
        {{- if .Values.api.extraEnv }}
          {{- toYaml .Values.api.extraEnv | nindent 8 }}
        {{- end }}
        envFrom:
          - configMapRef:
              name: {{ template "dify.api.fullname" . }}
        ports:
          - name: api
            containerPort: 5001
            protocol: TCP
        readinessProbe:
          httpGet:
            path: /health
            port: 5001
          initialDelaySeconds: 15
          periodSeconds: 30
        livenessProbe:
          httpGet:
            path: /health
            port: 5001
          initialDelaySeconds: 15
          periodSeconds: 30
        resources:
          {{- toYaml .Values.api.resources | nindent 12 }}
        volumeMounts:
        {{- if and (not .Values.externalS3.enabled) (not .Values.externalAzureBlob.enabled) (not .Values.externalAliyunOSS.enabled) }}
        - name: app-data
          mountPath: {{ .Values.api.persistence.mountPath | quote }}
          subPath: {{ .Values.api.persistence.persistentVolumeClaim.subPath | default "" }}
        {{- end }}
        {{- if .Values.api.externalModels }}
        {{- range $externalModel := .Values.api.externalModels }}
        - name: external-models-config
          mountPath: /app/api/core/model_runtime/model_providers/openai/llm/{{ . }}
          subPath: {{ . }}
        {{- end }}
        {{- end }}
        {{- if .Values.api.volumeMounts }}
        {{- range $apiVolumeMount := .Values.api.volumeMounts }}
        {{- if .items }}
        {{- range $apiVolumeMount.items}}
        - name: {{ $apiVolumeMount.name }}
          mountPath: {{ $apiVolumeMount.mountPath }}/{{ .path }}
          subPath: {{ .key | default "" }}
          readOnly: {{ .readOnly | default "false" }}
        {{- end }}
        {{- else }}
        - name: {{ .name }}
          mountPath: {{ .mountPath }}
          subPath: {{ .subPath | default "" }}
          readOnly: {{ .readOnly | default "false" }}
        {{- end }}
        {{- end }}
        {{- end }}
    {{- if and (.Values.nodeSelector) (not .Values.api.nodeSelector) }}
      nodeSelector:
{{ toYaml .Values.nodeSelector | indent 8 }}
    {{- end }}
    {{- if .Values.api.nodeSelector }}
      nodeSelector:
{{ toYaml .Values.api.nodeSelector | indent 8 }}
    {{- end }}
    {{- if and (.Values.affinity) (not .Values.api.affinity) }}
      affinity:
{{ toYaml .Values.affinity | indent 8 }}
    {{- end }}
    {{- if .Values.api.affinity }}
      affinity:
{{ toYaml .Values.api.affinity | indent 8 }}
    {{- end }}
    {{- if and (.Values.tolerations) (not .Values.api.tolerations) }}
      tolerations:
{{ toYaml .Values.tolerations | indent 8 }}
    {{- end }}
    {{- if .Values.api.tolerations }}
      tolerations:
{{ toYaml .Values.api.tolerations | indent 8 }}
    {{- end }}
      volumes:
      {{- if and (not .Values.externalS3.enabled) (not .Values.externalAzureBlob.enabled) (not .Values.externalAliyunOSS.enabled) }}
      - name: app-data
        persistentVolumeClaim:
          claimName: {{ .Values.api.persistence.persistentVolumeClaim.existingClaim | default (printf "%s" (include "dify.fullname" . | trunc 58)) }}
      {{- end }}
      {{- if .Values.api.externalModels }}
      - name: external-models-config
        configMap:
          name: {{ template "dify.fullname" . }}-external-models-config
      {{- end }}
      {{- if .Values.api.volumeMounts }}
      {{- range .Values.api.volumeMounts }}
      - name: {{ .name }}
        configMap:
          name: {{ .configMap.name }}
          {{- if .items }}
          items:
          {{- range .items | default list }}
          - key: {{ .key }}
            path: {{ .path }}
          {{- end }}
          {{- end }}
      {{- end }}
      {{- end }}
{{- end }}
