package cn.mlamp.insightflow.cms.graph.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Data
@Configuration
@ConfigurationProperties(prefix = "cms.agent.graph.llm-gateway")
public class LLMGatewayProperties {

    private String baseUrl = "https://ai-gateway.mininglamp.com/v1";

    private String apiKey;

    private String simpleModel = "gpt-4o-mini";

    private String model = "gpt-4o";

    private Map<String, ModelParameter> parameters = Map.of();

    @Data
    public static class ModelParameter {

        private Double temperature = 0.3;

        private Integer maxTokens = 4096;

    }

}
