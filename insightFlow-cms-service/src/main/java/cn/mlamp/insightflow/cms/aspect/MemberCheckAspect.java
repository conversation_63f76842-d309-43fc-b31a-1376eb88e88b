package cn.mlamp.insightflow.cms.aspect;

import cn.mlamp.insightflow.cms.annotation.HasMemberRightCheck;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.entity.CmsTenantMemberRightStatistic;
import cn.mlamp.insightflow.cms.enums.ErrorCode;
import cn.mlamp.insightflow.cms.enums.MemberRightEnum;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.mapper.CmsTenantMemberMapper;
import cn.mlamp.insightflow.cms.mapper.CmsTenantMemberRightStatisticMapper;
import cn.mlamp.insightflow.cms.model.dto.MaxRightValueDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Objects;
import java.util.Set;

@Aspect
@Component
@Slf4j
public class MemberCheckAspect {
    private static final Set<MemberRightEnum> VALID_CHECK_RIGHTS = Set.of(MemberRightEnum.HOT_STORYBOARD,
            MemberRightEnum.GOLD3S_SUMMARY, MemberRightEnum.VIDEO_SYNTHESIS, MemberRightEnum.DAM_CAPACITY,
            MemberRightEnum.VIDEO_DURATION, MemberRightEnum.DAM_AI_MARK);

    private final CmsTenantMemberRightStatisticMapper cmsTenantMemberRightStatisticMapper;
    private final CmsTenantMemberMapper cmsTenantMemberMapper;

    public MemberCheckAspect(CmsTenantMemberRightStatisticMapper cmsTenantMemberRightStatisticMapper,
                             CmsTenantMemberMapper cmsTenantMemberMapper) {
        this.cmsTenantMemberRightStatisticMapper = cmsTenantMemberRightStatisticMapper;
        this.cmsTenantMemberMapper = cmsTenantMemberMapper;
    }

    @Pointcut("@annotation(cn.mlamp.insightflow.cms.annotation.HasMemberCheck)")
    public void hasMemberCheckPointcut() {
    }

    @Pointcut("@annotation(cn.mlamp.insightflow.cms.annotation.HasMemberRightCheck)")
    public void hasMemberRightCheckPointcut() {
    }

    /**
     * 检查租户当前是否有会员身份，如果没有，抛出异常
     *
     * @param joinPoint
     */
    @Before("hasMemberCheckPointcut()")
    public void checkMember(JoinPoint joinPoint) {
        int tenantId = UserContext.getTenantId();
        CmsTenantMemberRightStatistic cmsTenantMemberRightStatistic = cmsTenantMemberRightStatisticMapper.
                getByTenantIdAndNow(tenantId);
        if (Objects.isNull(cmsTenantMemberRightStatistic)) {
            throw new BusinessException(ErrorCode.NO_MEMBER);
        }
    }

    /**
     * 检查租户是否有会员权益，如果没有，抛出异常
     *
     * @param joinPoint
     */
    @Before("hasMemberRightCheckPointcut()")
    public void checkMemberRight(JoinPoint joinPoint) {
        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 获取注解实例
        HasMemberRightCheck hasMemberRightCheckAnnotation = method.getAnnotation(HasMemberRightCheck.class);
        MemberRightEnum[] rightEnums = hasMemberRightCheckAnnotation.rights();
        for (MemberRightEnum rightEnum : rightEnums) {
            this.checkOneRight(rightEnum);
        }
    }

    void checkOneRight(MemberRightEnum rightEnum) {
        if (!VALID_CHECK_RIGHTS.contains(rightEnum)) {
            throw new BusinessException("invalid right");
        }

        int tenantId = UserContext.getTenantId();

        if (MemberRightEnum.DAM_CAPACITY.equals(rightEnum)) {
            this.checkDamCapacityRight(tenantId);
            return;
        }
        if (MemberRightEnum.VIDEO_DURATION.equals(rightEnum) || MemberRightEnum.DAM_AI_MARK.equals(rightEnum)) {
            this.checkAccumulativeRight(tenantId, rightEnum);
            return;
        }

        MaxRightValueDTO rightValueDTO = MaxRightValueDTO.builder()
                .tenantId(UserContext.getTenantId())
                .columnName(rightEnum.getColumnName())
                .build();

        Long maxValue = cmsTenantMemberMapper.getMaxRightValue(rightValueDTO);
        if (Objects.isNull(maxValue)) {
            throw new BusinessException(ErrorCode.NO_MEMBER);
        }
        if (0L == maxValue) {
            throw new BusinessException(ErrorCode.NO_ENOUGH_MEMBER_RIGHT);
        }
    }

    /**
     * 检查可累加类型权益
     *
     * @param tenantId
     * @param rightEnum
     */
    void checkAccumulativeRight(int tenantId, MemberRightEnum rightEnum) {
        CmsTenantMemberRightStatistic cmsTenantMemberRightStatistic = cmsTenantMemberRightStatisticMapper.
                getByTenantIdAndNow(tenantId);
        if (Objects.isNull(cmsTenantMemberRightStatistic)) {
            throw new BusinessException(ErrorCode.NO_MEMBER);
        }
        if (MemberRightEnum.VIDEO_DURATION.equals(rightEnum)) {
            if (cmsTenantMemberRightStatistic.getVideoDuration() <= 0) {
                throw new BusinessException(ErrorCode.NO_ENOUGH_MEMBER_RIGHT);
            }
        }
        if (MemberRightEnum.DAM_AI_MARK.equals(rightEnum)) {
            if (cmsTenantMemberRightStatistic.getDamAiMark() <= 0) {
                throw new BusinessException(ErrorCode.NO_ENOUGH_MEMBER_RIGHT);
            }
        }
    }

    void checkDamCapacityRight(int tenantId) {
        MaxRightValueDTO rightValueDTO = MaxRightValueDTO.builder()
                .tenantId(tenantId)
                .columnName(MemberRightEnum.DAM_CAPACITY.getColumnName())
                .build();
        Long maxDamCapacity = cmsTenantMemberMapper.getMaxRightValue(rightValueDTO);
        if (Objects.isNull(maxDamCapacity)) {
            throw new BusinessException(ErrorCode.NO_MEMBER);
        }

        CmsTenantMemberRightStatistic statistic = cmsTenantMemberRightStatisticMapper
                .selectOne((new LambdaQueryWrapper<CmsTenantMemberRightStatistic>())
                        .eq(CmsTenantMemberRightStatistic::getTenantId, tenantId));

        int result = maxDamCapacity.compareTo(statistic.getDamCapacity());
        if (result < 1) {
            throw new BusinessException(ErrorCode.NO_ENOUGH_MEMBER_RIGHT);
        }
    }
}