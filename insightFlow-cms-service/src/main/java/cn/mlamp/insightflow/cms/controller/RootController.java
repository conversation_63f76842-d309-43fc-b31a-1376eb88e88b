package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.entity.User;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-25
 */
@Slf4j
@RequestMapping("/")
@RestController
public class RootController {


    @GetMapping("/")
    public RespBody<?> test() {
        return RespBody.ok();
    }

}
