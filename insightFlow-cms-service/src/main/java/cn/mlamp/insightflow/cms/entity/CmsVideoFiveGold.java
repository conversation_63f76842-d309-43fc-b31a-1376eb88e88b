package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 视频黄金5秒方法论表;
 *
 * <AUTHOR> husper
 * @date : 2025-3-19
 */
@Data
@TableName("cms_video_five_gold")
public class CmsVideoFiveGold extends BaseEntity {
    /**
     * 主键 自增id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 行业分类
     */
    @TableField("industry")
    private String industry;
    /**
     * 标签
     */
    private String tag;
    /**
     * 分析日期
     */
    private String analysisDate;

    /**
     * 封面视频URL
     */
    private String coverVideoUrl;
    /**
     * 关联素材（视频）
     */
    private Integer videoNum;
    /**
     * 平均曝光量
     */
    private Integer exposureCount;
    /**
     * 平均互动数
     */
    private Integer interactCount;
    /**
     * 平均点赞数
     */
    private Integer likeCount;
    /**
     * 平均评论数
     */
    private Integer commentCount;
    /**
     * 创意分值
     */
    private String originalityNum;

    /**
     * 台词套路
     */
    private String dialogueRoutine;
    /**
     * 1：待处理；2：处理中，3：完成，4：失败
     */
    @TableField("`status`")
    private Integer status;


}