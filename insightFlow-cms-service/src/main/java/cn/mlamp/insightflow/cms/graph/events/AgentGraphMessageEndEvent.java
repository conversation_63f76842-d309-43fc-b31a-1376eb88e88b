package cn.mlamp.insightflow.cms.graph.events;

import lombok.Data;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * Agent Graph消息事件 用于传递节点执行过程中的消息信息
 */
@Getter
public class AgentGraphMessageEndEvent extends AbstractAgentGraphEvent<AgentGraphMessageEndEvent.EventData> {

    private final EventType event = EventType.MESSAGE_END;
    private final String runId;
    private final String message;
    private final Map<String, Object> metadata;

    public AgentGraphMessageEndEvent(
            String conversationId,
            String messageId,
            String runId,
            String message
    ) {
        this(conversationId, messageId, runId, message, new HashMap<>());
    }

    public AgentGraphMessageEndEvent(
            String conversationId,
            String messageId,
            String runId,
            String message,
            Map<String, Object> metadata
    ) {
        super(conversationId, messageId);
        this.runId = runId;
        this.message = message;
        this.metadata = new HashMap<>(metadata);
    }

    /**
     * 添加元数据
     */
    public AgentGraphMessageEndEvent withMetadata(String key, Object value) {
        Map<String, Object> newMetadata = new HashMap<>(this.metadata);
        newMetadata.put(key, value);
        return new AgentGraphMessageEndEvent(
                getConversationId(),
                getMessageId(),
                getRunId(),
                message,
                newMetadata
        );
    }

    @Override
    protected EventData getEventData() {
        final EventData eventData = new EventData();
        eventData.setText(message);
        return eventData;
    }

    @Data
    public static class EventData {

        private String text;

    }

}
