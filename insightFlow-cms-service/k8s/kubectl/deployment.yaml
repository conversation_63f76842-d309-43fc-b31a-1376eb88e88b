apiVersion: apps/v1
kind: Deployment
metadata:
  name: kubectl-cli
  namespace: ai-pc
  labels:
    app: kubectl-cli
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kubectl-cli
  template:
    metadata:
      labels:
        app: kubectl-cli
    spec:
      imagePullSecrets:
        - name: "ai-pc-ai-pc-robot"
      containers:
        - name: kubectl-cli
          image: hub.intra.mlamp.cn/ai-pc/bitnami/kubectl:latest
          imagePullPolicy: IfNotPresent
          command: ["/bin/sh", "-c", "while true; do sleep 30; done;"]