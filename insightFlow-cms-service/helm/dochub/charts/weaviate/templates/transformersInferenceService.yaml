{{ if index .Values "modules" "text2vec-transformers" "enabled" }}
apiVersion: v1
kind: Service
metadata:
  name: {{ index .Values "modules" "text2vec-transformers" "fullnameOverride" }}
  labels:
    app.kubernetes.io/name: weaviate
    app.kubernetes.io/managed-by: helm
spec:
  type: ClusterIP
  selector:
    app: {{ index .Values "modules" "text2vec-transformers" "fullnameOverride" }}
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
{{ else }}
    {{ if index .Values "modules" "text2vec-transformers" "passageQueryServices" "passage" "enabled"  }}
apiVersion: v1
kind: Service
metadata:
  name: {{ index .Values "modules" "text2vec-transformers" "passageQueryServices" "passage" "fullnameOverride" }}
spec:
  type: ClusterIP
  selector:
    app: {{ index .Values "modules" "text2vec-transformers" "passageQueryServices" "passage" "fullnameOverride" }}
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
    {{ end }}
---    
    {{ if index .Values "modules" "text2vec-transformers" "passageQueryServices" "query" "enabled"  }}
apiVersion: v1
kind: Service
metadata:
  name: {{ index .Values "modules" "text2vec-transformers" "passageQueryServices" "query" "fullnameOverride" }}
spec:
  type: ClusterIP
  selector:
    app: {{ index .Values "modules" "text2vec-transformers" "passageQueryServices" "query" "fullnameOverride" }}
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
    {{ end }}
{{ end }}
