package cn.mlamp.insightflow.cms.graph.events;

import cn.mlamp.insightflow.cms.graph.events.action.ActionType;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * Agent Graph消息事件 用于传递节点执行过程中的消息信息
 */
@Getter
public class AgentGraphActionEvent extends AbstractAgentGraphEvent<Map<String, Object>> {

    private final EventType event = EventType.ACTION;

    private final String runId;

    private final ActionType actionType;

    private final Object parameters;

    private final Map<String, Object> metadata;

    public AgentGraphActionEvent(
            String conversationId,
            String messageId,
            String runId,
            ActionType actionType,
            Object parameters
    ) {
        this(conversationId, messageId, runId, actionType, parameters, new HashMap<>());
    }

    public AgentGraphActionEvent(
            String conversationId,
            String messageId,
            String runId,
            ActionType actionType,
            Object parameters,
            Map<String, Object> metadata
    ) {
        super(conversationId, messageId);
        this.runId = runId;
        this.actionType = actionType;
        this.parameters = parameters;
        this.metadata = new HashMap<>(metadata);
    }

    public Map<String, Object> getEventData() {
        return Map.of(
                "type", actionType,
                "parameters", parameters
        );
    }

}
