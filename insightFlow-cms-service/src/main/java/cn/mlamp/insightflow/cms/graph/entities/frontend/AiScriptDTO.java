package cn.mlamp.insightflow.cms.graph.entities.frontend;

import cn.hutool.core.util.StrUtil;
import cn.mlamp.insightflow.cms.model.query.ScriptGenerationRequest;
import cn.mlamp.insightflow.cms.model.query.VideoScriptGenRequest;
import com.fasterxml.jackson.annotation.JsonSetter;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@NoArgsConstructor
@Data
public class AiScriptDTO {

    private Integer id;

    private FormDTO form;

    @NoArgsConstructor
    @Data
    public static class FormDTO {

        private Boolean isGold3s = true;

        private String brand;

        private String product;

        private Integer taskFlowId;

        private String sellingPoint;

        private String discount;

        private String dialogueEmotion;

        private String price;

        private String costumeSuggestions;

        private String scene;

        private String setRequirements;

        private String targetAudience;

        private String painPoint;

        private String wording;

        private String startGold3s;

        private String actionType;

        private String userStory;

        private String data;

        private String id;

        @JsonSetter("content")
        public void setContent(Map<String, Object> content) {
            this.isGold3s = false;
            this.brand = (String) content.get("brand");
            this.product = (String) content.get("product");
            this.sellingPoint = (String) content.get("sellingPoint");
            this.startGold3s = (String) content.get("startGold3s");

            this.discount = (String) content.get("discount");
            this.price = (String) content.get("price");
            this.dialogueEmotion = (String) content.get("dialogueEmotion");
            this.costumeSuggestions = (String) content.get("costumeSuggestions");
            this.scene = (String) content.get("scene");
            this.setRequirements = (String) content.get("setRequirements");
            this.targetAudience = (String) content.get("targetAudience");
            this.painPoint = (String) content.get("painPoint");
        }

        private void appendStr(StringBuilder sb, String label, String value) {
            if (StrUtil.isNotBlank(value)) {
                sb.append("- ").append(label).append(": ").append(value).append("\n");
            }
        }

        private void appendAdvance(StringBuilder sb) {
            sb.append("\n").append("高级设置:\n");
            appendStr(sb, "优惠活动", discount);
            appendStr(sb, "价格", price);
            appendStr(sb, "台词情绪", dialogueEmotion);
            appendStr(sb, "服装造型", costumeSuggestions);
            appendStr(sb, "场景", scene);
            appendStr(sb, "布景要求", setRequirements);
            appendStr(sb, "受众人群", targetAudience);
        }

        public String toGold3s() {
            final StringBuilder sb = new StringBuilder("黄金3秒相关信息:\n");
            appendStr(sb, "品牌名称", brand);
            appendStr(sb, "产品名称", product);
            appendStr(sb, "黄金3s类型", startGold3s);
            appendStr(sb, "台词套路", wording);
            appendStr(sb, "用户画像", userStory);
            appendStr(sb, "行动号召", actionType);

            return sb.toString();
        }

        public String toHotspotVideo() {
            final StringBuilder sb = new StringBuilder("爆款视频相关信息:\n");
            appendStr(sb, "品牌名称", brand);
            appendStr(sb, "产品名称", product);
            appendStr(sb, "卖点", sellingPoint);
            appendStr(sb, "黄金3s类型", startGold3s);

            appendAdvance(sb);
            appendStr(sb, "痛点", painPoint);
            return sb.toString();
        }

        public VideoScriptGenRequest.ScriptInputContent toScriptInputContent() {
            final VideoScriptGenRequest.ScriptInputContent content = new VideoScriptGenRequest.ScriptInputContent();
            if (!isGold3s) {
                content.setBrand(brand);
                content.setProduct(product);
                content.setSellingPoint(sellingPoint);
                content.setStartGold3s(startGold3s);

                // 高级设置
                content.setDiscount(discount);
                content.setPrice(price);
                content.setDialogueEmotion(dialogueEmotion);
                content.setCostumeSuggestions(costumeSuggestions);
                content.setScene(scene);
                content.setSetRequirements(setRequirements);
                content.setPainPoint(painPoint);
                content.setTargetAudience(targetAudience);
            }
            return content;
        }

        public ScriptGenerationRequest toScriptGenerationRequest() {
            final ScriptGenerationRequest request = new ScriptGenerationRequest();
            request.setBrand(brand);
            request.setProduct(product);
            request.setStartGold3s(startGold3s);
            request.setWording(wording);
            request.setUserStory(userStory);
            request.setActionType(actionType);
            request.setData(data);

            // 高级设置
            request.setDiscount(discount);
            request.setPrice(price);
            request.setDialogueEmotion(dialogueEmotion);
            request.setCostumeSuggestions(costumeSuggestions);
            request.setScene(scene);
            request.setSetRequirements(setRequirements);
            request.setTargetAudience(targetAudience);

            return request;
        }
    }
}
