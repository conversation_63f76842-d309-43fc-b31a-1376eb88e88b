package cn.mlamp.insightflow.cms.enums;

import lombok.Getter;
import org.springframework.lang.Nullable;

/**
 * 列表类型枚举
 */
@Getter
public enum CmsListTypeEnum {

    /**
     * 普通列表
     */
    NORMAL(1, "普通列表"),

    /**
     * 收藏列表
     */
    COLLECTION(2, "收藏列表");

    private final Integer code;
    private final String desc;

    CmsListTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过code获取枚举值
     *
     * @param code 枚举code
     * @return 枚举值
     */
    public static CmsListTypeEnum getByCode(@Nullable Integer code) {
        if (code == null) {
            return null;
        }
        for (CmsListTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
