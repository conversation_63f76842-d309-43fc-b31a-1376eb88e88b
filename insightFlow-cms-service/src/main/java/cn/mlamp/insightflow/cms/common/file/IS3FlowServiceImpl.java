/*
 *
 *                                  Apache License
 *                            Version 2.0, January 2004
 *                         https://www.apache.org/licenses/
 *
 *    TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION
 *
 *    1. Definitions.
 *
 *       "License" shall mean the terms and conditions for use, reproduction,
 *       and distribution as defined by Sections 1 through 9 of this document.
 *
 *       "Licensor" shall mean the copyright owner or entity authorized by
 *       the copyright owner that is granting the License.
 *
 *       "Legal Entity" shall mean the union of the acting entity and all
 *       other entities that control, are controlled by, or are under common
 *       control with that entity. For the purposes of this definition,
 *       "control" means (i) the power, direct or indirect, to cause the
 *       direction or management of such entity, whether by contract or
 *       otherwise, or (ii) ownership of fifty percent (50%) or more of the
 *       outstanding shares, or (iii) beneficial ownership of such entity.
 *
 *       "You" (or "Your") shall mean an individual or Legal Entity
 *       exercising permissions granted by this License.
 *
 *       "Source" form shall mean the preferred form for making modifications,
 *       including but not limited to software source code, documentation
 *       source, and configuration files.
 *
 *       "Object" form shall mean any form resulting from mechanical
 *       transformation or translation of a Source form, including but
 *       not limited to compiled object code, generated documentation,
 *       and conversions to other media types.
 *
 *       "Work" shall mean the work of authorship, whether in Source or
 *       Object form, made available under the License, as indicated by a
 *       copyright notice that is included in or attached to the work
 *       (an example is provided in the Appendix below).
 *
 *       "Derivative Works" shall mean any work, whether in Source or Object
 *       form, that is based on (or derived from) the Work and for which the
 *       editorial revisions, annotations, elaborations, or other modifications
 *       represent, as a whole, an original work of authorship. For the purposes
 *       of this License, Derivative Works shall not include works that remain
 *       separable from, or merely link (or bind by name) to the interfaces of,
 *       the Work and Derivative Works thereof.
 *
 *       "Contribution" shall mean any work of authorship, including
 *       the original version of the Work and any modifications or additions
 *       to that Work or Derivative Works thereof, that is intentionally
 *       submitted to Licensor for inclusion in the Work by the copyright owner
 *       or by an individual or Legal Entity authorized to submit on behalf of
 *       the copyright owner. For the purposes of this definition, "submitted"
 *       means any form of electronic, verbal, or written communication sent
 *       to the Licensor or its representatives, including but not limited to
 *       communication on electronic mailing lists, source code control systems,
 *       and issue tracking systems that are managed by, or on behalf of, the
 *       Licensor for the purpose of discussing and improving the Work, but
 *       excluding communication that is conspicuously marked or otherwise
 *       designated in writing by the copyright owner as "Not a Contribution."
 *
 *       "Contributor" shall mean Licensor and any individual or Legal Entity
 *       on behalf of whom a Contribution has been received by Licensor and
 *       subsequently incorporated within the Work.
 *
 *    2. Grant of Copyright License. Subject to the terms and conditions of
 *       this License, each Contributor hereby grants to You a perpetual,
 *       worldwide, non-exclusive, no-charge, royalty-free, irrevocable
 *       copyright license to reproduce, prepare Derivative Works of,
 *       publicly display, publicly perform, sublicense, and distribute the
 *       Work and such Derivative Works in Source or Object form.
 *
 *    3. Grant of Patent License. Subject to the terms and conditions of
 *       this License, each Contributor hereby grants to You a perpetual,
 *       worldwide, non-exclusive, no-charge, royalty-free, irrevocable
 *       (except as stated in this section) patent license to make, have made,
 *       use, offer to sell, sell, import, and otherwise transfer the Work,
 *       where such license applies only to those patent claims licensable
 *       by such Contributor that are necessarily infringed by their
 *       Contribution(s) alone or by combination of their Contribution(s)
 *       with the Work to which such Contribution(s) was submitted. If You
 *       institute patent litigation against any entity (including a
 *       cross-claim or counterclaim in a lawsuit) alleging that the Work
 *       or a Contribution incorporated within the Work constitutes direct
 *       or contributory patent infringement, then any patent licenses
 *       granted to You under this License for that Work shall terminate
 *       as of the date such litigation is filed.
 *
 *    4. Redistribution. You may reproduce and distribute copies of the
 *       Work or Derivative Works thereof in any medium, with or without
 *       modifications, and in Source or Object form, provided that You
 *       meet the following conditions:
 *
 *       (a) You must give any other recipients of the Work or
 *           Derivative Works a copy of this License; and
 *
 *       (b) You must cause any modified files to carry prominent notices
 *           stating that You changed the files; and
 *
 *       (c) You must retain, in the Source form of any Derivative Works
 *           that You distribute, all copyright, patent, trademark, and
 *           attribution notices from the Source form of the Work,
 *           excluding those notices that do not pertain to any part of
 *           the Derivative Works; and
 *
 *       (d) If the Work includes a "NOTICE" text file as part of its
 *           distribution, then any Derivative Works that You distribute must
 *           include a readable copy of the attribution notices contained
 *           within such NOTICE file, excluding those notices that do not
 *           pertain to any part of the Derivative Works, in at least one
 *           of the following places: within a NOTICE text file distributed
 *           as part of the Derivative Works; within the Source form or
 *           documentation, if provided along with the Derivative Works; or,
 *           within a display generated by the Derivative Works, if and
 *           wherever such third-party notices normally appear. The contents
 *           of the NOTICE file are for informational purposes only and
 *           do not modify the License. You may add Your own attribution
 *           notices within Derivative Works that You distribute, alongside
 *           or as an addendum to the NOTICE text from the Work, provided
 *           that such additional attribution notices cannot be construed
 *           as modifying the License.
 *
 *       You may add Your own copyright statement to Your modifications and
 *       may provide additional or different license terms and conditions
 *       for use, reproduction, or distribution of Your modifications, or
 *       for any such Derivative Works as a whole, provided Your use,
 *       reproduction, and distribution of the Work otherwise complies with
 *       the conditions stated in this License.
 *
 *    5. Submission of Contributions. Unless You explicitly state otherwise,
 *       any Contribution intentionally submitted for inclusion in the Work
 *       by You to the Licensor shall be under the terms and conditions of
 *       this License, without any additional terms or conditions.
 *       Notwithstanding the above, nothing herein shall supersede or modify
 *       the terms of any separate license agreement you may have executed
 *       with Licensor regarding such Contributions.
 *
 *    6. Trademarks. This License does not grant permission to use the trade
 *       names, trademarks, service marks, or product names of the Licensor,
 *       except as required for reasonable and customary use in describing the
 *       origin of the Work and reproducing the content of the NOTICE file.
 *
 *    7. Disclaimer of Warranty. Unless required by applicable law or
 *       agreed to in writing, Licensor provides the Work (and each
 *       Contributor provides its Contributions) on an "AS IS" BASIS,
 *       WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
 *       implied, including, without limitation, any warranties or conditions
 *       of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
 *       PARTICULAR PURPOSE. You are solely responsible for determining the
 *       appropriateness of using or redistributing the Work and assume any
 *       risks associated with Your exercise of permissions under this License.
 *
 *    8. Limitation of Liability. In no event and under no legal theory,
 *       whether in tort (including negligence), contract, or otherwise,
 *       unless required by applicable law (such as deliberate and grossly
 *       negligent acts) or agreed to in writing, shall any Contributor be
 *       liable to You for damages, including any direct, indirect, special,
 *       incidental, or consequential damages of any character arising as a
 *       result of this License or out of the use or inability to use the
 *       Work (including but not limited to damages for loss of goodwill,
 *       work stoppage, computer failure or malfunction, or any and all
 *       other commercial damages or losses), even if such Contributor
 *       has been advised of the possibility of such damages.
 *
 *    9. Accepting Warranty or Additional Liability. While redistributing
 *       the Work or Derivative Works thereof, You may choose to offer,
 *       and charge a fee for, acceptance of support, warranty, indemnity,
 *       or other liability obligations and/or rights consistent with this
 *       License. However, in accepting such obligations, You may act only
 *       on Your own behalf and on Your sole responsibility, not on behalf
 *       of any other Contributor, and only if You agree to indemnify,
 *       defend, and hold each Contributor harmless for any liability
 *       incurred by, or claims asserted against, such Contributor by reason
 *       of your accepting any such warranty or additional liability.
 *
 *    END OF TERMS AND CONDITIONS
 *
 *    APPENDIX: How to apply the Apache License to your work.
 *
 *       To apply the Apache License to your work, attach the following
 *       boilerplate notice, with the fields enclosed by brackets "{}"
 *       replaced with your own identifying information. (Don't include
 *       the brackets!)  The text should be enclosed in the appropriate
 *       comment syntax for the file format. We also recommend that a
 *       file or class name and description of purpose be included on the
 *       same "printed page" as the copyright notice for easier
 *       identification within third-party archives.
 *
 *    Copyright 2024 onsamepage.ai
 *
 *    Licensed under the Apache License, Version 2.0 (the "License");
 *    you may not use this file except in compliance with the License.
 *    You may obtain a copy of the License at
 *
 *        https://www.apache.org/licenses/LICENSE-2.0
 *
 *    Unless required by applicable law or agreed to in writing, software
 *    distributed under the License is distributed on an "AS IS" BASIS,
 *    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *    See the License for the specific language governing permissions and
 *    limitations under the License.
 */
package cn.mlamp.insightflow.cms.common.file;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.mlamp.insightflow.cms.common.resp.RespCode;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.HttpMethod;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHeaders;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;


/**
 *
 */
@Slf4j
public class IS3FlowServiceImpl implements IS3FlowService {


    private IS3FlowServiceImpl() {

    }


    /**
     * aws s3 client
     */
    private AmazonS3 s3;


    private ThreadPoolExecutor threadPoolExecutor = null;


    private String bucketName;

    private Integer minMultipartSize;

    private String cloudFrontHost;


    public IS3FlowServiceImpl(IS3FlowProperties properties,
                              ThreadPoolExecutor executor,
                              Integer spitSize) {
        this.bucketName = properties.getBucketName();
        ClientConfiguration config = new ClientConfiguration();
        config.setSocketTimeout(300 * 1000);
        config.setConnectionTimeout(500 * 1000);
        AwsClientBuilder.EndpointConfiguration endpointConfig = new AwsClientBuilder
                .EndpointConfiguration(properties.getEndpoint(), properties.getRegion());
        AWSCredentials awsCredentials = new BasicAWSCredentials(properties.getAccessKey(), properties.getSecretKey());
        AWSCredentialsProvider awsCredentialsProvider = new AWSStaticCredentialsProvider(awsCredentials);
//        boolean pathStyle = properties.getEndpoint().contains(bucketName);
        this.s3 = AmazonS3Client.builder()
                .withEndpointConfiguration(endpointConfig)
                .withClientConfiguration(config)
                .withCredentials(awsCredentialsProvider)
                .disableChunkedEncoding()
                .withPathStyleAccessEnabled(properties.getPathStyle())
                .build();

        this.threadPoolExecutor = executor;
        this.minMultipartSize = spitSize;
        this.cloudFrontHost = properties.getCloudFrontHost();
    }

    /**
     * 用s3协议进行上传
     *
     * @param tid
     * @param objectId
     */
    private void s3Upload(long tid, String objectId, Map<String, Object> headers, File file) {
        ObjectMetadata objectMetadata = new ObjectMetadata();
        if (Objects.nonNull(headers) && !headers.isEmpty()) {
            headers.forEach(objectMetadata::setHeader);
        }
        long length = file.length();
        objectMetadata.setContentLength(length);
        long uploadStart = System.currentTimeMillis();
        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest(this.bucketName, objectId, file);
            putObjectRequest.setMetadata(objectMetadata);
            putObjectRequest.getRequestClientOptions().setReadLimit(Math.toIntExact(length + 1));
            s3.putObject(putObjectRequest);
            if (log.isInfoEnabled()) {
                log.info("tid:{},上传完成，时间：{}", tid, System.currentTimeMillis() - uploadStart);
            }
        } catch (Exception e) {
            log.error("tid:{},S3文件上传异常", tid, e);
            throw new BusinessException(RespCode.INTERNAL_SERVER_ERROR, "文件上传失败");
        }
    }


    /**
     * 用s3协议进行上传
     *
     * @param tid
     * @param objectId
     * @param fileSize
     */
    private void s3Upload(long tid, String objectId, long fileSize, Map<String, Object> headers, InputStream inputStream) {
        // s3协议对象元信息
        ObjectMetadata objectMetadata = new ObjectMetadata();
        if (Objects.nonNull(headers) && !headers.isEmpty()) {
            headers.forEach(objectMetadata::setHeader);
        }
        objectMetadata.setContentLength(fileSize);
        long uploadStart = System.currentTimeMillis();
        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest(getBucketName(), objectId, inputStream, objectMetadata);
            putObjectRequest.getRequestClientOptions().setReadLimit(Math.toIntExact(fileSize + 1));
            s3.putObject(putObjectRequest);
            if (log.isInfoEnabled()) {
                log.info("tid:{},上传完成，时间：{}", tid, System.currentTimeMillis() - uploadStart);
            }
        } catch (Exception e) {
            log.error("tid:{},S3文件上传异常", tid, e);
            throw new BusinessException(RespCode.INTERNAL_SERVER_ERROR, "文件上传失败");
        }
    }


    @Override
    public AmazonS3 getClient() {
        return this.s3;
    }

    private final static Integer MAX_EXPIRE_SECOND = 7 * 24 * 60 * 60 - 1;

    @Override
    public URL downloadPresignedUrl(String bucketName, String objId, Integer expireSecond, Integer cacheMinute)
            throws IllegalArgumentException {
        return downloadPresignedUrl(bucketName, objId, expireSecond, null, null, cacheMinute);
    }


    @Override
    public URL downloadPresignedUrl(String bucketName, String objId, Integer expireSecond, String contentType, String contentDisposition, Integer cacheMinute) throws IllegalArgumentException {
        return this.downloadPresignedUrl(bucketName, objId, expireSecond, contentType, contentDisposition, cacheMinute, true);
    }

    @Override
    public URL downloadPresignedUrl(String bucketName,
                                    String objId,
                                    Integer expireSecond,
                                    String contentType,
                                    String contentDisposition,
                                    Integer cacheMinute, boolean isCdn) throws IllegalArgumentException {
        if (StrUtil.isBlank(objId)) {
            throw new IllegalArgumentException("获取预签名的下载的url参数不正确");

        }
        GeneratePresignedUrlRequest request = createRequest(bucketName, objId, null, Long.valueOf(expireSecond),
                null, HttpMethod.GET);
        ResponseHeaderOverrides responseHeaderOverrides = new ResponseHeaderOverrides();
        if (Objects.nonNull(cacheMinute)) {
            responseHeaderOverrides.setCacheControl(StrUtil.format("max-age={}", cacheMinute * 60));
        }
        if (StrUtil.isNotBlank(contentType)) {
            responseHeaderOverrides.setContentType(contentType);
        }
        if (StrUtil.isNotBlank(contentDisposition)) {
            responseHeaderOverrides.setContentDisposition(contentDisposition);
        }
        request.setResponseHeaders(responseHeaderOverrides);
        URL url = s3.generatePresignedUrl(request);
        if (!isCdn) {
            return url;
        }
        if (StrUtil.isBlank(cloudFrontHost)) {
            return url;
        } else {
            try {
                return new URL(url.getProtocol(), cloudFrontHost, url.getPort(), url.getFile());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return url;
            }
        }
    }

    @Override
    public URL uploadPresidedUrl(String objId, Integer expireSecond) throws IllegalArgumentException {
        if (StrUtil.isBlank(objId) || expireSecond <= 0 || expireSecond > 3600) {
            throw new IllegalArgumentException("获取预签名的上传的url参数不正确");
        }
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(this.bucketName, objId);
        request.setMethod(HttpMethod.PUT);
        request.setExpiration(new Date(System.currentTimeMillis() + expireSecond * 1000));
        return s3.generatePresignedUrl(request);

    }

    @Override
    public String uploadPresidedUrl(String objId, String contentType, Long expireSecond) throws IllegalArgumentException {
        return uploadPresidedUrl(objId, contentType, expireSecond, null);
    }


    @Override
    public String uploadPresidedUrl(String objId, String contentType, Long expireSecond, String version)
            throws IllegalArgumentException {
        if (StrUtil.isBlank(objId)) {
            throw new IllegalArgumentException("获取预签名的下载的url参数不正确");
        }

        return s3.generatePresignedUrl(createRequest(null, objId, contentType, expireSecond, version, HttpMethod.PUT)).toString();
    }

    private GeneratePresignedUrlRequest createRequest(String originBucketName, String objId, String contentType, Long expireSecond,
                                                      String version, HttpMethod httpMethod) {
        Date expiration = new Date();
        expireSecond = Objects.isNull(expireSecond) || MAX_EXPIRE_SECOND < expireSecond ?
                MAX_EXPIRE_SECOND : expireSecond;
        long expTimeMillis = expiration.getTime();
        expTimeMillis += 1000 * expireSecond;
        expiration.setTime(expTimeMillis);
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(getBucketName(),
                getObjId(originBucketName, objId), httpMethod)
                .withExpiration(expiration);
        if (StrUtil.isNotBlank(contentType)) {
            request.setContentType(contentType);
        }
        if (StrUtil.isNotBlank(version)) {
            request.setVersionId(version);
        }
        return request;
    }

    @Override
    public void upload(String objectId, Long fileSize, InputStream inputStream, Map<String, Object> headers) throws BusinessException {
        long tid = System.currentTimeMillis();
        if (Objects.isNull(fileSize)) {
            try {
                fileSize = (long) inputStream.available();
            } catch (IOException e) {
                log.error("tid:{},S3文件获取文件的大小", tid, e);
                throw new BusinessException(RespCode.INTERNAL_SERVER_ERROR, "S3文件获取文件的大小失败");
            }
        }
        s3Upload(tid, objectId, fileSize, headers, inputStream);
    }

    @Override
    public void upload(String objectId, File file) throws BusinessException {
        if (Objects.isNull(file) || !file.exists()) {
            throw new BusinessException(RespCode.INTERNAL_SERVER_ERROR, StrUtil.format("上传文件{}不存在", objectId));
        }
        s3Upload(System.currentTimeMillis(), objectId, Collections.emptyMap(), file);
    }

    @Override
    public void upload(String objectId, MultipartFile file) throws BusinessException, IOException {
        long tid = System.currentTimeMillis();
        log.info("tid:{},进入文件上传流程", tid);
        Map<String, Object> headers = new HashMap<>();
        headers.put(HttpHeaders.CONTENT_TYPE, file.getContentType());
        s3Upload(tid, objectId, file.getSize(), headers, file.getInputStream());
    }

    @Override
    public InputStream download(String objectId) throws BusinessException {
        return download(getBucketName(), objectId);
    }

    @Override
    public InputStream download(String bucketName, String objectId) throws BusinessException {
        long tid = System.currentTimeMillis();
        try {
            log.info("tid:{},objectId:{} 进入文件下载流程", tid, objectId);
            return s3.getObject(this.bucketName, getObjId(bucketName, objectId)).getObjectContent();


        } catch (Exception e) {
            String message = String.format("下载%s失败,原因：%s", tid, e.getMessage());
            log.error(message, e);
            throw new BusinessException(RespCode.INTERNAL_SERVER_ERROR, message);
        }

    }


    private String getBucketName() {
        return this.bucketName;
    }

    private String getObjId(String originBucketName, String objId) {
        if (StrUtil.isNotBlank(originBucketName)
                && !Objects.equals(originBucketName, this.bucketName)
                && !objId.startsWith(originBucketName)) {
            return originBucketName + "/" + objId;
        }
        return objId;
    }

    @Override
    public ObjectMetadata getMetaData(String bucketName, String objId) {
        return s3.getObjectMetadata(new GetObjectMetadataRequest(getBucketName(), getObjId(bucketName, objId)));
    }

    @Override
    public boolean existsObj(String objId) {
        return existsObj(null, objId);
    }

    @Override
    public ObjectMetadata getMetaData(String objId) {
        return getMetaData(null, objId);
    }

    @Override
    public boolean existsObj(String bucketName, String objId) {
        if (StrUtil.isNotBlank(objId)) {
            try {
                getMetaData(bucketName, objId);
                return true;
            } catch (AmazonS3Exception e) {
                if (e.getStatusCode() == 404) {
                    return false;
                }
                throw e;
            }
        }
        return false;
    }


    @Override
    public void deleteFile(String bucketName, String objectId) {
        s3.deleteObject(getBucketName(), getObjId(bucketName, objectId));
    }

    @Override
    public void deleteFile(String objectId) {
        deleteFile(null, objectId);
    }

    @Override
    public void multiPartUpload(String objectId, File file, Map<String, Object> headers) {
        if (StrUtil.isBlank(objectId) || Objects.isNull(file) || !file.exists()) {
            throw new BusinessException(RespCode.INTERNAL_SERVER_ERROR, "参数不符合要求");
        }
        if (Objects.isNull(threadPoolExecutor)) {
            upload(objectId, file);
            return;
        }
        long tid = System.currentTimeMillis();
        if (log.isInfoEnabled()) {
            log.info("tid:{},开始上传文件：{}", tid, objectId);
        }
        // 如果超过文件的大小 则多线程分片上传
        if (file.length() > minMultipartSize) {
            uploadMultipartFileByPart(tid, file, objectId, headers);
        } else {
            s3Upload(tid, objectId, headers, file);
        }
    }


    /**
     * 分段上传文件至S3
     *
     * @param file  文件实体
     * @param s3Key key的信息
     */
    public void uploadMultipartFileByPart(long tid, File file, String s3Key, Map<String, Object> headers) {
        if (Objects.isNull(threadPoolExecutor)) {
            throw new UnsupportedOperationException("线程池不存在，暂不支持");
        }
        long size = file.length();
        int minPartSize = minMultipartSize;
        // 得到总共的段数，和 分段后，每个段的开始上传的字节位置
        List<Long> positions = Collections.synchronizedList(new ArrayList<>());
        long filePosition = 0;
        while (filePosition < size) {
            positions.add(filePosition);
            filePosition += Math.min(minPartSize, (size - filePosition));
        }
        ObjectMetadata objectMetadata = new ObjectMetadata();
        if (Objects.nonNull(headers) && !headers.isEmpty()) {
            headers.forEach((k, v) -> objectMetadata.setHeader(k, String.valueOf(v)));
        }
        log.info("tid:{},总大小：{}，分为{}段", tid, size, positions.size());
        // 创建一个列表保存所有分传的 PartETag, 在分段完成后会用到
        List<PartETag> partETags = Collections.synchronizedList(new ArrayList<>());
        // 第一步，初始化，声明下面将有一个 Multipart Upload
        InitiateMultipartUploadRequest initRequest = new InitiateMultipartUploadRequest(bucketName, s3Key);
        InitiateMultipartUploadResult initResponse = s3.initiateMultipartUpload(initRequest);
        long begin = System.currentTimeMillis();
        try {
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            // MultipartFile 转 File
            for (int i = 0; i < positions.size(); i++) {
                int finalI = i;
                CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                    long time1 = System.currentTimeMillis();
                    UploadPartRequest uploadRequest = new UploadPartRequest()
                            .withBucketName(bucketName).withKey(s3Key).withUploadId(initResponse.getUploadId()).withPartNumber(finalI + 1).withFileOffset(positions.get(finalI)).withFile(file).withObjectMetadata(objectMetadata).withPartSize(Math.min(minPartSize, (size - positions.get(finalI))));
                    // 第二步，上传分段，并把当前段的 PartETag 放到列表中
                    partETags.add(s3.uploadPart(uploadRequest).getPartETag());
                    long time2 = System.currentTimeMillis();
                    log.info("tid:{},第{}段上传耗时：{}", tid, finalI + 1, (time2 - time1));
                }, threadPoolExecutor);
                futures.add(completableFuture);
            }

            // 等待上传完成
            futures.forEach(CompletableFuture::join);
            // 第三步，完成上传，合并分段
            CompleteMultipartUploadRequest compRequest = new CompleteMultipartUploadRequest(bucketName, s3Key, initResponse.getUploadId(), partETags);
            s3.completeMultipartUpload(compRequest);
        } catch (Exception e) {
            s3.abortMultipartUpload(new AbortMultipartUploadRequest(bucketName, s3Key, initResponse.getUploadId()));
            log.error("Failed to upload, " + e.getMessage());
        }
        long end = System.currentTimeMillis();
        log.info("tid:{},总上传耗时：{}", tid, (end - begin));
    }


    @Override
    public String uploadPresidedUrl(String objId, Integer expireSecond, Map<String, String> params) throws IllegalArgumentException {
        return uploadPresidedUrl(objId, null, Long.valueOf(expireSecond), null, params);
    }

    private String uploadPresidedUrl(String objId, String contentType, Long expireSecond, String version, Map<String, String> params)
            throws IllegalArgumentException {
        if (StrUtil.isBlank(objId)) {
            throw new IllegalArgumentException("获取预签名的下载的url参数不正确");
        }
        GeneratePresignedUrlRequest request = createRequest(null, objId, contentType, expireSecond, version, HttpMethod.PUT);
        if (CollectionUtil.isNotEmpty(params)) {
            params.forEach(request::addRequestParameter);
        }
        return s3.generatePresignedUrl(request).toString();
    }

    @Override
    public URL thumbnailDownloadPresignedUrl(String bucketName,
                                     String objId,
                                     Integer expireSecond,
                                     String contentType,
                                     String contentDisposition,
                                     Integer cacheMinute, boolean isCdn,
                                     Integer width,
                                     Integer height) throws IllegalArgumentException {
        if (StrUtil.isBlank(objId)) {
            throw new IllegalArgumentException("获取预签名的下载的url参数不正确");

        }

        GeneratePresignedUrlRequest request = createRequest(bucketName, objId, null, Long.valueOf(expireSecond),
                null, HttpMethod.GET);
        // 添加 OSS 图片处理参数
        if (width != null && height != null && width > 0 && height > 0) {
            String processParam = String.format("image/resize,m_fixed,w_%d,h_%d", width, height);
            request.addRequestParameter("x-oss-process", processParam);
        }
        ResponseHeaderOverrides responseHeaderOverrides = new ResponseHeaderOverrides();
        if (Objects.nonNull(cacheMinute)) {
            responseHeaderOverrides.setCacheControl(StrUtil.format("max-age={}", cacheMinute * 60));
        }
        if (StrUtil.isNotBlank(contentType)) {
            responseHeaderOverrides.setContentType(contentType);
        }
        if (StrUtil.isNotBlank(contentDisposition)) {
            responseHeaderOverrides.setContentDisposition(contentDisposition);
        }
        request.setResponseHeaders(responseHeaderOverrides);
        URL url = s3.generatePresignedUrl(request);
        if (!isCdn) {
            return url;
        }
        if (StrUtil.isBlank(cloudFrontHost)) {
            return url;
        } else {
            try {
                return new URL(url.getProtocol(), cloudFrontHost, url.getPort(), url.getFile());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return url;
            }
        }
    }
}
