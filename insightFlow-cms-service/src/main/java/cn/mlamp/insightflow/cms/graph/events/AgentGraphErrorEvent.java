package cn.mlamp.insightflow.cms.graph.events;

import lombok.Data;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * Agent Graph消息事件 用于传递节点执行过程中的消息信息
 */
@Getter
public class AgentGraphErrorEvent extends AbstractAgentGraphEvent<AgentGraphErrorEvent.EventData> {

    private final EventType event = EventType.WORKFLOW_ERROR;
    private final String error;
    private final Map<String, Object> metadata;

    public AgentGraphErrorEvent(
            String conversationId,
            String messageId,
            String error
    ) {
        this(conversationId, messageId, error, new HashMap<>());
    }

    public AgentGraphErrorEvent(
            String conversationId,
            String messageId,
            String error,
            Map<String, Object> metadata
    ) {
        super(conversationId, messageId);
        this.error = error;
        this.metadata = new HashMap<>(metadata);
    }

    /**
     * 添加元数据
     */
    public AgentGraphErrorEvent withMetadata(String key, Object value) {
        Map<String, Object> newMetadata = new HashMap<>(this.metadata);
        newMetadata.put(key, value);
        return new AgentGraphErrorEvent(
                getConversationId(),
                getMessageId(),
                error,
                newMetadata
        );
    }

    @Override
    protected EventData getEventData() {
        final EventData eventData = new EventData();
        eventData.setError(error);
        eventData.setElapsedTime(System.currentTimeMillis() - getCreateTime());
        eventData.setCreateTime(getCreateTime());
        eventData.setFinishTime(System.currentTimeMillis());
        return eventData;
    }

    @Data
    public static class EventData {

        private String error;

        private long elapsedTime;

        private long createTime;

        private long finishTime;

    }

}
