{{- if and .Values.webMlamp.enabled}}
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
{{ include "dify.ud.annotations" . | indent 4 }}
    descriptions: web mlamp server
  labels:
{{- include "dify.labels" . | nindent 4 }}
    component: web-mlamp
    # app: {{ template "dify.web.fullname" . }}
{{ include "dify.ud.labels" . | indent 4 }}
  name: {{ template "dify.webMlamp.fullname" . }}
spec:
  replicas: {{ .Values.webMlamp.replicas }}
  selector:
    matchLabels:
{{- include "dify.selectorLabels" . | nindent 6 }}
      component: web-mlamp
      {{/*
      # Required labels for istio
      # app: {{ template "dify.webMlamp.fullname" . }}
      # version: {{ (print "v" .Values.serviceMesh.version) | quote }}
      */}}
  template:
    metadata:
      annotations:
{{ include "dify.ud.annotations" . | indent 8 }}
      labels:
{{- include "dify.selectorLabels" . | nindent 8 }}
        component: web-mlamp
        {{/*
        # Required labels for istio
        # app: {{ template "dify.webMlamp.fullname" . }}
        # version: {{ (print "v" .Values.serviceMesh.version) | quote }}
        */}}
{{ include "dify.ud.labels" . | indent 8 }}
    spec:
      {{- if .Values.image.webMlamp.pullSecrets }}
      imagePullSecrets:
      {{- range .Values.image.webMlamp.pullSecrets }}
        - name: {{ . }}
      {{- end }}
      {{- end }}
      containers:
      - image: "{{ .Values.image.webMlamp.repository }}:{{ .Values.image.webMlamp.tag }}"
        imagePullPolicy: "{{ .Values.image.webMlamp.pullPolicy }}"
        name: web-mlamp
        env:
        {{- if .Values.webMlamp.extraEnv }}
          {{- toYaml .Values.webMlamp.extraEnv | nindent 8 }}
        {{- end }}
        ports:
          - name: web-mlamp
            containerPort: 3000
            protocol: TCP
        readinessProbe:
          tcpSocket:
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 30
        livenessProbe:
          tcpSocket:
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 30
        resources:
          {{- toYaml .Values.webMlamp.resources | nindent 12 }}
    {{- if and (.Values.nodeSelector) (not .Values.webMlamp.nodeSelector) }}
      nodeSelector:
{{ toYaml .Values.nodeSelector | indent 8 }}
    {{- end }}
    {{- if .Values.webMlamp.nodeSelector }}
      nodeSelector:
{{ toYaml .Values.webMlamp.nodeSelector | indent 8 }}
    {{- end }}
    {{- if and (.Values.affinity) (not .Values.webMlamp.affinity) }}
      affinity:
{{ toYaml .Values.affinity | indent 8 }}
    {{- end }}
    {{- if .Values.webMlamp.affinity }}
      affinity:
{{ toYaml .Values.webMlamp.affinity | indent 8 }}
    {{- end }}
    {{- if and (.Values.tolerations) (not .Values.webMlamp.tolerations) }}
      tolerations:
{{ toYaml .Values.tolerations | indent 8 }}
    {{- end }}
    {{- if .Values.webMlamp.tolerations }}
      tolerations:
{{ toYaml .Values.webMlamp.tolerations | indent 8 }}
    {{- end }}
{{- end }}