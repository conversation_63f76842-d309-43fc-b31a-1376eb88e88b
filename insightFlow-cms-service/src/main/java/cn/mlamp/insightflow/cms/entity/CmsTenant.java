package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("cms_tenant")
public class CmsTenant {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer tenantId;
    private String name;
    @TableField(value = "is_deleted")
    private Integer deleted;
}
