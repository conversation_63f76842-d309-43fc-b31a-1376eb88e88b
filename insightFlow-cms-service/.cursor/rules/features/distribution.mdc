---
globs: distribution/**/*,pom.xml
alwaysApply: false
description: Apache风格assembly打包配置
---

# Apache风格Assembly打包方案

基于Maven Assembly插件实现类似Apache软件的目录结构打包，支持开发调试和生产部署两种模式。

## 目录结构

```
distribution/
├── bin/           # 启动脚本目录
│   └── run.sh     # 统一启动脚本（支持api/worker模式）
├── conf/          # 配置文件目录
│   ├── application.yml           # 主配置文件
│   ├── application-prod.yml      # 生产环境配置
│   ├── application-test.yml      # 测试环境配置
│   ├── logback-spring.xml       # 日志配置
│   └── ...                      # 其他配置文件
├── logs/          # 日志目录（运行时创建）
├── lib/           # 第三方依赖JAR目录
│   ├── commons-*.jar
│   ├── spring-*.jar
│   └── ...        # 所有第三方依赖
├── boot/          # 应用类文件目录
│   └──  spring-boot-application.jar
└── README.txt     # 部署说明文档
```

## Maven配置要求

### POM.xml配置

在 [pom.xml](mdc:pom.xml) 中需要添加以下配置：

1. **Assembly插件配置**：
```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-assembly-plugin</artifactId>
    <configuration>
        <descriptors>
            <descriptor>distribution/assembly.xml</descriptor>
        </descriptors>
        <finalName>${project.artifactId}-${project.version}</finalName>
        <appendAssemblyId>false</appendAssemblyId>
    </configuration>
    <executions>
        <execution>
            <id>make-assembly</id>
            <phase>package</phase>
            <goals>
                <goal>single</goal>
            </goals>
        </execution>
    </executions>
</plugin>
```

2. **移除Spring Boot插件**：
```xml
<!-- 从pom.xml中完全移除spring-boot-maven-plugin -->
```

3. **Assembly Profile配置**：
```xml
<profiles>
    <!-- Assembly打包模式：不包含配置文件 -->
    <profile>
        <id>assembly</id>
        <build>
            <resources>
                <resource>
                    <directory>src/main/resources</directory>
                    <excludes>
                        <exclude>**/*.yml</exclude>
                        <exclude>**/*.yaml</exclude>
                        <exclude>**/*.properties</exclude>
                        <exclude>**/logback*.xml</exclude>
                    </excludes>
                </resource>
            </resources>
        </build>
    </profile>
</profiles>
```

## Assembly描述文件

需要创建 [distribution/assembly.xml](mdc:distribution/assembly.xml) 文件来定义打包结构，包括：

- 复制编译后的class文件和resources到boot/目录
- 复制所有运行时依赖到lib/目录  
- 复制配置文件到conf/目录
- 复制run.sh启动脚本到bin/目录
- 创建logs/目录结构

## 启动脚本示例

### run.sh 统一启动脚本
```bash
#!/bin/bash

# 检查MODE环境变量
if [ -z "$MODE" ]; then
    echo "Error: MODE environment variable is required (api or worker)"
    echo "Usage: MODE=api ./run.sh [additional_java_args...]"
    echo "       MODE=worker ./run.sh [additional_java_args...]"
    exit 1
fi

# 检测JAVA_HOME
if [ -z "$JAVA_HOME" ]; then
    JAVA_CMD=java
else
    JAVA_CMD="$JAVA_HOME/bin/java"
fi

# 应用目录
APP_HOME="$(cd "$(dirname "$0")/.." && pwd)"
CONF_DIR="$APP_HOME/conf"
LIB_DIR="$APP_HOME/lib"
BOOT_DIR="$APP_HOME/boot"
LOG_DIR="$APP_HOME/logs"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 统一的JVM参数（适用于所有模式）
DEFAULT_JVM_OPTS="-XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0 -XX:InitialRAMPercentage=30.0"

# 根据MODE设置主类
case "$MODE" in
    "api")
        MAIN_CLASS="cn.mlamp.insightflow.cms.CmsApplication"
        ;;
    "worker")
        MAIN_CLASS="cn.mlamp.insightflow.cms.CmsWorker"
        ;;
    *)
        echo "Error: Invalid MODE '$MODE'. Must be 'api' or 'worker'"
        exit 1
        ;;
esac

# JVM参数：默认参数 + 环境变量JVM_OPS
JVM_OPTS="$DEFAULT_JVM_OPTS $JVM_OPS"

# 应用参数：基础配置
DEFAULT_APP_OPTS="-Dspring.config.location=$CONF_DIR/ -Dlogging.file.path=$LOG_DIR"

# 根据APP_ENV环境变量设置Spring profiles
if [ -n "$APP_ENV" ]; then
    DEFAULT_APP_OPTS="$DEFAULT_APP_OPTS --spring.profiles.active=$APP_ENV"
fi

# 应用参数：默认参数 + 环境变量APP_OPTS
APP_OPTS="$DEFAULT_APP_OPTS $APP_OPTS"

# 类路径
CLASSPATH="$CONF_DIR:$LIB_DIR/*:$BOOT_DIR/*"

# 启动命令（同步运行，支持追加参数$@）
echo "Starting CMS $MODE mode with class: $MAIN_CLASS"
exec $JAVA_CMD $JVM_OPTS $APP_OPTS -cp "$CLASSPATH" $MAIN_CLASS "$@"
```

## 启动脚本要求

### run.sh 特性
- 通过`MODE`环境变量区分启动模式（api/worker）
- 自动检测JAVA_HOME
- 使用`java -cp`方式启动，不依赖Spring Boot插件
- 类路径包含：`conf:lib/*:boot/*`
- 同步运行，适合Docker entrypoint调用
- 支持`$@`追加Java参数
- 支持环境变量`JVM_OPS`和`APP_OPTS`扩展配置

### 环境变量支持
- `MODE`: 必需，指定启动模式（api/worker）
- `APP_ENV`: 可选，指定Spring环境配置（如prod、test、dev），自动设置--spring.profiles.active
- `JVM_OPS`: 可选，追加JVM参数
- `APP_OPTS`: 可选，追加应用参数
- `JAVA_HOME`: 可选，指定Java安装路径

### 主类映射
- `MODE=api` → `cn.mlamp.insightflow.cms.CmsApplication`
- `MODE=worker` → `cn.mlamp.insightflow.cms.CmsWorker`

## 配置文件管理

### 开发模式（IDEA Debug）
- 配置文件打包在JAR内
- 直接运行主类，无需特殊配置
- 支持热重载和调试

### Assembly模式
- 配置文件独立存放在conf目录
- JAR文件不包含*.yml、*.yaml文件和第三方依赖
- 使用`java -cp conf:lib/*:boot/*`启动
- 通过`-Dspring.config.location=conf/`指定配置路径
- 支持运行时配置修改

## 使用方式

### 开发调试
```bash
# IDEA中直接运行主类
# cn.mlamp.insightflow.cms.CmsApplication
# cn.mlamp.insightflow.cms.CmsWorker

# 或者使用mvn compile后手动运行
mvn compile
java -cp target/classes:$(mvn dependency:build-classpath -Dmdep.outputFile=/dev/stdout -q) cn.mlamp.insightflow.cms.CmsApplication
```

### Assembly打包
```bash
# 使用assembly profile打包
mvn clean package -Passembly

# 打包过程：
# 1. Maven compile编译项目
# 2. Assembly插件按照assembly.xml描述文件打包
# 3. 生成最终分发包
target/insightFlow-cms-service-1.0-SNAPSHOT.tar.gz
```

### 生产部署
```bash
# 解压assembly包
tar -xzf insightFlow-cms-service-1.0-SNAPSHOT.tar.gz
cd insightFlow-cms-service-1.0-SNAPSHOT

# 启动API服务
MODE=api ./bin/run.sh

# 启动Worker服务  
MODE=worker ./bin/run.sh

# 指定环境配置启动（生产环境）
MODE=api APP_ENV=prod ./bin/run.sh

# 指定环境配置启动（测试环境）
MODE=worker APP_ENV=test ./bin/run.sh

# 带自定义JVM参数启动
MODE=api JVM_OPS="-XX:+PrintGC" ./bin/run.sh

# Docker容器中使用（作为entrypoint）
MODE=api APP_ENV=prod ./bin/run.sh

# 追加额外的Java参数
MODE=worker APP_ENV=prod ./bin/run.sh -Dmy.custom.property=value
```

### Docker集成
```dockerfile
# Dockerfile示例
FROM your-base-image
COPY distribution/ /app/
WORKDIR /app

# 设置环境变量
ENV MODE=api
ENV APP_ENV=local

# COPY资源，利用cache加速lib
COPY ./target/lib /app/lib
COPY ./target/conf /app/conf
COPY ./target/bin /app/bin
COPY ./target/boot /app/boot

# 使用run.sh作为entrypoint
ENTRYPOINT ["./bin/run.sh"]
CMD []
```

## 优势

1. **标准化部署**：遵循Apache软件标准目录结构
2. **配置分离**：生产环境配置与代码分离，便于运维管理
3. **依赖分离**：JAR文件轻量化，依赖库独立管理和升级
4. **启动方式简洁**：使用传统`java -cp`方式，完全移除Spring Boot插件
5. **模块化启动**：通过MODE环境变量切换API/Worker模式
6. **开发友好**：IDEA直接运行主类，无需额外配置
7. **Docker友好**：同步运行，完美适配容器化部署
8. **参数灵活**：支持环境变量和命令行参数扩展
9. **内存优化**：容器感知的自适应内存管理，避免OOM
10. **环境隔离**：通过APP_ENV自动配置Spring环境
11. **故障排查**：依赖问题更容易定位和解决
