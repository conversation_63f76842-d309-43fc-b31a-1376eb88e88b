package cn.mlamp.insightflow.cms.enums.dam;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;

@Getter
public enum DamAssetUploadTypeEnum implements IEnum<Integer> {
    SEGMENT(1),
    TOTAL(2);


    @JsonValue
    private final Integer value;

    DamAssetUploadTypeEnum(Integer value) {
        this.value = value;
    }

    @JsonCreator
    public static DamAssetUploadTypeEnum from(Integer value) {
        if (value == null) {
            return null;
        }
        for (DamAssetUploadTypeEnum type : DamAssetUploadTypeEnum.values()) {
            if (Objects.equals(type.getValue(), value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown DamAssetTypeEnum value: " + value);
    }

}
