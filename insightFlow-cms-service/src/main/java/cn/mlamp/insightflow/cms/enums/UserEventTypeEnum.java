package cn.mlamp.insightflow.cms.enums;

import lombok.Getter;

/**
 * @Author: husuper
 * @CreateTime: 2025-07-07
 */
@Getter
public enum UserEventTypeEnum {

    /**
     * 登录
     */
    LOGIN(1, "登录");

    private final Integer code;

    private final String describe;

    UserEventTypeEnum(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public static UserEventTypeEnum getByCode(Integer code) {
        for (UserEventTypeEnum type : UserEventTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
