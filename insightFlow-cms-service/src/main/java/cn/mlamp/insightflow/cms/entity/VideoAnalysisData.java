package cn.mlamp.insightflow.cms.entity;

import cn.mlamp.insightflow.cms.enums.LanguageEnum;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

/**
 * 视频分析数据来源
 * {@link QianchuanMaterialVideo}
 * {@link CmsPullTaskDedupedData}
 *
 * <AUTHOR>
 */
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "classType"
)
@JsonSubTypes({
        @JsonSubTypes.Type(name = CmsPullTaskDedupedData.CLASS_TYPE, value = CmsPullTaskDedupedData.class),
        @JsonSubTypes.Type(name = QianchuanMaterialVideo.CLASS_TYPE, value = QianchuanMaterialVideo.class),
})
public interface VideoAnalysisData {

    /**
     * 仅用于子类反序列化
     */
    String getClassType();

    String getVideoId();

    /**
     * @return 原始 oss id
     */
    String getOssid();

    /**
     * @param ossid ossId
     */
    void setOssid(String ossid);

    /**
     * @return 语言
     */
    LanguageEnum getLanguage();

    /**
     * @return 标题
     */
    String getTitle();

    /**
     * @return 内容
     */
    String getContent();

    /**
     * 设置品牌
     *
     * @param brand 品牌
     */
    void setBrand(String brand);

    /**
     * 设置品牌 - 拼音
     *
     * @param brandSpell 品牌 - 拼音
     */
    void setBrandSpell(String brandSpell);

    void setOurDuration(Integer ourDuration);
}
