{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.rbac.create }}
kind: Role
apiVersion: {{ include "common.capabilities.rbac.apiVersion" . }}
metadata:
  name: {{ include "postgresql.v1.chart.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
# yamllint disable rule:indentation
rules:
  {{- if and (include "common.capabilities.psp.supported" .) .Values.psp.create }}
  - apiGroups:
      - 'policy'
    resources:
      - 'podsecuritypolicies'
    verbs:
      - 'use'
    resourceNames:
      - {{ include "postgresql.v1.chart.fullname" . }}
  {{- end }}
  {{- if .Values.rbac.rules }}
  {{- include "common.tplvalues.render" ( dict "value" .Values.rbac.rules "context" $ ) | nindent 2 }}
  {{- end }}
# yamllint enable rule:indentation
{{- end }}
