package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 视频黄金5秒关联表;
 *
 * <AUTHOR> yangzhibo
 * @date : 2025-5-15
 */
@Data
@TableName("cms_video_three_gold_relation")
public class CmsVideoThreeGoldRelation extends BaseEntity {
    /**
     * 主键 自增id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 黄金5秒id
     */
    private Integer videoThreeGoldId;
    /**
     * 视频ID
     */
    private String videoId;
}
