fullnameOverride: postgresql

auth:
  postgresPassword: "tmvQvuzkQDJjnR7k"
  replicationUsername: "repl"
  replicationPassword: "W4zkoQ4xO0CpGGVH"

architecture: replication

primary:
  extraEnvVars:
    - name: TZ
      value: "Asia/Shanghai"
    - name: PGTZ
      value: "Asia/Shanghai"
  persistence:
    enabled: true
    storageClass: "cbs"
    accessModes:
      - ReadWriteOnce
    size: 50Gi
  configuration: |
    # 连接和认证
    max_connections = 200
    superuser_reserved_connections = 3
    
    listen_addresses = '*'
    
    # 内存设置 (基于 8-16GB 内存)
    shared_buffers = 4GB                    # 约 25% 内存
    effective_cache_size = 12GB             # 约 75% 内存
    work_mem = 32MB                         # 8GB / 300 连接 * 安全系数
    maintenance_work_mem = 1GB              # 用于 VACUUM, CREATE INDEX 等
    autovacuum_work_mem = 1GB              # 自动清理内存
    
    # WAL 设置
    wal_level = replica
    wal_buffers = 64MB                      # 适合高并发
    checkpoint_completion_target = 0.9
    min_wal_size = 2GB
    max_wal_size = 8GB
    checkpoint_timeout = 15min
    
    # 日志和监控
    shared_preload_libraries = 'pg_stat_statements,pg_prewarm'
    log_min_duration_statement = 1000       # 记录超过1秒的查询
    log_checkpoints = on
    log_lock_waits = on
    log_temp_files = 100MB

    # 自动清理优化
    autovacuum = on
    autovacuum_max_workers = 6              # 基于 CPU 核心数
    autovacuum_naptime = 30s
    autovacuum_vacuum_threshold = 50
    autovacuum_analyze_threshold = 50
    autovacuum_vacuum_scale_factor = 0.1
    autovacuum_analyze_scale_factor = 0.05
    
    # 并发和锁
    max_worker_processes = 16               # 2 * CPU 核心数
    max_parallel_workers = 8                # CPU 核心数
    max_parallel_workers_per_gather = 4
    max_parallel_maintenance_workers = 4
    
    # 向量扩展优化
    effective_cache_size = 8GB             # 对向量索引缓存重要
    random_page_cost = 1.0                  # 向量相似性搜索优化
    
    # 其他性能优化
    synchronous_commit = on                 # 生产环境保持数据一致性
    full_page_writes = on                   # 防止部分页写入
    bgwriter_delay = 200ms
    bgwriter_lru_maxpages = 500
    bgwriter_lru_multiplier = 2.0
    
    # 时区
    timezone = 'Asia/Shanghai'
    log_timezone = 'Asia/Shanghai'
  resources:
    requests:
      cpu: 4
      memory: 8Gi
    limits:
      cpu: 8
      memory: 16Gi

readReplicas:
  replicaCount: 1
  extraEnvVars:
    - name: TZ
      value: "Asia/Shanghai"
    - name: PGTZ
      value: "Asia/Shanghai"
  persistence:
    enabled: true
    storageClass: "cbs"
    accessModes:
      - ReadWriteOnce
    size: 50Gi
  extendedConfiguration: |
    max_connections = 200
    superuser_reserved_connections = 3
    
    # 并发和锁
    max_worker_processes = 16               # 2 * CPU 核心数
    max_parallel_workers = 8                # CPU 核心数
    max_parallel_workers_per_gather = 4
    max_parallel_maintenance_workers = 4
    
    # 从库特定优化
    hot_standby = on
    hot_standby_feedback = on
    
    timezone = 'Asia/Shanghai'
    log_timezone = 'Asia/Shanghai'
  resources:
    requests:
      cpu: 2
      memory: 4Gi
    limits:
      cpu: 4
      memory: 8Gi

backup:
  enabled: false