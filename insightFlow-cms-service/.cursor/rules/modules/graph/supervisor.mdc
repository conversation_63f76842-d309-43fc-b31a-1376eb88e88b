---
description:
globs: src/main/java/cn/mlamp/insightflow/cms/graph/nodes/SupervisorNode.java
alwaysApply: false
---
# SupervisorNode 协调节点详细说明

## 概述
[SupervisorNode](mdc:src/main/java/cn/mlamp/insightflow/cms/graph/nodes/SupervisorNode.java) 是整个Graph系统的核心协调节点，负责接受用户请求，智能分析用户意图，并将任务分发到对应的专业节点执行。支持智能参数补全协调，当Worker节点检测到参数缺失时自动触发参数收集流程。

## 核心功能

### 1. 智能任务分发
- 使用大模型分析用户查询意图
- 通过 `dispatch_task` 工具函数选择合适的worker节点
- 支持的worker节点：
  - `FeaturedVideosNode`: 热门视频搜索推荐
  - `VideoAnalysisNode`: 视频分析处理
  - `AIScriptNode`: AI脚本生成
  - `VideoGenerateNode`: 视频生成
  - `ThreeSecondsNode`: 三秒钟黄金时段分析
  - `ParameterExtractorNode`: 参数抽取和补全

### 2. 参数补全协调
- **自动检测**：识别Worker节点提交的参数缺失请求
- **智能委托**：委托 [ParameterExtractorNode](mdc:.cursor/rules/modules/graph/parameter_extractor.mdc) 进行参数补全
- **状态管理**：管理参数收集状态和流程转换
- **用户友好**：支持用户随时跳出参数补全流程
- **类型安全**：使用强类型 `ParameterMissingRequest` 类替代JSON字符串解析

### 3. 会话状态管理
- 维护对话历史记录
- 管理supervisor专用消息队列
- 处理工具执行结果的上下文传递

### 4. 流式响应处理
- 支持SSE流式输出
- 实时反馈任务执行状态
- 通过 `SupervisorStreamingChatResponseHandler` 处理流式响应

## 参数补全机制

### 1. 参数缺失请求模型

Worker节点通过 [ParameterMissingRequest](mdc:src/main/java/cn/mlamp/insightflow/cms/graph/context/ParameterMissingRequest.java) 类提交参数缺失请求：

```java
public class ParameterMissingRequest {
    private String targetNode;                    // 目标节点名称
    private String originalGoal;                  // 原始任务目标
    private List<ParameterSpec> missingParams;    // 缺失的参数规格
    private Map<String, Object> existingParams;  // 已存在的参数
    private LocalDateTime createTime;             // 请求创建时间
    private String errorMessage;                  // 错误消息（可选）
}
```

#### 参数规格定义
```java
public static class ParameterSpec {
    private String name;              // 参数名称
    private String displayName;       // 显示名称
    private String description;       // 参数描述
    private ParameterType type;       // 参数类型
    private boolean required;         // 是否必填
    private Object defaultValue;      // 默认值
    private String pattern;           // 验证正则表达式
    private Range range;              // 数值范围
    private List<String> enumValues;  // 枚举值列表
    private String example;           // 参数示例
    private String hint;              // 参数提示信息
}
```

### 2. Worker节点集成方式

Worker节点支持两种参数缺失请求提交方式：

#### 方式一：直接提交（基础方式）
Worker节点通过继承 [BaseAgentNode](mdc:src/main/java/cn/mlamp/insightflow/cms/graph/nodes/BaseAgentNode.java) 获得参数管理能力：

#### 便捷方法
```java
// 提交参数缺失请求
protected Map<String, Object> requestMissingParameters(
    CMSGraphState state,
    String originalGoal,
    List<ParameterMissingRequest.ParameterSpec> missingParams) {
    // 内部实现...
}

// 创建参数规格
protected ParameterMissingRequest.ParameterSpec createParameterSpec(
    String name, String displayName, String description,
    ParameterMissingRequest.ParameterType type) {
    // 内部实现...
}

// 获取完整参数（来自参数补全流程）
protected Optional<Map<String, Object>> getCompleteParams(CMSGraphState state) {
    // 内部实现...
}
```

#### Worker节点实现示例
```java
@Override
protected Map<String, Object> run(String runId, String query, CMSGraphState state, CallbackManager callbackManager) throws Exception {
    
    // 1. 检查是否有完整参数（来自参数补全流程）
    Optional<Map<String, Object>> completeParams = getCompleteParams(state);
    if (completeParams.isPresent()) {
        return executeWithParams(completeParams.get());
    }
    
    // 2. 检测参数缺失并提交请求
    List<ParameterMissingRequest.ParameterSpec> missingParams = checkMissingParams(query);
    if (!missingParams.isEmpty()) {
        return requestMissingParameters(state, "执行任务", missingParams);
    }
    
    // 3. 正常执行任务
    return executeTask();
}

private List<ParameterMissingRequest.ParameterSpec> checkMissingParams(String query) {
    List<ParameterMissingRequest.ParameterSpec> missing = new ArrayList<>();
    
    if (needsKeyword(query)) {
        missing.add(createParameterSpec(
            "keyword", "搜索关键词", "用于搜索的关键词",
            ParameterMissingRequest.ParameterType.STRING, true,
            null, "美妆", "请输入具体的搜索关键词"
        ));
    }
    
    return missing;
}
```

#### 方式二：Agent响应字段（推荐）
Agent节点在Response中设置 `ParameterMissingRequest` 字段提交请求，具体实现请参考 [Tools规则文档](mdc:.cursor/rules/modules/graph/tools.mdc)：

```java
@Data
public static class AgentResponse {
    private String result;
    private String error;
    
    @Description("参数缺失请求，当工具调用发现缺失必需参数时使用")
    private ParameterMissingRequest parameterMissingRequest;
    
    public boolean hasParameterMissingRequest() {
        return parameterMissingRequest != null;
    }
}

// Agent节点处理逻辑
@Override
protected Map<String, Object> run(String runId, String goal, CMSGraphState state, CallbackManager callbackManager) throws Exception {
    
    // 1. 检查完整参数（来自参数补全流程）
    Optional<Map<String, Object>> completeParams = getCompleteParams(state);
    if (completeParams.isPresent()) {
        return executeWithCompleteParams(goal, completeParams.get(), state, callbackManager);
    }
    
    // 2. 执行Agent
    final Result<AgentResponse> agentResult = chat(goal, context.toString(), callbackManager);
    final AgentResponse response = agentResult.content();
    
    // 3. 检查Agent响应中的参数缺失请求
    if (response.hasParameterMissingRequest()) {
        return handleParameterMissingRequest(response.getParameterMissingRequest(), state);
    }
    
    // 4. 正常处理响应...
}
```

### 3. SupervisorNode处理流程

#### 核心执行逻辑
```java
@Override
public Map<String, Object> run(String runId, String query, CMSGraphState state, CallbackManager callbackManager) throws Exception {
    
    // 检查是否直接返回
    if (state.isDirectReturn()) {
        return handleDirectReturn(state, callbackManager);
    }
    
    // 检查是否有参数缺失请求需要处理
    if (state.hasParameterMissingRequest()) {
        return handleParameterMissingRequest(state);
    }
    
    // 检查是否在参数补全流程中
    if (state.isInParameterCompletion()) {
        return handleParameterCompletionFlow(query, state, callbackManager);
    }
    
    // 正常的任务分发流程
    return handleTaskDispatch(state, callbackManager);
}
```

#### 参数缺失请求处理
```java
private Map<String, Object> handleParameterMissingRequest(CMSGraphState state) {
    log.info("处理参数缺失请求");
    
    ParameterMissingRequest request = state.getParameterMissingRequest()
        .orElseThrow(() -> new IllegalStateException("参数缺失请求不存在"));
    
    // 转换为参数补全上下文所需的格式
    List<ParameterCompletionContext.ParameterSpec> missingParams = 
        convertToParameterCompletionSpecs(request.getMissingParams());
    
    // 创建参数补全上下文
    ParameterCompletionContext completionContext = ParameterCompletionContext.create(
        request.getTargetNode(),
        request.getOriginalGoal(),
        missingParams,
        request.getExistingParams()
    );
    
    // 委托给ParameterExtractorNode
    Map<String, Object> result = new HashMap<>();
    result.put(CMSGraphState.PARAMETER_MISSING_REQUEST_KEY, null);
    result.put(CMSGraphState.PARAMETER_COMPLETION_CONTEXT_KEY, completionContext);
    result.put(CMSGraphState.NEXT_KEY, ParameterExtractorNode.NAME);
    result.put(CMSGraphState.GOAL_KEY, "正在为您收集必要参数...");
    
    return result;
}
```

### 4. 参数补全完成处理

当ParameterExtractorNode完成参数收集后，SupervisorNode处理返回结果：

```java
private Map<String, Object> handleParameterCompletionResult(
    Map<String, Object> responseData, CMSGraphState state) {
    
    ParameterCompletionContext context = state.getParameterCompletionContext()
        .orElseThrow(() -> new IllegalStateException("参数补全上下文不存在"));
        
    String targetNode = context.getTargetNode();
    String originalGoal = context.getOriginalGoal();
    Map<String, Object> completeParams = context.getAllParams();

    // 将完整参数传递给目标worker节点
    Map<String, Object> result = new HashMap<>();
    result.put(CMSGraphState.NEXT_KEY, targetNode);
    result.put(CMSGraphState.GOAL_KEY, originalGoal);
    result.put(CMSGraphState.PARAMETER_COMPLETION_CONTEXT_KEY, null);
    
    // 将完整参数存储到metadata中，供目标节点使用
    Map<String, Object> metadata = new HashMap<>(state.getMetadata().orElse(new HashMap<>()));
    metadata.put("completeParams", completeParams);
    result.put(CMSGraphState.METADATA_KEY, metadata);
    
    return result;
}
```

## 状态管理

### CMSGraphState 增强功能

基于 [CMSGraphState](mdc:src/main/java/cn/mlamp/insightflow/cms/graph/CMSGraphState.java) 的状态管理，新增：

- `PARAMETER_MISSING_REQUEST_KEY`: 参数缺失请求
- `hasParameterMissingRequest()`: 检查是否存在参数缺失请求
- `getParameterMissingRequest()`: 获取参数缺失请求
- `isInParameterCompletion()`: 检查是否在参数补全流程中

### 支持的参数类型

```java
public enum ParameterType {
    STRING,         // 文本类型
    INTEGER,        // 整数类型
    DECIMAL,        // 小数类型
    BOOLEAN,        // 布尔类型
    DATE,           // 日期类型
    DATE_RANGE,     // 日期范围
    ENUM,           // 枚举类型
    ARRAY,          // 数组类型
    OBJECT,         // 对象类型
    FILE_ID,        // 文件ID
    URL,            // URL链接
    EMAIL,          // 邮箱地址
    PHONE,          // 电话号码
    JSON            // JSON对象
}
```

## 用户交互流程

### 1. 自动参数收集流程
1. **用户请求** → SupervisorNode分析意图
2. **任务分发** → 委托给相应Worker节点
3. **参数检测** → Worker节点检测参数缺失
4. **请求提交** → Worker节点提交ParameterMissingRequest
5. **流程委托** → SupervisorNode委托给ParameterExtractorNode
6. **参数收集** → 通过对话收集缺失参数
7. **任务恢复** → 参数收集完成后回到原Worker节点执行

### 2. 用户取消机制
- 检测取消关键词：`取消`、`不用了`、`算了`、`重新开始`、`换个话题`
- 识别新任务请求：检测用户是否提出了新的独立任务
- 智能判断：区分取消意图和正常参数输入

### 3. 错误处理与恢复
- 参数验证失败时提供清晰的错误提示
- 支持参数重新输入和修正
- 异常情况下的优雅降级处理

## 技术实现要点

### 1. 类型安全设计
- 使用强类型 `ParameterMissingRequest` 类
- 避免JSON字符串解析错误
- 编译时类型检查保证安全性

### 2. 状态转换清晰
- 明确的状态标识和转换逻辑
- 通过 CMSGraphState 统一管理状态
- 支持状态的持久化和恢复

### 3. 扩展性设计
- Worker节点易于集成参数管理功能
- 支持新的参数类型和验证规则
- 可配置的参数收集策略

### 4. 用户体验优化
- 智能参数提取和验证
- 友好的错误提示和引导
- 支持批量和单个参数输入

## 最佳实践

### 1. Worker节点开发
- 继承 BaseAgentNode 获得参数管理能力
- 优先检查 `getCompleteParams()` 获取完整参数
- 使用 `createParameterSpec()` 创建标准化参数规格
- 提供清晰的参数描述和示例

### 2. 参数设计原则
- 只收集必要的参数，避免过度复杂化
- 提供合理的默认值和枚举选项
- 使用正确的参数类型和验证规则
- 编写用户友好的描述和提示信息

### 3. 错误处理
- 提供具体的错误原因和修正建议
- 支持参数的重新输入和修正
- 在验证失败时给出示例和格式要求

## 集成示例

完整的Worker节点集成示例：

```java
@Slf4j
@Component
public class VideoSearchNode extends BaseAgentNode {

    public static final String NAME = "videoSearch";

    @Override
    public String getNodeName() {
        return NAME;
    }

    @Override
    protected Map<String, Object> run(String runId, String query, CMSGraphState state, CallbackManager callbackManager) throws Exception {
        
        // 1. 检查完整参数
        Optional<Map<String, Object>> completeParams = getCompleteParams(state);
        if (completeParams.isPresent()) {
            return executeVideoSearch(completeParams.get(), callbackManager);
        }

        // 2. 检测参数缺失
        List<ParameterMissingRequest.ParameterSpec> missingParams = detectMissingParams(query);
        if (!missingParams.isEmpty()) {
            return requestMissingParameters(state, "搜索视频内容", missingParams);
        }

        // 3. 执行搜索
        Map<String, Object> parsedParams = parseUserQuery(query);
        return executeVideoSearch(parsedParams, callbackManager);
    }

    private List<ParameterMissingRequest.ParameterSpec> detectMissingParams(String query) {
        List<ParameterMissingRequest.ParameterSpec> missing = new ArrayList<>();

        if (!containsKeyword(query)) {
            missing.add(createParameterSpec(
                "keyword", "搜索关键词", "您想要搜索的视频内容关键词",
                ParameterMissingRequest.ParameterType.STRING, true,
                null, "美妆、护肤、化妆教程", "请输入具体的搜索关键词"
            ));
        }

        if (!containsIndustry(query)) {
            missing.add(createParameterSpec(
                "industry", "行业分类", "视频内容所属的行业类别",
                ParameterMissingRequest.ParameterType.ENUM, true,
                List.of("美妆", "服装", "数码", "食品", "教育", "娱乐"),
                "美妆", "请选择最符合您需求的行业分类"
            ));
        }

        return missing;
    }
}
```

这种设计确保了参数补全功能的类型安全、易用性和可扩展性，同时提供了优秀的用户体验。