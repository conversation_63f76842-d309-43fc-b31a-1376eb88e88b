package cn.mlamp.insightflow.cms.graph.events;

import cn.mlamp.insightflow.cms.graph.entities.EnvInfo;
import lombok.Data;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * Agent Graph消息事件 用于传递节点执行过程中的消息信息
 */
@Getter
public class AgentGraphToolFinishedEvent extends AbstractAgentGraphEvent<AgentGraphToolFinishedEvent.EventData> {

    private final EventType event = EventType.TOOL_FINISHED;

    private final String runId;

    private final String name;

    private final String label;

    private final EnvInfo env;

    private final String input;

    private final String output;

    private final Map<String, Object> metadata;

    public AgentGraphToolFinishedEvent(
            String conversationId,
            String messageId,
            String runId,
            String name,
            String label,
            EnvInfo env,
            String input,
            String output
    ) {
        this(conversationId, messageId, runId, name, label, env, input, output, new HashMap<>());
    }

    public AgentGraphToolFinishedEvent(
            String conversationId,
            String messageId,
            String runId,
            String name,
            String label,
            EnvInfo env,
            String input,
            String output,
            Map<String, Object> metadata
    ) {
        super(conversationId, messageId);
        this.runId = runId;
        this.name = name;
        this.label = label;
        this.env = env;
        this.input = input;
        this.output = output;
        this.metadata = new HashMap<>(metadata);
    }

    @Override
    protected AgentGraphToolFinishedEvent.EventData getEventData() {
        final EventData eventData = new EventData();
        eventData.setName(name);
        eventData.setLabel(label);
        eventData.setEnv(env);
        eventData.setInput(input);
        eventData.setOutput(output);
        eventData.setMetadata(metadata);
        return eventData;
    }

    @Data
    public static class EventData {

        private String name;

        private String label;

        private EnvInfo env;

        private String input;

        private String output;

        private Map<String, Object> metadata;

    }

}
